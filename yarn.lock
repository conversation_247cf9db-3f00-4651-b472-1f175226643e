# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@babel/code-frame@^7.0.0":
  "integrity" "sha512-LYvhNKfwWSPpocw8GI7gpK2nq3HSDuEPC/uSYaALSJu9xjsalaaYFOq0Pwt5KmVqwEbZlDu81aLXwBOmD/Fv9g=="
  "resolved" "https://registry.npmmirror.com/@babel/code-frame/-/code-frame-7.21.4.tgz"
  "version" "7.21.4"
  dependencies:
    "@babel/highlight" "^7.18.6"

"@babel/helper-validator-identifier@^7.18.6":
  "integrity" "sha512-awrNfaMtnHUr653GgGEs++LlAvW6w+DcPrOliSMXWCKo597CwL5Acf/wWdNkf/tfEQE3mjkeD1YOVZOUV/od1w=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-validator-identifier/-/helper-validator-identifier-7.19.1.tgz"
  "version" "7.19.1"

"@babel/highlight@^7.18.6":
  "integrity" "sha512-u7stbOuYjaPezCuLj29hNW1v64M2Md2qupEKP1fHc7WdOA3DgLh37suiSrZYY7haUB7iBeQZ9P1uiRF359do3g=="
  "resolved" "https://registry.npmmirror.com/@babel/highlight/-/highlight-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-validator-identifier" "^7.18.6"
    "chalk" "^2.0.0"
    "js-tokens" "^4.0.0"

"@colors/colors@1.5.0":
  "version" "1.5.0"

"@commitlint/cli@^17.6.3":
  "integrity" "sha512-ItSz2fd4F+CujgIbQOfNNerDF1eFlsBGEfp9QcCb1kxTYMuKTYZzA6Nu1YRRrIaaWwe2E7awUGpIMrPoZkOG3A=="
  "resolved" "https://registry.npmmirror.com/@commitlint/cli/-/cli-17.6.3.tgz"
  "version" "17.6.3"
  dependencies:
    "@commitlint/format" "^17.4.4"
    "@commitlint/lint" "^17.6.3"
    "@commitlint/load" "^17.5.0"
    "@commitlint/read" "^17.5.1"
    "@commitlint/types" "^17.4.4"
    "execa" "^5.0.0"
    "lodash.isfunction" "^3.0.9"
    "resolve-from" "5.0.0"
    "resolve-global" "1.0.0"
    "yargs" "^17.0.0"

"@commitlint/config-validator@^17.4.4":
  "integrity" "sha512-bi0+TstqMiqoBAQDvdEP4AFh0GaKyLFlPPEObgI29utoKEYoPQTvF0EYqIwYYLEoJYhj5GfMIhPHJkTJhagfeg=="
  "resolved" "https://registry.npmmirror.com/@commitlint/config-validator/-/config-validator-17.4.4.tgz"
  "version" "17.4.4"
  dependencies:
    "@commitlint/types" "^17.4.4"
    "ajv" "^8.11.0"

"@commitlint/ensure@^17.4.4":
  "integrity" "sha512-AHsFCNh8hbhJiuZ2qHv/m59W/GRE9UeOXbkOqxYMNNg9pJ7qELnFcwj5oYpa6vzTSHtPGKf3C2yUFNy1GGHq6g=="
  "resolved" "https://registry.npmmirror.com/@commitlint/ensure/-/ensure-17.4.4.tgz"
  "version" "17.4.4"
  dependencies:
    "@commitlint/types" "^17.4.4"
    "lodash.camelcase" "^4.3.0"
    "lodash.kebabcase" "^4.1.1"
    "lodash.snakecase" "^4.1.1"
    "lodash.startcase" "^4.4.0"
    "lodash.upperfirst" "^4.3.1"

"@commitlint/execute-rule@^17.4.0":
  "integrity" "sha512-LIgYXuCSO5Gvtc0t9bebAMSwd68ewzmqLypqI2Kke1rqOqqDbMpYcYfoPfFlv9eyLIh4jocHWwCK5FS7z9icUA=="
  "resolved" "https://registry.npmmirror.com/@commitlint/execute-rule/-/execute-rule-17.4.0.tgz"
  "version" "17.4.0"

"@commitlint/format@^17.4.4":
  "integrity" "sha512-+IS7vpC4Gd/x+uyQPTAt3hXs5NxnkqAZ3aqrHd5Bx/R9skyCAWusNlNbw3InDbAK6j166D9asQM8fnmYIa+CXQ=="
  "resolved" "https://registry.npmmirror.com/@commitlint/format/-/format-17.4.4.tgz"
  "version" "17.4.4"
  dependencies:
    "@commitlint/types" "^17.4.4"
    "chalk" "^4.1.0"

"@commitlint/is-ignored@^17.6.3":
  "integrity" "sha512-LQbNdnPbxrpbcrVKR5yf51SvquqktpyZJwqXx3lUMF6+nT9PHB8xn3wLy8pi2EQv5Zwba484JnUwDE1ygVYNQA=="
  "resolved" "https://registry.npmmirror.com/@commitlint/is-ignored/-/is-ignored-17.6.3.tgz"
  "version" "17.6.3"
  dependencies:
    "@commitlint/types" "^17.4.4"
    "semver" "7.5.0"

"@commitlint/lint@^17.6.3":
  "integrity" "sha512-fBlXwt6SHJFgm3Tz+luuo3DkydAx9HNC5y4eBqcKuDuMVqHd2ugMNr+bQtx6riv9mXFiPoKp7nE4Xn/ls3iVDA=="
  "resolved" "https://registry.npmmirror.com/@commitlint/lint/-/lint-17.6.3.tgz"
  "version" "17.6.3"
  dependencies:
    "@commitlint/is-ignored" "^17.6.3"
    "@commitlint/parse" "^17.4.4"
    "@commitlint/rules" "^17.6.1"
    "@commitlint/types" "^17.4.4"

"@commitlint/load@^17.5.0":
  "integrity" "sha512-l+4W8Sx4CD5rYFsrhHH8HP01/8jEP7kKf33Xlx2Uk2out/UKoKPYMOIRcDH5ppT8UXLMV+x6Wm5osdRKKgaD1Q=="
  "resolved" "https://registry.npmmirror.com/@commitlint/load/-/load-17.5.0.tgz"
  "version" "17.5.0"
  dependencies:
    "@commitlint/config-validator" "^17.4.4"
    "@commitlint/execute-rule" "^17.4.0"
    "@commitlint/resolve-extends" "^17.4.4"
    "@commitlint/types" "^17.4.4"
    "@types/node" "*"
    "chalk" "^4.1.0"
    "cosmiconfig" "^8.0.0"
    "cosmiconfig-typescript-loader" "^4.0.0"
    "lodash.isplainobject" "^4.0.6"
    "lodash.merge" "^4.6.2"
    "lodash.uniq" "^4.5.0"
    "resolve-from" "^5.0.0"
    "ts-node" "^10.8.1"
    "typescript" "^4.6.4 || ^5.0.0"

"@commitlint/message@^17.4.2":
  "integrity" "sha512-3XMNbzB+3bhKA1hSAWPCQA3lNxR4zaeQAQcHj0Hx5sVdO6ryXtgUBGGv+1ZCLMgAPRixuc6en+iNAzZ4NzAa8Q=="
  "resolved" "https://registry.npmmirror.com/@commitlint/message/-/message-17.4.2.tgz"
  "version" "17.4.2"

"@commitlint/parse@^17.4.4":
  "integrity" "sha512-EKzz4f49d3/OU0Fplog7nwz/lAfXMaDxtriidyGF9PtR+SRbgv4FhsfF310tKxs6EPj8Y+aWWuX3beN5s+yqGg=="
  "resolved" "https://registry.npmmirror.com/@commitlint/parse/-/parse-17.4.4.tgz"
  "version" "17.4.4"
  dependencies:
    "@commitlint/types" "^17.4.4"
    "conventional-changelog-angular" "^5.0.11"
    "conventional-commits-parser" "^3.2.2"

"@commitlint/read@^17.5.1":
  "integrity" "sha512-7IhfvEvB//p9aYW09YVclHbdf1u7g7QhxeYW9ZHSO8Huzp8Rz7m05aCO1mFG7G8M+7yfFnXB5xOmG18brqQIBg=="
  "resolved" "https://registry.npmmirror.com/@commitlint/read/-/read-17.5.1.tgz"
  "version" "17.5.1"
  dependencies:
    "@commitlint/top-level" "^17.4.0"
    "@commitlint/types" "^17.4.4"
    "fs-extra" "^11.0.0"
    "git-raw-commits" "^2.0.11"
    "minimist" "^1.2.6"

"@commitlint/resolve-extends@^17.4.4":
  "integrity" "sha512-znXr1S0Rr8adInptHw0JeLgumS11lWbk5xAWFVno+HUFVN45875kUtqjrI6AppmD3JI+4s0uZlqqlkepjJd99A=="
  "resolved" "https://registry.npmmirror.com/@commitlint/resolve-extends/-/resolve-extends-17.4.4.tgz"
  "version" "17.4.4"
  dependencies:
    "@commitlint/config-validator" "^17.4.4"
    "@commitlint/types" "^17.4.4"
    "import-fresh" "^3.0.0"
    "lodash.mergewith" "^4.6.2"
    "resolve-from" "^5.0.0"
    "resolve-global" "^1.0.0"

"@commitlint/rules@^17.6.1":
  "integrity" "sha512-lUdHw6lYQ1RywExXDdLOKxhpp6857/4c95Dc/1BikrHgdysVUXz26yV0vp1GL7Gv+avx9WqZWTIVB7pNouxlfw=="
  "resolved" "https://registry.npmmirror.com/@commitlint/rules/-/rules-17.6.1.tgz"
  "version" "17.6.1"
  dependencies:
    "@commitlint/ensure" "^17.4.4"
    "@commitlint/message" "^17.4.2"
    "@commitlint/to-lines" "^17.4.0"
    "@commitlint/types" "^17.4.4"
    "execa" "^5.0.0"

"@commitlint/to-lines@^17.4.0":
  "integrity" "sha512-LcIy/6ZZolsfwDUWfN1mJ+co09soSuNASfKEU5sCmgFCvX5iHwRYLiIuoqXzOVDYOy7E7IcHilr/KS0e5T+0Hg=="
  "resolved" "https://registry.npmmirror.com/@commitlint/to-lines/-/to-lines-17.4.0.tgz"
  "version" "17.4.0"

"@commitlint/top-level@^17.4.0":
  "integrity" "sha512-/1loE/g+dTTQgHnjoCy0AexKAEFyHsR2zRB4NWrZ6lZSMIxAhBJnmCqwao7b4H8888PsfoTBCLBYIw8vGnej8g=="
  "resolved" "https://registry.npmmirror.com/@commitlint/top-level/-/top-level-17.4.0.tgz"
  "version" "17.4.0"
  dependencies:
    "find-up" "^5.0.0"

"@commitlint/types@^17.4.4":
  "integrity" "sha512-amRN8tRLYOsxRr6mTnGGGvB5EmW/4DDjLMgiwK3CCVEmN6Sr/6xePGEpWaspKkckILuUORCwe6VfDBw6uj4axQ=="
  "resolved" "https://registry.npmmirror.com/@commitlint/types/-/types-17.4.4.tgz"
  "version" "17.4.4"
  dependencies:
    "chalk" "^4.1.0"

"@cspotcode/source-map-support@^0.8.0":
  "integrity" "sha512-IchNf6dN4tHoMFIn/7OE8LWZ19Y6q/67Bmf6vnGREv8RSbBVb9LPJxEcnwrcwX6ixSvaiGoomAUvu4YSxXrVgw=="
  "resolved" "https://registry.npmmirror.com/@cspotcode/source-map-support/-/source-map-support-0.8.1.tgz"
  "version" "0.8.1"
  dependencies:
    "@jridgewell/trace-mapping" "0.3.9"

"@gar/promisify@^1.1.3":
  "version" "1.1.3"

"@isaacs/string-locale-compare@^1.1.0":
  "version" "1.1.0"

"@jridgewell/resolve-uri@^3.0.3":
  "integrity" "sha512-dSYZh7HhCDtCKm4QakX0xFpsRDqjjtZf/kjI/v3T3Nwt5r8/qz/M19F9ySyOqU94SXBmeG9ttTul+YnR4LOxFA=="
  "resolved" "https://registry.npmmirror.com/@jridgewell/resolve-uri/-/resolve-uri-3.1.1.tgz"
  "version" "3.1.1"

"@jridgewell/sourcemap-codec@^1.4.10":
  "integrity" "sha512-eF2rxCRulEKXHTRiDrDy6erMYWqNw4LPdQ8UQA4huuxaQsVeRPFl2oM8oDGxMFhJUWZf9McpLtJasDDZb/Bpeg=="
  "resolved" "https://registry.npmmirror.com/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.15.tgz"
  "version" "1.4.15"

"@jridgewell/trace-mapping@0.3.9":
  "integrity" "sha512-3Belt6tdc8bPgAtbcmdtNJlirVoTmEb5e2gC94PnkwEW9jI6CAHUeoG85tjWP5WquqfavoMtMwiG4P926ZKKuQ=="
  "resolved" "https://registry.npmmirror.com/@jridgewell/trace-mapping/-/trace-mapping-0.3.9.tgz"
  "version" "0.3.9"
  dependencies:
    "@jridgewell/resolve-uri" "^3.0.3"
    "@jridgewell/sourcemap-codec" "^1.4.10"

"@npmcli/arborist@^5.6.3":
  "version" "5.6.3"
  dependencies:
    "@isaacs/string-locale-compare" "^1.1.0"
    "@npmcli/installed-package-contents" "^1.0.7"
    "@npmcli/map-workspaces" "^2.0.3"
    "@npmcli/metavuln-calculator" "^3.0.1"
    "@npmcli/move-file" "^2.0.0"
    "@npmcli/name-from-folder" "^1.0.1"
    "@npmcli/node-gyp" "^2.0.0"
    "@npmcli/package-json" "^2.0.0"
    "@npmcli/query" "^1.2.0"
    "@npmcli/run-script" "^4.1.3"
    "bin-links" "^3.0.3"
    "cacache" "^16.1.3"
    "common-ancestor-path" "^1.0.1"
    "hosted-git-info" "^5.2.1"
    "json-parse-even-better-errors" "^2.3.1"
    "json-stringify-nice" "^1.1.4"
    "minimatch" "^5.1.0"
    "mkdirp" "^1.0.4"
    "mkdirp-infer-owner" "^2.0.0"
    "nopt" "^6.0.0"
    "npm-install-checks" "^5.0.0"
    "npm-package-arg" "^9.0.0"
    "npm-pick-manifest" "^7.0.2"
    "npm-registry-fetch" "^13.0.0"
    "npmlog" "^6.0.2"
    "pacote" "^13.6.1"
    "parse-conflict-json" "^2.0.1"
    "proc-log" "^2.0.0"
    "promise-all-reject-late" "^1.0.0"
    "promise-call-limit" "^1.0.1"
    "read-package-json-fast" "^2.0.2"
    "readdir-scoped-modules" "^1.1.0"
    "rimraf" "^3.0.2"
    "semver" "^7.3.7"
    "ssri" "^9.0.0"
    "treeverse" "^2.0.0"
    "walk-up-path" "^1.0.0"

"@npmcli/ci-detect@^2.0.0":
  "version" "2.0.0"

"@npmcli/config@^4.2.1":
  "version" "4.2.2"
  dependencies:
    "@npmcli/map-workspaces" "^2.0.2"
    "ini" "^3.0.0"
    "mkdirp-infer-owner" "^2.0.0"
    "nopt" "^6.0.0"
    "proc-log" "^2.0.0"
    "read-package-json-fast" "^2.0.3"
    "semver" "^7.3.5"
    "walk-up-path" "^1.0.0"

"@npmcli/disparity-colors@^2.0.0":
  "version" "2.0.0"
  dependencies:
    "ansi-styles" "^4.3.0"

"@npmcli/fs@^2.1.0", "@npmcli/fs@^2.1.1":
  "version" "2.1.2"
  dependencies:
    "@gar/promisify" "^1.1.3"
    "semver" "^7.3.5"

"@npmcli/git@^3.0.0":
  "version" "3.0.2"
  dependencies:
    "@npmcli/promise-spawn" "^3.0.0"
    "lru-cache" "^7.4.4"
    "mkdirp" "^1.0.4"
    "npm-pick-manifest" "^7.0.0"
    "proc-log" "^2.0.0"
    "promise-inflight" "^1.0.1"
    "promise-retry" "^2.0.1"
    "semver" "^7.3.5"
    "which" "^2.0.2"

"@npmcli/installed-package-contents@^1.0.7":
  "version" "1.0.7"
  dependencies:
    "npm-bundled" "^1.1.1"
    "npm-normalize-package-bin" "^1.0.1"

"@npmcli/map-workspaces@^2.0.2", "@npmcli/map-workspaces@^2.0.3":
  "version" "2.0.4"
  dependencies:
    "@npmcli/name-from-folder" "^1.0.1"
    "glob" "^8.0.1"
    "minimatch" "^5.0.1"
    "read-package-json-fast" "^2.0.3"

"@npmcli/metavuln-calculator@^3.0.1":
  "version" "3.1.1"
  dependencies:
    "cacache" "^16.0.0"
    "json-parse-even-better-errors" "^2.3.1"
    "pacote" "^13.0.3"
    "semver" "^7.3.5"

"@npmcli/move-file@^2.0.0":
  "version" "2.0.1"
  dependencies:
    "mkdirp" "^1.0.4"
    "rimraf" "^3.0.2"

"@npmcli/name-from-folder@^1.0.1":
  "version" "1.0.1"

"@npmcli/node-gyp@^2.0.0":
  "version" "2.0.0"

"@npmcli/package-json@^2.0.0":
  "version" "2.0.0"
  dependencies:
    "json-parse-even-better-errors" "^2.3.1"

"@npmcli/promise-spawn@^3.0.0":
  "version" "3.0.0"
  dependencies:
    "infer-owner" "^1.0.4"

"@npmcli/query@^1.2.0":
  "version" "1.2.0"
  dependencies:
    "npm-package-arg" "^9.1.0"
    "postcss-selector-parser" "^6.0.10"
    "semver" "^7.3.7"

"@npmcli/run-script@^4.1.0", "@npmcli/run-script@^4.1.3", "@npmcli/run-script@^4.2.0", "@npmcli/run-script@^4.2.1":
  "version" "4.2.1"
  dependencies:
    "@npmcli/node-gyp" "^2.0.0"
    "@npmcli/promise-spawn" "^3.0.0"
    "node-gyp" "^9.0.0"
    "read-package-json-fast" "^2.0.3"
    "which" "^2.0.2"

"@tetris/commitlint-config@^1.0.0":
  "integrity" "sha512-MkiljobggpPyl8YfEe1br4SS8eEvo93l/txRMeGsCRxuw3QREoW0WJb3vHvdom8WMgzhYp+h/fAVFRxVf0AnEA=="
  "resolved" "http://npm.mail.netease.com/registry/@tetris/commitlint-config/-/@tetris/commitlint-config-1.0.0.tgz"
  "version" "1.0.0"

"@tootallnate/once@2":
  "version" "2.0.0"

"@tsconfig/node10@^1.0.7":
  "integrity" "sha512-jNsYVVxU8v5g43Erja32laIDHXeoNvFEpX33OK4d6hljo3jDhCBDhx5dhCCTMWUojscpAagGiRkBKxpdl9fxqA=="
  "resolved" "https://registry.npmmirror.com/@tsconfig/node10/-/node10-1.0.9.tgz"
  "version" "1.0.9"

"@tsconfig/node12@^1.0.7":
  "integrity" "sha512-cqefuRsh12pWyGsIoBKJA9luFu3mRxCA+ORZvA4ktLSzIuCUtWVxGIuXigEwO5/ywWFMZ2QEGKWvkZG1zDMTag=="
  "resolved" "https://registry.npmmirror.com/@tsconfig/node12/-/node12-1.0.11.tgz"
  "version" "1.0.11"

"@tsconfig/node14@^1.0.0":
  "integrity" "sha512-ysT8mhdixWK6Hw3i1V2AeRqZ5WfXg1G43mqoYlM2nc6388Fq5jcXyr5mRsqViLx/GJYdoL0bfXD8nmF+Zn/Iow=="
  "resolved" "https://registry.npmmirror.com/@tsconfig/node14/-/node14-1.0.3.tgz"
  "version" "1.0.3"

"@tsconfig/node16@^1.0.2":
  "integrity" "sha512-vxhUy4J8lyeyinH7Azl1pdd43GJhZH/tP2weN8TntQblOY+A0XbT8DJk1/oCPuOOyg/Ja757rG0CgHcWC8OfMA=="
  "resolved" "https://registry.npmmirror.com/@tsconfig/node16/-/node16-1.0.4.tgz"
  "version" "1.0.4"

"@types/minimist@^1.2.0":
  "integrity" "sha512-jhuKLIRrhvCPLqwPcx6INqmKeiA5EWrsCOPhrlFSrbrmU4ZMPjj5Ul/oLCMDO98XRUIwVm78xICz4EPCektzeQ=="
  "resolved" "https://registry.npmmirror.com/@types/minimist/-/minimist-1.2.2.tgz"
  "version" "1.2.2"

"@types/node@*":
  "integrity" "sha512-pg9d0yC4rVNWQzX8U7xb4olIOFuuVL9za3bzMT2pu2SU0SNEi66i2qrvhE2qt0HvkhuCaWJu7pLNOt/Pj8BIrw=="
  "resolved" "https://registry.npmmirror.com/@types/node/-/node-20.2.3.tgz"
  "version" "20.2.3"

"@types/normalize-package-data@^2.4.0":
  "integrity" "sha512-Gj7cI7z+98M282Tqmp2K5EIsoouUEzbBJhQQzDE3jSIRk6r9gsz0oUokqIUR4u1R3dMHo0pDHM7sNOHyhulypw=="
  "resolved" "https://registry.npmmirror.com/@types/normalize-package-data/-/normalize-package-data-2.4.1.tgz"
  "version" "2.4.1"

"abbrev@^1.0.0", "abbrev@~1.1.1", "abbrev@1":
  "version" "1.1.1"

"acorn-walk@^8.1.1":
  "integrity" "sha512-k+iyHEuPgSw6SbuDpGQM+06HQUa04DZ3o+F6CSzXMvvI5KMvnaEqXe+YVe555R9nn6GPt404fos4wcgpw12SDA=="
  "resolved" "https://registry.npmmirror.com/acorn-walk/-/acorn-walk-8.2.0.tgz"
  "version" "8.2.0"

"acorn@^8.4.1":
  "integrity" "sha512-xjIYgE8HBrkpd/sJqOGNspf8uHG+NOHGOw6a/Urj8taM2EXfdNAH2oFcPeIFfsv3+kz/mJrS5VuMqbNLjCa2vw=="
  "resolved" "https://registry.npmmirror.com/acorn/-/acorn-8.8.2.tgz"
  "version" "8.8.2"

"agent-base@^6.0.2", "agent-base@6":
  "integrity" "sha1-Sf/1hXfP7j83F2/qtMIuAPhtf3c="
  "resolved" "https://registry.npmmirror.com/agent-base/download/agent-base-6.0.2.tgz"
  "version" "6.0.2"
  dependencies:
    "debug" "4"

"agentkeepalive@^4.2.1":
  "version" "4.2.1"
  dependencies:
    "debug" "^4.1.0"
    "depd" "^1.1.2"
    "humanize-ms" "^1.2.1"

"aggregate-error@^3.0.0":
  "integrity" "sha1-kmcP9Q9TWb23o+DUDQ7DDFc3aHo="
  "resolved" "https://registry.npmmirror.com/aggregate-error/download/aggregate-error-3.1.0.tgz?cache=0&sync_timestamp=1618681553608&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Faggregate-error%2Fdownload%2Faggregate-error-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "clean-stack" "^2.0.0"
    "indent-string" "^4.0.0"

"ajv@^8.11.0":
  "integrity" "sha512-sRu1kpcO9yLtYxBKvqfTeh9KzZEwO3STyX1HT+4CaDzC6HpTGYhIhPIzj9XuKU7KYDwnaeh5hcOwjy1QuJzBPA=="
  "resolved" "https://registry.npmmirror.com/ajv/-/ajv-8.12.0.tgz"
  "version" "8.12.0"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "json-schema-traverse" "^1.0.0"
    "require-from-string" "^2.0.2"
    "uri-js" "^4.2.2"

"ansi-regex@^5.0.1":
  "integrity" "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ=="
  "resolved" "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-5.0.1.tgz"
  "version" "5.0.1"

"ansi-styles@^3.2.1":
  "integrity" "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA=="
  "resolved" "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "color-convert" "^1.9.0"

"ansi-styles@^4.0.0", "ansi-styles@^4.1.0":
  "integrity" "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg=="
  "resolved" "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "color-convert" "^2.0.1"

"ansi-styles@^4.3.0":
  "integrity" "sha1-7dgDYornHATIWuegkG7a00tkiTc="
  "resolved" "https://registry.npmmirror.com/ansi-styles/download/ansi-styles-4.3.0.tgz?cache=0&sync_timestamp=1618995588464&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fansi-styles%2Fdownload%2Fansi-styles-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "color-convert" "^2.0.1"

"aproba@^1.0.3 || ^2.0.0", "aproba@^2.0.0":
  "version" "2.0.0"

"archy@~1.0.0":
  "version" "1.0.0"

"are-we-there-yet@^3.0.0":
  "version" "3.0.1"
  dependencies:
    "delegates" "^1.0.0"
    "readable-stream" "^3.6.0"

"arg@^4.1.0":
  "integrity" "sha512-58S9QDqG0Xx27YwPSt9fJxivjYl432YCwfDMfZ+71RAqUrZef7LrKQZ3LHLOwCS4FLNBplP533Zx895SeOCHvA=="
  "resolved" "https://registry.npmmirror.com/arg/-/arg-4.1.3.tgz"
  "version" "4.1.3"

"argparse@^2.0.1":
  "integrity" "sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q=="
  "resolved" "https://registry.npmmirror.com/argparse/-/argparse-2.0.1.tgz"
  "version" "2.0.1"

"array-ify@^1.0.0":
  "integrity" "sha512-c5AMf34bKdvPhQ7tBGhqkgKNUzMr4WUs+WDtC2ZUGOUncbxKMTvqxYctiseW3+L4bA8ec+GcZ6/A/FW4m8ukng=="
  "resolved" "https://registry.npmmirror.com/array-ify/-/array-ify-1.0.0.tgz"
  "version" "1.0.0"

"arrify@^1.0.1":
  "integrity" "sha512-3CYzex9M9FGQjCGMGyi6/31c8GJbgb0qGyrx5HWxPd0aCwh4cB2YjMb2Xf9UuoogrMrlO9cTqnB5rI5GHZTcUA=="
  "resolved" "https://registry.npmmirror.com/arrify/-/arrify-1.0.1.tgz"
  "version" "1.0.1"

"asap@^2.0.0":
  "version" "2.0.6"

"balanced-match@^1.0.0":
  "integrity" "sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4="
  "resolved" "https://registry.npmmirror.com/balanced-match/download/balanced-match-1.0.2.tgz?cache=0&sync_timestamp=1617714233441&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fbalanced-match%2Fdownload%2Fbalanced-match-1.0.2.tgz"
  "version" "1.0.2"

"bin-links@^3.0.3":
  "version" "3.0.3"
  dependencies:
    "cmd-shim" "^5.0.0"
    "mkdirp-infer-owner" "^2.0.0"
    "npm-normalize-package-bin" "^2.0.0"
    "read-cmd-shim" "^3.0.0"
    "rimraf" "^3.0.0"
    "write-file-atomic" "^4.0.0"

"binary-extensions@^2.2.0":
  "version" "2.2.0"

"brace-expansion@^1.1.7":
  "integrity" "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0="
  "resolved" "https://registry.npmmirror.com/brace-expansion/download/brace-expansion-1.1.11.tgz"
  "version" "1.1.11"
  dependencies:
    "balanced-match" "^1.0.0"
    "concat-map" "0.0.1"

"brace-expansion@^2.0.1":
  "version" "2.0.1"
  dependencies:
    "balanced-match" "^1.0.0"

"builtins@^5.0.0":
  "version" "5.0.1"
  dependencies:
    "semver" "^7.0.0"

"cacache@^16.0.0", "cacache@^16.1.0", "cacache@^16.1.3":
  "version" "16.1.3"
  dependencies:
    "@npmcli/fs" "^2.1.0"
    "@npmcli/move-file" "^2.0.0"
    "chownr" "^2.0.0"
    "fs-minipass" "^2.1.0"
    "glob" "^8.0.1"
    "infer-owner" "^1.0.4"
    "lru-cache" "^7.7.1"
    "minipass" "^3.1.6"
    "minipass-collect" "^1.0.2"
    "minipass-flush" "^1.0.5"
    "minipass-pipeline" "^1.2.4"
    "mkdirp" "^1.0.4"
    "p-map" "^4.0.0"
    "promise-inflight" "^1.0.1"
    "rimraf" "^3.0.2"
    "ssri" "^9.0.0"
    "tar" "^6.1.11"
    "unique-filename" "^2.0.0"

"callsites@^3.0.0":
  "integrity" "sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ=="
  "resolved" "https://registry.npmmirror.com/callsites/-/callsites-3.1.0.tgz"
  "version" "3.1.0"

"camelcase-keys@^6.2.2":
  "integrity" "sha512-YrwaA0vEKazPBkn0ipTiMpSajYDSe+KjQfrjhcBMxJt/znbvlHd8Pw/Vamaz5EB4Wfhs3SUR3Z9mwRu/P3s3Yg=="
  "resolved" "https://registry.npmmirror.com/camelcase-keys/-/camelcase-keys-6.2.2.tgz"
  "version" "6.2.2"
  dependencies:
    "camelcase" "^5.3.1"
    "map-obj" "^4.0.0"
    "quick-lru" "^4.0.1"

"camelcase@^5.3.1":
  "integrity" "sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg=="
  "resolved" "https://registry.npmmirror.com/camelcase/-/camelcase-5.3.1.tgz"
  "version" "5.3.1"

"chalk@^2.0.0":
  "integrity" "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ=="
  "resolved" "https://registry.npmmirror.com/chalk/-/chalk-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "ansi-styles" "^3.2.1"
    "escape-string-regexp" "^1.0.5"
    "supports-color" "^5.3.0"

"chalk@^4.0.0", "chalk@^4.1.2":
  "integrity" "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE="
  "resolved" "https://registry.npmmirror.com/chalk/download/chalk-4.1.2.tgz?cache=0&sync_timestamp=1627646655305&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fchalk%2Fdownload%2Fchalk-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"chalk@^4.1.0":
  "integrity" "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA=="
  "resolved" "https://registry.npmmirror.com/chalk/-/chalk-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"chownr@^2.0.0":
  "version" "2.0.0"

"cidr-regex@^3.1.1":
  "version" "3.1.1"
  dependencies:
    "ip-regex" "^4.1.0"

"clean-stack@^2.0.0":
  "integrity" "sha1-7oRy27Ep5yezHooQpCfe6d/kAIs="
  "resolved" "https://registry.npmmirror.com/clean-stack/download/clean-stack-2.2.0.tgz?cache=0&sync_timestamp=1621915044030&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fclean-stack%2Fdownload%2Fclean-stack-2.2.0.tgz"
  "version" "2.2.0"

"cli-columns@^4.0.0":
  "version" "4.0.0"
  dependencies:
    "string-width" "^4.2.3"
    "strip-ansi" "^6.0.1"

"cli-table3@^0.6.2":
  "version" "0.6.2"
  dependencies:
    "string-width" "^4.2.0"
  optionalDependencies:
    "@colors/colors" "1.5.0"

"cliui@^8.0.1":
  "integrity" "sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ=="
  "resolved" "https://registry.npmmirror.com/cliui/-/cliui-8.0.1.tgz"
  "version" "8.0.1"
  dependencies:
    "string-width" "^4.2.0"
    "strip-ansi" "^6.0.1"
    "wrap-ansi" "^7.0.0"

"clone@^1.0.2":
  "version" "1.0.4"

"cmd-shim@^5.0.0":
  "version" "5.0.0"
  dependencies:
    "mkdirp-infer-owner" "^2.0.0"

"color-convert@^1.9.0":
  "integrity" "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg=="
  "resolved" "https://registry.npmmirror.com/color-convert/-/color-convert-1.9.3.tgz"
  "version" "1.9.3"
  dependencies:
    "color-name" "1.1.3"

"color-convert@^2.0.1":
  "integrity" "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ=="
  "resolved" "https://registry.npmmirror.com/color-convert/-/color-convert-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "color-name" "~1.1.4"

"color-name@~1.1.4":
  "integrity" "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA=="
  "resolved" "https://registry.npmmirror.com/color-name/-/color-name-1.1.4.tgz"
  "version" "1.1.4"

"color-name@1.1.3":
  "integrity" "sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw=="
  "resolved" "https://registry.npmmirror.com/color-name/-/color-name-1.1.3.tgz"
  "version" "1.1.3"

"color-support@^1.1.3":
  "version" "1.1.3"

"columnify@^1.6.0":
  "version" "1.6.0"
  dependencies:
    "strip-ansi" "^6.0.1"
    "wcwidth" "^1.0.0"

"commitlint@^17.1.2":
  "integrity" "sha512-0S6j3gKZyLmY4F/YchW7lqXiBeplFBKJqXcrOdxhzJdsZdqiPZxqsN7zq++Ovc4iMLnX65W4bJB7YKalYfXesw=="
  "resolved" "https://registry.npmmirror.com/commitlint/-/commitlint-17.6.3.tgz"
  "version" "17.6.3"
  dependencies:
    "@commitlint/cli" "^17.6.3"
    "@commitlint/types" "^17.4.4"

"common-ancestor-path@^1.0.1":
  "version" "1.0.1"

"compare-func@^2.0.0":
  "integrity" "sha512-zHig5N+tPWARooBnb0Zx1MFcdfpyJrfTJ3Y5L+IFvUm8rM74hHz66z0gw0x4tijh5CorKkKUCnW82R2vmpeCRA=="
  "resolved" "https://registry.npmmirror.com/compare-func/-/compare-func-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "array-ify" "^1.0.0"
    "dot-prop" "^5.1.0"

"concat-map@0.0.1":
  "integrity" "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s="
  "resolved" "https://registry.npmmirror.com/concat-map/download/concat-map-0.0.1.tgz"
  "version" "0.0.1"

"console-control-strings@^1.1.0":
  "version" "1.1.0"

"conventional-changelog-angular@^5.0.11":
  "integrity" "sha512-i/gipMxs7s8L/QeuavPF2hLnJgH6pEZAttySB6aiQLWcX3puWDL3ACVmvBhJGxnAy52Qc15ua26BufY6KpmrVA=="
  "resolved" "https://registry.npmmirror.com/conventional-changelog-angular/-/conventional-changelog-angular-5.0.13.tgz"
  "version" "5.0.13"
  dependencies:
    "compare-func" "^2.0.0"
    "q" "^1.5.1"

"conventional-commits-parser@^3.2.2":
  "integrity" "sha512-nK7sAtfi+QXbxHCYfhpZsfRtaitZLIA6889kFIouLvz6repszQDgxBu7wf2WbU+Dco7sAnNCJYERCwt54WPC2Q=="
  "resolved" "https://registry.npmmirror.com/conventional-commits-parser/-/conventional-commits-parser-3.2.4.tgz"
  "version" "3.2.4"
  dependencies:
    "is-text-path" "^1.0.1"
    "JSONStream" "^1.0.4"
    "lodash" "^4.17.15"
    "meow" "^8.0.0"
    "split2" "^3.0.0"
    "through2" "^4.0.0"

"cosmiconfig-typescript-loader@^4.0.0":
  "integrity" "sha512-NTxV1MFfZDLPiBMjxbHRwSh5LaLcPMwNdCutmnHJCKoVnlvldPWlllonKwrsRJ5pYZBIBGRWWU2tfvzxgeSW5Q=="
  "resolved" "https://registry.npmmirror.com/cosmiconfig-typescript-loader/-/cosmiconfig-typescript-loader-4.3.0.tgz"
  "version" "4.3.0"

"cosmiconfig@^8.0.0", "cosmiconfig@>=7":
  "integrity" "sha512-/UkO2JKI18b5jVMJUp0lvKFMpa/Gye+ZgZjKD+DGEN9y7NRcf/nK1A0sp67ONmKtnDCNMS44E6jrk0Yc3bDuUw=="
  "resolved" "https://registry.npmmirror.com/cosmiconfig/-/cosmiconfig-8.1.3.tgz"
  "version" "8.1.3"
  dependencies:
    "import-fresh" "^3.2.1"
    "js-yaml" "^4.1.0"
    "parse-json" "^5.0.0"
    "path-type" "^4.0.0"

"create-require@^1.1.0":
  "integrity" "sha512-dcKFX3jn0MpIaXjisoRvexIJVEKzaq7z2rZKxf+MSr9TkdmHmsU4m2lcLojrj/FHl8mk5VxMmYA+ftRkP/3oKQ=="
  "resolved" "https://registry.npmmirror.com/create-require/-/create-require-1.1.1.tgz"
  "version" "1.1.1"

"cross-spawn@^7.0.3":
  "integrity" "sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w=="
  "resolved" "https://registry.npmmirror.com/cross-spawn/-/cross-spawn-7.0.3.tgz"
  "version" "7.0.3"
  dependencies:
    "path-key" "^3.1.0"
    "shebang-command" "^2.0.0"
    "which" "^2.0.1"

"cssesc@^3.0.0":
  "integrity" "sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4="
  "resolved" "https://registry.npmmirror.com/cssesc/download/cssesc-3.0.0.tgz"
  "version" "3.0.0"

"cz-git@^1.3.12":
  "integrity" "sha512-+ohk/MFETERw4o/WpN3hUk7GM8oAwnaQI/uDuREGm4/B+G3DghGvNZQ/0MbFqZHWgKWvMzNZI6eKmPo0uPCu1A=="
  "resolved" "https://registry.npmmirror.com/cz-git/-/cz-git-1.6.1.tgz"
  "version" "1.6.1"

"dargs@^7.0.0":
  "integrity" "sha512-2iy1EkLdlBzQGvbweYRFxmFath8+K7+AKB0TlhHWkNuH+TmovaMH/Wp7V7R4u7f4SnX3OgLsU9t1NI9ioDnUpg=="
  "resolved" "https://registry.npmmirror.com/dargs/-/dargs-7.0.0.tgz"
  "version" "7.0.0"

"debug@^4.1.0", "debug@^4.3.3", "debug@4":
  "version" "4.3.4"
  dependencies:
    "ms" "2.1.2"

"debuglog@^1.0.1":
  "version" "1.0.1"

"decamelize-keys@^1.1.0":
  "integrity" "sha512-WiPxgEirIV0/eIOMcnFBA3/IJZAZqKnwAwWyvvdi4lsr1WCN22nhdf/3db3DoZcUjTV2SqfzIwNyp6y2xs3nmg=="
  "resolved" "https://registry.npmmirror.com/decamelize-keys/-/decamelize-keys-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "decamelize" "^1.1.0"
    "map-obj" "^1.0.0"

"decamelize@^1.1.0":
  "integrity" "sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA=="
  "resolved" "https://registry.npmmirror.com/decamelize/-/decamelize-1.2.0.tgz"
  "version" "1.2.0"

"defaults@^1.0.3":
  "version" "1.0.3"
  dependencies:
    "clone" "^1.0.2"

"delegates@^1.0.0":
  "version" "1.0.0"

"depd@^1.1.2":
  "version" "1.1.2"

"dezalgo@^1.0.0":
  "version" "1.0.4"
  dependencies:
    "asap" "^2.0.0"
    "wrappy" "1"

"diff@^4.0.1":
  "integrity" "sha512-58lmxKSA4BNyLz+HHMUzlOEpg09FV+ev6ZMe3vJihgdxzgcwZ8VoEEPmALCZG9LmqfVoNMMKpttIYTVG6uDY7A=="
  "resolved" "https://registry.npmmirror.com/diff/-/diff-4.0.2.tgz"
  "version" "4.0.2"

"diff@^5.1.0":
  "version" "5.1.0"

"dot-prop@^5.1.0":
  "integrity" "sha512-QM8q3zDe58hqUqjraQOmzZ1LIH9SWQJTlEKCH4kJ2oQvLZk7RbQXvtDM2XEq3fwkV9CCvvH4LA0AV+ogFsBM2Q=="
  "resolved" "https://registry.npmmirror.com/dot-prop/-/dot-prop-5.3.0.tgz"
  "version" "5.3.0"
  dependencies:
    "is-obj" "^2.0.0"

"emoji-regex@^8.0.0":
  "integrity" "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A=="
  "resolved" "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-8.0.0.tgz"
  "version" "8.0.0"

"encoding@^0.1.13":
  "version" "0.1.13"
  dependencies:
    "iconv-lite" "^0.6.2"

"env-paths@^2.2.0":
  "version" "2.2.1"

"err-code@^2.0.2":
  "version" "2.0.3"

"error-ex@^1.3.1":
  "integrity" "sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g=="
  "resolved" "https://registry.npmmirror.com/error-ex/-/error-ex-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "is-arrayish" "^0.2.1"

"escalade@^3.1.1":
  "integrity" "sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw=="
  "resolved" "https://registry.npmmirror.com/escalade/-/escalade-3.1.1.tgz"
  "version" "3.1.1"

"escape-string-regexp@^1.0.5":
  "integrity" "sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg=="
  "resolved" "https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"
  "version" "1.0.5"

"execa@^5.0.0":
  "integrity" "sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg=="
  "resolved" "https://registry.npmmirror.com/execa/-/execa-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "cross-spawn" "^7.0.3"
    "get-stream" "^6.0.0"
    "human-signals" "^2.1.0"
    "is-stream" "^2.0.0"
    "merge-stream" "^2.0.0"
    "npm-run-path" "^4.0.1"
    "onetime" "^5.1.2"
    "signal-exit" "^3.0.3"
    "strip-final-newline" "^2.0.0"

"fast-deep-equal@^3.1.1":
  "integrity" "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q=="
  "resolved" "https://registry.npmmirror.com/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  "version" "3.1.3"

"fastest-levenshtein@^1.0.12":
  "version" "1.0.12"

"find-up@^4.1.0":
  "integrity" "sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw=="
  "resolved" "https://registry.npmmirror.com/find-up/-/find-up-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "locate-path" "^5.0.0"
    "path-exists" "^4.0.0"

"find-up@^5.0.0":
  "integrity" "sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng=="
  "resolved" "https://registry.npmmirror.com/find-up/-/find-up-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "locate-path" "^6.0.0"
    "path-exists" "^4.0.0"

"fs-extra@^11.0.0":
  "integrity" "sha512-MGIE4HOvQCeUCzmlHs0vXpih4ysz4wg9qiSAu6cd42lVwPbTM1TjV7RusoyQqMmk/95gdQZX72u+YW+c3eEpFQ=="
  "resolved" "https://registry.npmmirror.com/fs-extra/-/fs-extra-11.1.1.tgz"
  "version" "11.1.1"
  dependencies:
    "graceful-fs" "^4.2.0"
    "jsonfile" "^6.0.1"
    "universalify" "^2.0.0"

"fs-minipass@^2.0.0", "fs-minipass@^2.1.0":
  "version" "2.1.0"
  dependencies:
    "minipass" "^3.0.0"

"fs.realpath@^1.0.0":
  "integrity" "sha1-FQStJSMVjKpA20onh8sBQRmU6k8="
  "resolved" "https://registry.npmmirror.com/fs.realpath/download/fs.realpath-1.0.0.tgz"
  "version" "1.0.0"

"function-bind@^1.1.1":
  "integrity" "sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A=="
  "resolved" "https://registry.npmmirror.com/function-bind/-/function-bind-1.1.1.tgz"
  "version" "1.1.1"

"gauge@^4.0.3":
  "version" "4.0.4"
  dependencies:
    "aproba" "^1.0.3 || ^2.0.0"
    "color-support" "^1.1.3"
    "console-control-strings" "^1.1.0"
    "has-unicode" "^2.0.1"
    "signal-exit" "^3.0.7"
    "string-width" "^4.2.3"
    "strip-ansi" "^6.0.1"
    "wide-align" "^1.1.5"

"get-caller-file@^2.0.5":
  "integrity" "sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg=="
  "resolved" "https://registry.npmmirror.com/get-caller-file/-/get-caller-file-2.0.5.tgz"
  "version" "2.0.5"

"get-stream@^6.0.0":
  "integrity" "sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg=="
  "resolved" "https://registry.npmmirror.com/get-stream/-/get-stream-6.0.1.tgz"
  "version" "6.0.1"

"git-raw-commits@^2.0.11":
  "integrity" "sha512-VnctFhw+xfj8Va1xtfEqCUD2XDrbAPSJx+hSrE5K7fGdjZruW7XV+QOrN7LF/RJyvspRiD2I0asWsxFp0ya26A=="
  "resolved" "https://registry.npmmirror.com/git-raw-commits/-/git-raw-commits-2.0.11.tgz"
  "version" "2.0.11"
  dependencies:
    "dargs" "^7.0.0"
    "lodash" "^4.17.15"
    "meow" "^8.0.0"
    "split2" "^3.0.0"
    "through2" "^4.0.0"

"glob@^7.1.3":
  "version" "7.2.3"
  dependencies:
    "fs.realpath" "^1.0.0"
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "^3.1.1"
    "once" "^1.3.0"
    "path-is-absolute" "^1.0.0"

"glob@^7.1.4":
  "version" "7.2.3"
  dependencies:
    "fs.realpath" "^1.0.0"
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "^3.1.1"
    "once" "^1.3.0"
    "path-is-absolute" "^1.0.0"

"glob@^8.0.1":
  "version" "8.0.3"
  dependencies:
    "fs.realpath" "^1.0.0"
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "^5.0.1"
    "once" "^1.3.0"

"global-dirs@^0.1.1":
  "integrity" "sha512-NknMLn7F2J7aflwFOlGdNIuCDpN3VGoSoB+aap3KABFWbHVn1TCgFC+np23J8W2BiZbjfEw3BFBycSMv1AFblg=="
  "resolved" "https://registry.npmmirror.com/global-dirs/-/global-dirs-0.1.1.tgz"
  "version" "0.1.1"
  dependencies:
    "ini" "^1.3.4"

"graceful-fs@^4.1.2", "graceful-fs@^4.2.10", "graceful-fs@^4.2.6":
  "version" "4.2.10"

"graceful-fs@^4.1.6", "graceful-fs@^4.2.0":
  "integrity" "sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ=="
  "resolved" "https://registry.npmmirror.com/graceful-fs/-/graceful-fs-4.2.11.tgz"
  "version" "4.2.11"

"hard-rejection@^2.1.0":
  "integrity" "sha512-VIZB+ibDhx7ObhAe7OVtoEbuP4h/MuOTHJ+J8h/eBXotJYl0fBgR72xDFCKgIh22OJZIOVNxBMWuhAr10r8HdA=="
  "resolved" "https://registry.npmmirror.com/hard-rejection/-/hard-rejection-2.1.0.tgz"
  "version" "2.1.0"

"has-flag@^3.0.0":
  "integrity" "sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw=="
  "resolved" "https://registry.npmmirror.com/has-flag/-/has-flag-3.0.0.tgz"
  "version" "3.0.0"

"has-flag@^4.0.0":
  "integrity" "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ=="
  "resolved" "https://registry.npmmirror.com/has-flag/-/has-flag-4.0.0.tgz"
  "version" "4.0.0"

"has-unicode@^2.0.1":
  "version" "2.0.1"

"has@^1.0.3":
  "integrity" "sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw=="
  "resolved" "https://registry.npmmirror.com/has/-/has-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "function-bind" "^1.1.1"

"hosted-git-info@^2.1.4":
  "integrity" "sha512-mxIDAb9Lsm6DoOJ7xH+5+X4y1LU/4Hi50L9C5sIswK3JzULS4bwk1FvjdBgvYR4bzT4tuUQiC15FE2f5HbLvYw=="
  "resolved" "https://registry.npmmirror.com/hosted-git-info/-/hosted-git-info-2.8.9.tgz"
  "version" "2.8.9"

"hosted-git-info@^4.0.1":
  "integrity" "sha512-kyCuEOWjJqZuDbRHzL8V93NzQhwIB71oFWSyzVo+KPZI+pnQPPxucdkrOZvkLRnrf5URsQM+IJ09Dw29cRALIA=="
  "resolved" "https://registry.npmmirror.com/hosted-git-info/-/hosted-git-info-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "lru-cache" "^6.0.0"

"hosted-git-info@^5.0.0", "hosted-git-info@^5.2.1":
  "version" "5.2.1"
  dependencies:
    "lru-cache" "^7.5.1"

"http-cache-semantics@^4.1.0":
  "version" "4.1.1"

"http-proxy-agent@^5.0.0":
  "version" "5.0.0"
  dependencies:
    "@tootallnate/once" "2"
    "agent-base" "6"
    "debug" "4"

"https-proxy-agent@^5.0.0":
  "version" "5.0.1"
  dependencies:
    "agent-base" "6"
    "debug" "4"

"human-signals@^2.1.0":
  "integrity" "sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw=="
  "resolved" "https://registry.npmmirror.com/human-signals/-/human-signals-2.1.0.tgz"
  "version" "2.1.0"

"humanize-ms@^1.2.1":
  "version" "1.2.1"
  dependencies:
    "ms" "^2.0.0"

"husky@^8.0.1":
  "integrity" "sha512-+dQSyqPh4x1hlO1swXBiNb2HzTDN1I2IGLQx1GrBuiqFJfoMrnZWwVmatvSiO+Iz8fBUnf+lekwNo4c2LlXItg=="
  "resolved" "https://registry.npmmirror.com/husky/-/husky-8.0.3.tgz"
  "version" "8.0.3"

"iconv-lite@^0.6.2":
  "integrity" "sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE="
  "resolved" "https://registry.npmmirror.com/iconv-lite/download/iconv-lite-0.6.3.tgz"
  "version" "0.6.3"
  dependencies:
    "safer-buffer" ">= 2.1.2 < 3.0.0"

"ignore-walk@^5.0.1":
  "version" "5.0.1"
  dependencies:
    "minimatch" "^5.0.1"

"import-fresh@^3.0.0", "import-fresh@^3.2.1":
  "integrity" "sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw=="
  "resolved" "https://registry.npmmirror.com/import-fresh/-/import-fresh-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "parent-module" "^1.0.0"
    "resolve-from" "^4.0.0"

"imurmurhash@^0.1.4":
  "integrity" "sha1-khi5srkoojixPcT7a21XbyMUU+o="
  "resolved" "https://registry.npmmirror.com/imurmurhash/download/imurmurhash-0.1.4.tgz"
  "version" "0.1.4"

"indent-string@^4.0.0":
  "integrity" "sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg=="
  "resolved" "https://registry.npmmirror.com/indent-string/-/indent-string-4.0.0.tgz"
  "version" "4.0.0"

"infer-owner@^1.0.4":
  "version" "1.0.4"

"inflight@^1.0.4":
  "integrity" "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk="
  "resolved" "https://registry.npmmirror.com/inflight/download/inflight-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "once" "^1.3.0"
    "wrappy" "1"

"inherits@^2.0.3":
  "integrity" "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ=="
  "resolved" "https://registry.npmmirror.com/inherits/-/inherits-2.0.4.tgz"
  "version" "2.0.4"

"inherits@2":
  "integrity" "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w="
  "resolved" "https://registry.npmmirror.com/inherits/download/inherits-2.0.4.tgz"
  "version" "2.0.4"

"ini@^1.3.4":
  "integrity" "sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew=="
  "resolved" "https://registry.npmmirror.com/ini/-/ini-1.3.8.tgz"
  "version" "1.3.8"

"ini@^3.0.0", "ini@^3.0.1":
  "version" "3.0.1"

"init-package-json@^3.0.2":
  "version" "3.0.2"
  dependencies:
    "npm-package-arg" "^9.0.1"
    "promzard" "^0.3.0"
    "read" "^1.0.7"
    "read-package-json" "^5.0.0"
    "semver" "^7.3.5"
    "validate-npm-package-license" "^3.0.4"
    "validate-npm-package-name" "^4.0.0"

"ip-regex@^4.1.0":
  "version" "4.3.0"

"ip@^2.0.0":
  "version" "2.0.0"

"is-arrayish@^0.2.1":
  "integrity" "sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg=="
  "resolved" "https://registry.npmmirror.com/is-arrayish/-/is-arrayish-0.2.1.tgz"
  "version" "0.2.1"

"is-cidr@^4.0.2":
  "version" "4.0.2"
  dependencies:
    "cidr-regex" "^3.1.1"

"is-core-module@^2.11.0", "is-core-module@^2.5.0":
  "integrity" "sha512-Q4ZuBAe2FUsKtyQJoQHlvP8OvBERxO3jEmy1I7hcRXcJBGGHFh/aJBswbXuS9sgrDH2QUO8ilkwNPHvHMd8clg=="
  "resolved" "https://registry.npmmirror.com/is-core-module/-/is-core-module-2.12.1.tgz"
  "version" "2.12.1"
  dependencies:
    "has" "^1.0.3"

"is-core-module@^2.8.1":
  "version" "2.10.0"
  dependencies:
    "has" "^1.0.3"

"is-fullwidth-code-point@^3.0.0":
  "integrity" "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg=="
  "resolved" "https://registry.npmmirror.com/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
  "version" "3.0.0"

"is-lambda@^1.0.1":
  "version" "1.0.1"

"is-obj@^2.0.0":
  "integrity" "sha512-drqDG3cbczxxEJRoOXcOjtdp1J/lyp1mNn0xaznRs8+muBhgQcrnbspox5X5fOw0HnMnbfDzvnEMEtqDEJEo8w=="
  "resolved" "https://registry.npmmirror.com/is-obj/-/is-obj-2.0.0.tgz"
  "version" "2.0.0"

"is-plain-obj@^1.1.0":
  "integrity" "sha512-yvkRyxmFKEOQ4pNXCmJG5AEQNlXJS5LaONXo5/cLdTZdWvsZ1ioJEonLGAosKlMWE8lwUy/bJzMjcw8az73+Fg=="
  "resolved" "https://registry.npmmirror.com/is-plain-obj/-/is-plain-obj-1.1.0.tgz"
  "version" "1.1.0"

"is-stream@^2.0.0":
  "integrity" "sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg=="
  "resolved" "https://registry.npmmirror.com/is-stream/-/is-stream-2.0.1.tgz"
  "version" "2.0.1"

"is-text-path@^1.0.1":
  "integrity" "sha512-xFuJpne9oFz5qDaodwmmG08e3CawH/2ZV8Qqza1Ko7Sk8POWbkRdwIoAWVhqvq0XeUzANEhKo2n0IXUGBm7A/w=="
  "resolved" "https://registry.npmmirror.com/is-text-path/-/is-text-path-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "text-extensions" "^1.0.0"

"isexe@^2.0.0":
  "integrity" "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw=="
  "resolved" "https://registry.npmmirror.com/isexe/-/isexe-2.0.0.tgz"
  "version" "2.0.0"

"js-tokens@^4.0.0":
  "integrity" "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ=="
  "resolved" "https://registry.npmmirror.com/js-tokens/-/js-tokens-4.0.0.tgz"
  "version" "4.0.0"

"js-yaml@^4.1.0":
  "integrity" "sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA=="
  "resolved" "https://registry.npmmirror.com/js-yaml/-/js-yaml-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "argparse" "^2.0.1"

"json-parse-even-better-errors@^2.3.0":
  "integrity" "sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w=="
  "resolved" "https://registry.npmmirror.com/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz"
  "version" "2.3.1"

"json-parse-even-better-errors@^2.3.1":
  "integrity" "sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0="
  "resolved" "https://registry.npmmirror.com/json-parse-even-better-errors/download/json-parse-even-better-errors-2.3.1.tgz"
  "version" "2.3.1"

"json-schema-traverse@^1.0.0":
  "integrity" "sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug=="
  "resolved" "https://registry.npmmirror.com/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz"
  "version" "1.0.0"

"json-stringify-nice@^1.1.4":
  "version" "1.1.4"

"jsonfile@^6.0.1":
  "integrity" "sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ=="
  "resolved" "https://registry.npmmirror.com/jsonfile/-/jsonfile-6.1.0.tgz"
  "version" "6.1.0"
  dependencies:
    "universalify" "^2.0.0"
  optionalDependencies:
    "graceful-fs" "^4.1.6"

"jsonparse@^1.2.0":
  "integrity" "sha512-POQXvpdL69+CluYsillJ7SUhKvytYjW9vG/GKpnf+xP8UWgYEM/RaMzHHofbALDiKbbP1W8UEYmgGl39WkPZsg=="
  "resolved" "https://registry.npmmirror.com/jsonparse/-/jsonparse-1.3.1.tgz"
  "version" "1.3.1"

"jsonparse@^1.3.1":
  "version" "1.3.1"

"JSONStream@^1.0.4":
  "integrity" "sha512-E+iruNOY8VV9s4JEbe1aNEm6MiszPRr/UfcHMz0TQh1BXSxHK+ASV1R6W4HpjBhSeS+54PIsAMCBmwD06LLsqQ=="
  "resolved" "https://registry.npmmirror.com/JSONStream/-/JSONStream-1.3.5.tgz"
  "version" "1.3.5"
  dependencies:
    "jsonparse" "^1.2.0"
    "through" ">=2.2.7 <3"

"just-diff-apply@^5.2.0":
  "version" "5.4.1"

"just-diff@^5.0.1":
  "version" "5.1.1"

"kind-of@^6.0.3":
  "integrity" "sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw=="
  "resolved" "https://registry.npmmirror.com/kind-of/-/kind-of-6.0.3.tgz"
  "version" "6.0.3"

"libnpmaccess@^6.0.4":
  "version" "6.0.4"
  dependencies:
    "aproba" "^2.0.0"
    "minipass" "^3.1.1"
    "npm-package-arg" "^9.0.1"
    "npm-registry-fetch" "^13.0.0"

"libnpmdiff@^4.0.5":
  "version" "4.0.5"
  dependencies:
    "@npmcli/disparity-colors" "^2.0.0"
    "@npmcli/installed-package-contents" "^1.0.7"
    "binary-extensions" "^2.2.0"
    "diff" "^5.1.0"
    "minimatch" "^5.0.1"
    "npm-package-arg" "^9.0.1"
    "pacote" "^13.6.1"
    "tar" "^6.1.0"

"libnpmexec@^4.0.14":
  "version" "4.0.14"
  dependencies:
    "@npmcli/arborist" "^5.6.3"
    "@npmcli/ci-detect" "^2.0.0"
    "@npmcli/fs" "^2.1.1"
    "@npmcli/run-script" "^4.2.0"
    "chalk" "^4.1.0"
    "mkdirp-infer-owner" "^2.0.0"
    "npm-package-arg" "^9.0.1"
    "npmlog" "^6.0.2"
    "pacote" "^13.6.1"
    "proc-log" "^2.0.0"
    "read" "^1.0.7"
    "read-package-json-fast" "^2.0.2"
    "semver" "^7.3.7"
    "walk-up-path" "^1.0.0"

"libnpmfund@^3.0.5":
  "version" "3.0.5"
  dependencies:
    "@npmcli/arborist" "^5.6.3"

"libnpmhook@^8.0.4":
  "version" "8.0.4"
  dependencies:
    "aproba" "^2.0.0"
    "npm-registry-fetch" "^13.0.0"

"libnpmorg@^4.0.4":
  "version" "4.0.4"
  dependencies:
    "aproba" "^2.0.0"
    "npm-registry-fetch" "^13.0.0"

"libnpmpack@^4.1.3":
  "version" "4.1.3"
  dependencies:
    "@npmcli/run-script" "^4.1.3"
    "npm-package-arg" "^9.0.1"
    "pacote" "^13.6.1"

"libnpmpublish@^6.0.5":
  "version" "6.0.5"
  dependencies:
    "normalize-package-data" "^4.0.0"
    "npm-package-arg" "^9.0.1"
    "npm-registry-fetch" "^13.0.0"
    "semver" "^7.3.7"
    "ssri" "^9.0.0"

"libnpmsearch@^5.0.4":
  "version" "5.0.4"
  dependencies:
    "npm-registry-fetch" "^13.0.0"

"libnpmteam@^4.0.4":
  "version" "4.0.4"
  dependencies:
    "aproba" "^2.0.0"
    "npm-registry-fetch" "^13.0.0"

"libnpmversion@^3.0.7":
  "version" "3.0.7"
  dependencies:
    "@npmcli/git" "^3.0.0"
    "@npmcli/run-script" "^4.1.3"
    "json-parse-even-better-errors" "^2.3.1"
    "proc-log" "^2.0.0"
    "semver" "^7.3.7"

"lines-and-columns@^1.1.6":
  "integrity" "sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg=="
  "resolved" "https://registry.npmmirror.com/lines-and-columns/-/lines-and-columns-1.2.4.tgz"
  "version" "1.2.4"

"locate-path@^5.0.0":
  "integrity" "sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g=="
  "resolved" "https://registry.npmmirror.com/locate-path/-/locate-path-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "p-locate" "^4.1.0"

"locate-path@^6.0.0":
  "integrity" "sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw=="
  "resolved" "https://registry.npmmirror.com/locate-path/-/locate-path-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "p-locate" "^5.0.0"

"lodash.camelcase@^4.3.0":
  "integrity" "sha512-TwuEnCnxbc3rAvhf/LbG7tJUDzhqXyFnv3dtzLOPgCG/hODL7WFnsbwktkD7yUV0RrreP/l1PALq/YSg6VvjlA=="
  "resolved" "https://registry.npmmirror.com/lodash.camelcase/-/lodash.camelcase-4.3.0.tgz"
  "version" "4.3.0"

"lodash.isfunction@^3.0.9":
  "integrity" "sha512-AirXNj15uRIMMPihnkInB4i3NHeb4iBtNg9WRWuK2o31S+ePwwNmDPaTL3o7dTJ+VXNZim7rFs4rxN4YU1oUJw=="
  "resolved" "https://registry.npmmirror.com/lodash.isfunction/-/lodash.isfunction-3.0.9.tgz"
  "version" "3.0.9"

"lodash.isplainobject@^4.0.6":
  "integrity" "sha512-oSXzaWypCMHkPC3NvBEaPHf0KsA5mvPrOPgQWDsbg8n7orZ290M0BmC/jgRZ4vcJ6DTAhjrsSYgdsW/F+MFOBA=="
  "resolved" "https://registry.npmmirror.com/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz"
  "version" "4.0.6"

"lodash.kebabcase@^4.1.1":
  "integrity" "sha512-N8XRTIMMqqDgSy4VLKPnJ/+hpGZN+PHQiJnSenYqPaVV/NCqEogTnAdZLQiGKhxX+JCs8waWq2t1XHWKOmlY8g=="
  "resolved" "https://registry.npmmirror.com/lodash.kebabcase/-/lodash.kebabcase-4.1.1.tgz"
  "version" "4.1.1"

"lodash.merge@^4.6.2":
  "integrity" "sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ=="
  "resolved" "https://registry.npmmirror.com/lodash.merge/-/lodash.merge-4.6.2.tgz"
  "version" "4.6.2"

"lodash.mergewith@^4.6.2":
  "integrity" "sha512-GK3g5RPZWTRSeLSpgP8Xhra+pnjBC56q9FZYe1d5RN3TJ35dbkGy3YqBSMbyCrlbi+CM9Z3Jk5yTL7RCsqboyQ=="
  "resolved" "https://registry.npmmirror.com/lodash.mergewith/-/lodash.mergewith-4.6.2.tgz"
  "version" "4.6.2"

"lodash.snakecase@^4.1.1":
  "integrity" "sha512-QZ1d4xoBHYUeuouhEq3lk3Uq7ldgyFXGBhg04+oRLnIz8o9T65Eh+8YdroUwn846zchkA9yDsDl5CVVaV2nqYw=="
  "resolved" "https://registry.npmmirror.com/lodash.snakecase/-/lodash.snakecase-4.1.1.tgz"
  "version" "4.1.1"

"lodash.startcase@^4.4.0":
  "integrity" "sha512-+WKqsK294HMSc2jEbNgpHpd0JfIBhp7rEV4aqXWqFr6AlXov+SlcgB1Fv01y2kGe3Gc8nMW7VA0SrGuSkRfIEg=="
  "resolved" "https://registry.npmmirror.com/lodash.startcase/-/lodash.startcase-4.4.0.tgz"
  "version" "4.4.0"

"lodash.uniq@^4.5.0":
  "integrity" "sha512-xfBaXQd9ryd9dlSDvnvI0lvxfLJlYAZzXomUYzLKtUeOQvOP5piqAWuGtrhWeqaXK9hhoM/iyJc5AV+XfsX3HQ=="
  "resolved" "https://registry.npmmirror.com/lodash.uniq/-/lodash.uniq-4.5.0.tgz"
  "version" "4.5.0"

"lodash.upperfirst@^4.3.1":
  "integrity" "sha512-sReKOYJIJf74dhJONhU4e0/shzi1trVbSWDOhKYE5XV2O+H7Sb2Dihwuc7xWxVl+DgFPyTqIN3zMfT9cq5iWDg=="
  "resolved" "https://registry.npmmirror.com/lodash.upperfirst/-/lodash.upperfirst-4.3.1.tgz"
  "version" "4.3.1"

"lodash@^4.17.15":
  "integrity" "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg=="
  "resolved" "https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz"
  "version" "4.17.21"

"lru-cache@^6.0.0":
  "integrity" "sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA=="
  "resolved" "https://registry.npmmirror.com/lru-cache/-/lru-cache-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "yallist" "^4.0.0"

"lru-cache@^7.4.4", "lru-cache@^7.5.1", "lru-cache@^7.7.1":
  "version" "7.13.2"

"make-error@^1.1.1":
  "integrity" "sha512-s8UhlNe7vPKomQhC1qFelMokr/Sc3AgNbso3n74mVPA5LTZwkB9NlXf4XPamLxJE8h0gh73rM94xvwRT2CVInw=="
  "resolved" "https://registry.npmmirror.com/make-error/-/make-error-1.3.6.tgz"
  "version" "1.3.6"

"make-fetch-happen@^10.0.3", "make-fetch-happen@^10.0.6", "make-fetch-happen@^10.2.0":
  "version" "10.2.1"
  dependencies:
    "agentkeepalive" "^4.2.1"
    "cacache" "^16.1.0"
    "http-cache-semantics" "^4.1.0"
    "http-proxy-agent" "^5.0.0"
    "https-proxy-agent" "^5.0.0"
    "is-lambda" "^1.0.1"
    "lru-cache" "^7.7.1"
    "minipass" "^3.1.6"
    "minipass-collect" "^1.0.2"
    "minipass-fetch" "^2.0.3"
    "minipass-flush" "^1.0.5"
    "minipass-pipeline" "^1.2.4"
    "negotiator" "^0.6.3"
    "promise-retry" "^2.0.1"
    "socks-proxy-agent" "^7.0.0"
    "ssri" "^9.0.0"

"map-obj@^1.0.0":
  "integrity" "sha512-7N/q3lyZ+LVCp7PzuxrJr4KMbBE2hW7BT7YNia330OFxIf4d3r5zVpicP2650l7CPN6RM9zOJRl3NGpqSiw3Eg=="
  "resolved" "https://registry.npmmirror.com/map-obj/-/map-obj-1.0.1.tgz"
  "version" "1.0.1"

"map-obj@^4.0.0":
  "integrity" "sha512-hdN1wVrZbb29eBGiGjJbeP8JbKjq1urkHJ/LIP/NY48MZ1QVXUsQBV1G1zvYFHn1XE06cwjBsOI2K3Ulnj1YXQ=="
  "resolved" "https://registry.npmmirror.com/map-obj/-/map-obj-4.3.0.tgz"
  "version" "4.3.0"

"meow@^8.0.0":
  "integrity" "sha512-r85E3NdZ+mpYk1C6RjPFEMSE+s1iZMuHtsHAqY0DT3jZczl0diWUZ8g6oU7h0M9cD2EL+PzaYghhCLzR0ZNn5Q=="
  "resolved" "https://registry.npmmirror.com/meow/-/meow-8.1.2.tgz"
  "version" "8.1.2"
  dependencies:
    "@types/minimist" "^1.2.0"
    "camelcase-keys" "^6.2.2"
    "decamelize-keys" "^1.1.0"
    "hard-rejection" "^2.1.0"
    "minimist-options" "4.1.0"
    "normalize-package-data" "^3.0.0"
    "read-pkg-up" "^7.0.1"
    "redent" "^3.0.0"
    "trim-newlines" "^3.0.0"
    "type-fest" "^0.18.0"
    "yargs-parser" "^20.2.3"

"merge-stream@^2.0.0":
  "integrity" "sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w=="
  "resolved" "https://registry.npmmirror.com/merge-stream/-/merge-stream-2.0.0.tgz"
  "version" "2.0.0"

"mimic-fn@^2.1.0":
  "integrity" "sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg=="
  "resolved" "https://registry.npmmirror.com/mimic-fn/-/mimic-fn-2.1.0.tgz"
  "version" "2.1.0"

"min-indent@^1.0.0":
  "integrity" "sha512-I9jwMn07Sy/IwOj3zVkVik2JTvgpaykDZEigL6Rx6N9LbMywwUSMtxET+7lVoDLLd3O3IXwJwvuuns8UB/HeAg=="
  "resolved" "https://registry.npmmirror.com/min-indent/-/min-indent-1.0.1.tgz"
  "version" "1.0.1"

"minimatch@^3.1.1":
  "version" "3.1.2"
  dependencies:
    "brace-expansion" "^1.1.7"

"minimatch@^5.0.1", "minimatch@^5.1.0":
  "version" "5.1.0"
  dependencies:
    "brace-expansion" "^2.0.1"

"minimist-options@4.1.0":
  "integrity" "sha512-Q4r8ghd80yhO/0j1O3B2BjweX3fiHg9cdOwjJd2J76Q135c+NDxGCqdYKQ1SKBuFfgWbAUzBfvYjPUEeNgqN1A=="
  "resolved" "https://registry.npmmirror.com/minimist-options/-/minimist-options-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "arrify" "^1.0.1"
    "is-plain-obj" "^1.1.0"
    "kind-of" "^6.0.3"

"minimist@^1.2.6":
  "integrity" "sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA=="
  "resolved" "https://registry.npmmirror.com/minimist/-/minimist-1.2.8.tgz"
  "version" "1.2.8"

"minipass-collect@^1.0.2":
  "version" "1.0.2"
  dependencies:
    "minipass" "^3.0.0"

"minipass-fetch@^2.0.3":
  "version" "2.1.1"
  dependencies:
    "minipass" "^3.1.6"
    "minipass-sized" "^1.0.3"
    "minizlib" "^2.1.2"
  optionalDependencies:
    "encoding" "^0.1.13"

"minipass-flush@^1.0.5":
  "version" "1.0.5"
  dependencies:
    "minipass" "^3.0.0"

"minipass-json-stream@^1.0.1":
  "version" "1.0.1"
  dependencies:
    "jsonparse" "^1.3.1"
    "minipass" "^3.0.0"

"minipass-pipeline@^1.2.4":
  "version" "1.2.4"
  dependencies:
    "minipass" "^3.0.0"

"minipass-sized@^1.0.3":
  "version" "1.0.3"
  dependencies:
    "minipass" "^3.0.0"

"minipass@^3.0.0", "minipass@^3.1.1", "minipass@^3.1.6":
  "version" "3.3.4"
  dependencies:
    "yallist" "^4.0.0"

"minizlib@^2.1.1", "minizlib@^2.1.2":
  "version" "2.1.2"
  dependencies:
    "minipass" "^3.0.0"
    "yallist" "^4.0.0"

"mkdirp-infer-owner@^2.0.0":
  "version" "2.0.0"
  dependencies:
    "chownr" "^2.0.0"
    "infer-owner" "^1.0.4"
    "mkdirp" "^1.0.3"

"mkdirp@^1.0.3", "mkdirp@^1.0.4":
  "version" "1.0.4"

"ms@^2.0.0", "ms@^2.1.2":
  "version" "2.1.3"

"ms@2.1.2":
  "version" "2.1.2"

"mute-stream@~0.0.4":
  "version" "0.0.8"

"negotiator@^0.6.3":
  "version" "0.6.3"

"node-gyp@^9.0.0", "node-gyp@^9.1.0":
  "version" "9.1.0"
  dependencies:
    "env-paths" "^2.2.0"
    "glob" "^7.1.4"
    "graceful-fs" "^4.2.6"
    "make-fetch-happen" "^10.0.3"
    "nopt" "^5.0.0"
    "npmlog" "^6.0.0"
    "rimraf" "^3.0.2"
    "semver" "^7.3.5"
    "tar" "^6.1.2"
    "which" "^2.0.2"

"nopt@^5.0.0":
  "version" "5.0.0"
  dependencies:
    "abbrev" "1"

"nopt@^6.0.0":
  "version" "6.0.0"
  dependencies:
    "abbrev" "^1.0.0"

"normalize-package-data@^2.5.0":
  "integrity" "sha512-/5CMN3T0R4XTj4DcGaexo+roZSdSFW/0AOOTROrjxzCG1wrWXEsGbRKevjlIL+ZDE4sZlJr5ED4YW0yqmkK+eA=="
  "resolved" "https://registry.npmmirror.com/normalize-package-data/-/normalize-package-data-2.5.0.tgz"
  "version" "2.5.0"
  dependencies:
    "hosted-git-info" "^2.1.4"
    "resolve" "^1.10.0"
    "semver" "2 || 3 || 4 || 5"
    "validate-npm-package-license" "^3.0.1"

"normalize-package-data@^3.0.0":
  "integrity" "sha512-p2W1sgqij3zMMyRC067Dg16bfzVH+w7hyegmpIvZ4JNjqtGOVAIvLmjBx3yP7YTe9vKJgkoNOPjwQGogDoMXFA=="
  "resolved" "https://registry.npmmirror.com/normalize-package-data/-/normalize-package-data-3.0.3.tgz"
  "version" "3.0.3"
  dependencies:
    "hosted-git-info" "^4.0.1"
    "is-core-module" "^2.5.0"
    "semver" "^7.3.4"
    "validate-npm-package-license" "^3.0.1"

"normalize-package-data@^4.0.0":
  "version" "4.0.1"
  dependencies:
    "hosted-git-info" "^5.0.0"
    "is-core-module" "^2.8.1"
    "semver" "^7.3.5"
    "validate-npm-package-license" "^3.0.4"

"npm-audit-report@^3.0.0":
  "version" "3.0.0"
  dependencies:
    "chalk" "^4.0.0"

"npm-bundled@^1.1.1":
  "version" "1.1.2"
  dependencies:
    "npm-normalize-package-bin" "^1.0.1"

"npm-bundled@^2.0.0":
  "version" "2.0.1"
  dependencies:
    "npm-normalize-package-bin" "^2.0.0"

"npm-install-checks@^5.0.0":
  "version" "5.0.0"
  dependencies:
    "semver" "^7.1.1"

"npm-normalize-package-bin@^1.0.1":
  "version" "1.0.1"

"npm-normalize-package-bin@^2.0.0":
  "version" "2.0.0"

"npm-package-arg@^9.0.0", "npm-package-arg@^9.0.1", "npm-package-arg@^9.1.0":
  "version" "9.1.0"
  dependencies:
    "hosted-git-info" "^5.0.0"
    "proc-log" "^2.0.1"
    "semver" "^7.3.5"
    "validate-npm-package-name" "^4.0.0"

"npm-packlist@^5.1.0":
  "version" "5.1.3"
  dependencies:
    "glob" "^8.0.1"
    "ignore-walk" "^5.0.1"
    "npm-bundled" "^2.0.0"
    "npm-normalize-package-bin" "^2.0.0"

"npm-pick-manifest@^7.0.0", "npm-pick-manifest@^7.0.2":
  "version" "7.0.2"
  dependencies:
    "npm-install-checks" "^5.0.0"
    "npm-normalize-package-bin" "^2.0.0"
    "npm-package-arg" "^9.0.0"
    "semver" "^7.3.5"

"npm-profile@^6.2.0":
  "version" "6.2.1"
  dependencies:
    "npm-registry-fetch" "^13.0.1"
    "proc-log" "^2.0.0"

"npm-registry-fetch@^13.0.0", "npm-registry-fetch@^13.0.1", "npm-registry-fetch@^13.3.1":
  "version" "13.3.1"
  dependencies:
    "make-fetch-happen" "^10.0.6"
    "minipass" "^3.1.6"
    "minipass-fetch" "^2.0.3"
    "minipass-json-stream" "^1.0.1"
    "minizlib" "^2.1.2"
    "npm-package-arg" "^9.0.1"
    "proc-log" "^2.0.0"

"npm-run-path@^4.0.1":
  "integrity" "sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw=="
  "resolved" "https://registry.npmmirror.com/npm-run-path/-/npm-run-path-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "path-key" "^3.0.0"

"npm-user-validate@^1.0.1":
  "version" "1.0.1"

"npm@^8.19.4":
  "integrity" "sha512-3HANl8i9DKnUA89P4KEgVNN28EjSeDCmvEqbzOAuxCFDzdBZzjUl99zgnGpOUumvW5lvJo2HKcjrsc+tfyv1Hw=="
  "resolved" "https://registry.npmmirror.com/npm/-/npm-8.19.4.tgz"
  "version" "8.19.4"
  dependencies:
    "@isaacs/string-locale-compare" "^1.1.0"
    "@npmcli/arborist" "^5.6.3"
    "@npmcli/ci-detect" "^2.0.0"
    "@npmcli/config" "^4.2.1"
    "@npmcli/fs" "^2.1.0"
    "@npmcli/map-workspaces" "^2.0.3"
    "@npmcli/package-json" "^2.0.0"
    "@npmcli/run-script" "^4.2.1"
    "abbrev" "~1.1.1"
    "archy" "~1.0.0"
    "cacache" "^16.1.3"
    "chalk" "^4.1.2"
    "chownr" "^2.0.0"
    "cli-columns" "^4.0.0"
    "cli-table3" "^0.6.2"
    "columnify" "^1.6.0"
    "fastest-levenshtein" "^1.0.12"
    "fs-minipass" "^2.1.0"
    "glob" "^8.0.1"
    "graceful-fs" "^4.2.10"
    "hosted-git-info" "^5.2.1"
    "ini" "^3.0.1"
    "init-package-json" "^3.0.2"
    "is-cidr" "^4.0.2"
    "json-parse-even-better-errors" "^2.3.1"
    "libnpmaccess" "^6.0.4"
    "libnpmdiff" "^4.0.5"
    "libnpmexec" "^4.0.14"
    "libnpmfund" "^3.0.5"
    "libnpmhook" "^8.0.4"
    "libnpmorg" "^4.0.4"
    "libnpmpack" "^4.1.3"
    "libnpmpublish" "^6.0.5"
    "libnpmsearch" "^5.0.4"
    "libnpmteam" "^4.0.4"
    "libnpmversion" "^3.0.7"
    "make-fetch-happen" "^10.2.0"
    "minimatch" "^5.1.0"
    "minipass" "^3.1.6"
    "minipass-pipeline" "^1.2.4"
    "mkdirp" "^1.0.4"
    "mkdirp-infer-owner" "^2.0.0"
    "ms" "^2.1.2"
    "node-gyp" "^9.1.0"
    "nopt" "^6.0.0"
    "npm-audit-report" "^3.0.0"
    "npm-install-checks" "^5.0.0"
    "npm-package-arg" "^9.1.0"
    "npm-pick-manifest" "^7.0.2"
    "npm-profile" "^6.2.0"
    "npm-registry-fetch" "^13.3.1"
    "npm-user-validate" "^1.0.1"
    "npmlog" "^6.0.2"
    "opener" "^1.5.2"
    "p-map" "^4.0.0"
    "pacote" "^13.6.2"
    "parse-conflict-json" "^2.0.2"
    "proc-log" "^2.0.1"
    "qrcode-terminal" "^0.12.0"
    "read" "~1.0.7"
    "read-package-json" "^5.0.2"
    "read-package-json-fast" "^2.0.3"
    "readdir-scoped-modules" "^1.1.0"
    "rimraf" "^3.0.2"
    "semver" "^7.3.7"
    "ssri" "^9.0.1"
    "tar" "^6.1.11"
    "text-table" "~0.2.0"
    "tiny-relative-date" "^1.3.0"
    "treeverse" "^2.0.0"
    "validate-npm-package-name" "^4.0.0"
    "which" "^2.0.2"
    "write-file-atomic" "^4.0.1"

"npmlog@^6.0.0", "npmlog@^6.0.2":
  "version" "6.0.2"
  dependencies:
    "are-we-there-yet" "^3.0.0"
    "console-control-strings" "^1.1.0"
    "gauge" "^4.0.3"
    "set-blocking" "^2.0.0"

"once@^1.3.0":
  "integrity" "sha1-WDsap3WWHUsROsF9nFC6753Xa9E="
  "resolved" "https://registry.npmmirror.com/once/download/once-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "wrappy" "1"

"onetime@^5.1.2":
  "integrity" "sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg=="
  "resolved" "https://registry.npmmirror.com/onetime/-/onetime-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "mimic-fn" "^2.1.0"

"opener@^1.5.2":
  "integrity" "sha1-XTfh81B3udysQwE3InGv3rKhNZg="
  "resolved" "https://registry.npmmirror.com/opener/download/opener-1.5.2.tgz"
  "version" "1.5.2"

"p-limit@^2.2.0":
  "integrity" "sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w=="
  "resolved" "https://registry.npmmirror.com/p-limit/-/p-limit-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "p-try" "^2.0.0"

"p-limit@^3.0.2":
  "integrity" "sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ=="
  "resolved" "https://registry.npmmirror.com/p-limit/-/p-limit-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "yocto-queue" "^0.1.0"

"p-locate@^4.1.0":
  "integrity" "sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A=="
  "resolved" "https://registry.npmmirror.com/p-locate/-/p-locate-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "p-limit" "^2.2.0"

"p-locate@^5.0.0":
  "integrity" "sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw=="
  "resolved" "https://registry.npmmirror.com/p-locate/-/p-locate-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "p-limit" "^3.0.2"

"p-map@^4.0.0":
  "integrity" "sha1-uy+Vpe2i7BaOySdOBqdHw+KQTSs="
  "resolved" "https://registry.npmmirror.com/p-map/download/p-map-4.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fp-map%2Fdownload%2Fp-map-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "aggregate-error" "^3.0.0"

"p-try@^2.0.0":
  "integrity" "sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ=="
  "resolved" "https://registry.npmmirror.com/p-try/-/p-try-2.2.0.tgz"
  "version" "2.2.0"

"pacote@^13.0.3", "pacote@^13.6.1", "pacote@^13.6.2":
  "version" "13.6.2"
  dependencies:
    "@npmcli/git" "^3.0.0"
    "@npmcli/installed-package-contents" "^1.0.7"
    "@npmcli/promise-spawn" "^3.0.0"
    "@npmcli/run-script" "^4.1.0"
    "cacache" "^16.0.0"
    "chownr" "^2.0.0"
    "fs-minipass" "^2.1.0"
    "infer-owner" "^1.0.4"
    "minipass" "^3.1.6"
    "mkdirp" "^1.0.4"
    "npm-package-arg" "^9.0.0"
    "npm-packlist" "^5.1.0"
    "npm-pick-manifest" "^7.0.0"
    "npm-registry-fetch" "^13.0.1"
    "proc-log" "^2.0.0"
    "promise-retry" "^2.0.1"
    "read-package-json" "^5.0.0"
    "read-package-json-fast" "^2.0.3"
    "rimraf" "^3.0.2"
    "ssri" "^9.0.0"
    "tar" "^6.1.11"

"parent-module@^1.0.0":
  "integrity" "sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g=="
  "resolved" "https://registry.npmmirror.com/parent-module/-/parent-module-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "callsites" "^3.0.0"

"parse-conflict-json@^2.0.1", "parse-conflict-json@^2.0.2":
  "version" "2.0.2"
  dependencies:
    "json-parse-even-better-errors" "^2.3.1"
    "just-diff" "^5.0.1"
    "just-diff-apply" "^5.2.0"

"parse-json@^5.0.0":
  "integrity" "sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg=="
  "resolved" "https://registry.npmmirror.com/parse-json/-/parse-json-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "error-ex" "^1.3.1"
    "json-parse-even-better-errors" "^2.3.0"
    "lines-and-columns" "^1.1.6"

"path-exists@^4.0.0":
  "integrity" "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w=="
  "resolved" "https://registry.npmmirror.com/path-exists/-/path-exists-4.0.0.tgz"
  "version" "4.0.0"

"path-is-absolute@^1.0.0":
  "integrity" "sha1-F0uSaHNVNP+8es5r9TpanhtcX18="
  "resolved" "https://registry.npmmirror.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz"
  "version" "1.0.1"

"path-key@^3.0.0", "path-key@^3.1.0":
  "integrity" "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q=="
  "resolved" "https://registry.npmmirror.com/path-key/-/path-key-3.1.1.tgz"
  "version" "3.1.1"

"path-parse@^1.0.7":
  "integrity" "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw=="
  "resolved" "https://registry.npmmirror.com/path-parse/-/path-parse-1.0.7.tgz"
  "version" "1.0.7"

"path-type@^4.0.0":
  "integrity" "sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw=="
  "resolved" "https://registry.npmmirror.com/path-type/-/path-type-4.0.0.tgz"
  "version" "4.0.0"

"postcss-selector-parser@^6.0.10":
  "version" "6.0.10"
  dependencies:
    "cssesc" "^3.0.0"
    "util-deprecate" "^1.0.2"

"proc-log@^2.0.0", "proc-log@^2.0.1":
  "version" "2.0.1"

"promise-all-reject-late@^1.0.0":
  "version" "1.0.1"

"promise-call-limit@^1.0.1":
  "version" "1.0.1"

"promise-inflight@^1.0.1":
  "version" "1.0.1"

"promise-retry@^2.0.1":
  "version" "2.0.1"
  dependencies:
    "err-code" "^2.0.2"
    "retry" "^0.12.0"

"promzard@^0.3.0":
  "version" "0.3.0"
  dependencies:
    "read" "1"

"punycode@^2.1.0":
  "integrity" "sha512-rRV+zQD8tVFys26lAGR9WUuS4iUAngJScM+ZRSKtvl5tKeZ2t5bvdNFdNHBW9FWR4guGHlgmsZ1G7BSm2wTbuA=="
  "resolved" "https://registry.npmmirror.com/punycode/-/punycode-2.3.0.tgz"
  "version" "2.3.0"

"q@^1.5.1":
  "integrity" "sha512-kV/CThkXo6xyFEZUugw/+pIOywXcDbFYgSct5cT3gqlbkBE1SJdwy6UQoZvodiWF/ckQLZyDE/Bu1M6gVu5lVw=="
  "resolved" "https://registry.npmmirror.com/q/-/q-1.5.1.tgz"
  "version" "1.5.1"

"qrcode-terminal@^0.12.0":
  "version" "0.12.0"

"quick-lru@^4.0.1":
  "integrity" "sha512-ARhCpm70fzdcvNQfPoy49IaanKkTlRWF2JMzqhcJbhSFRZv7nPTvZJdcY7301IPmvW+/p0RgIWnQDLJxifsQ7g=="
  "resolved" "https://registry.npmmirror.com/quick-lru/-/quick-lru-4.0.1.tgz"
  "version" "4.0.1"

"read-cmd-shim@^3.0.0":
  "version" "3.0.0"

"read-package-json-fast@^2.0.2", "read-package-json-fast@^2.0.3":
  "version" "2.0.3"
  dependencies:
    "json-parse-even-better-errors" "^2.3.0"
    "npm-normalize-package-bin" "^1.0.1"

"read-package-json@^5.0.0", "read-package-json@^5.0.2":
  "version" "5.0.2"
  dependencies:
    "glob" "^8.0.1"
    "json-parse-even-better-errors" "^2.3.1"
    "normalize-package-data" "^4.0.0"
    "npm-normalize-package-bin" "^2.0.0"

"read-pkg-up@^7.0.1":
  "integrity" "sha512-zK0TB7Xd6JpCLmlLmufqykGE+/TlOePD6qKClNW7hHDKFh/J7/7gCWGR7joEQEW1bKq3a3yUZSObOoWLFQ4ohg=="
  "resolved" "https://registry.npmmirror.com/read-pkg-up/-/read-pkg-up-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "find-up" "^4.1.0"
    "read-pkg" "^5.2.0"
    "type-fest" "^0.8.1"

"read-pkg@^5.2.0":
  "integrity" "sha512-Ug69mNOpfvKDAc2Q8DRpMjjzdtrnv9HcSMX+4VsZxD1aZ6ZzrIE7rlzXBtWTyhULSMKg076AW6WR5iZpD0JiOg=="
  "resolved" "https://registry.npmmirror.com/read-pkg/-/read-pkg-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "@types/normalize-package-data" "^2.4.0"
    "normalize-package-data" "^2.5.0"
    "parse-json" "^5.0.0"
    "type-fest" "^0.6.0"

"read@^1.0.7", "read@~1.0.7", "read@1":
  "version" "1.0.7"
  dependencies:
    "mute-stream" "~0.0.4"

"readable-stream@^3.0.0", "readable-stream@3":
  "integrity" "sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA=="
  "resolved" "https://registry.npmmirror.com/readable-stream/-/readable-stream-3.6.2.tgz"
  "version" "3.6.2"
  dependencies:
    "inherits" "^2.0.3"
    "string_decoder" "^1.1.1"
    "util-deprecate" "^1.0.1"

"readable-stream@^3.6.0":
  "version" "3.6.0"
  dependencies:
    "inherits" "^2.0.3"
    "string_decoder" "^1.1.1"
    "util-deprecate" "^1.0.1"

"readdir-scoped-modules@^1.1.0":
  "version" "1.1.0"
  dependencies:
    "debuglog" "^1.0.1"
    "dezalgo" "^1.0.0"
    "graceful-fs" "^4.1.2"
    "once" "^1.3.0"

"redent@^3.0.0":
  "integrity" "sha512-6tDA8g98We0zd0GvVeMT9arEOnTw9qM03L9cJXaCjrip1OO764RDBLBfrB4cwzNGDj5OA5ioymC9GkizgWJDUg=="
  "resolved" "https://registry.npmmirror.com/redent/-/redent-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "indent-string" "^4.0.0"
    "strip-indent" "^3.0.0"

"require-directory@^2.1.1":
  "integrity" "sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q=="
  "resolved" "https://registry.npmmirror.com/require-directory/-/require-directory-2.1.1.tgz"
  "version" "2.1.1"

"require-from-string@^2.0.2":
  "integrity" "sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw=="
  "resolved" "https://registry.npmmirror.com/require-from-string/-/require-from-string-2.0.2.tgz"
  "version" "2.0.2"

"resolve-from@^4.0.0":
  "integrity" "sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g=="
  "resolved" "https://registry.npmmirror.com/resolve-from/-/resolve-from-4.0.0.tgz"
  "version" "4.0.0"

"resolve-from@^5.0.0", "resolve-from@5.0.0":
  "integrity" "sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw=="
  "resolved" "https://registry.npmmirror.com/resolve-from/-/resolve-from-5.0.0.tgz"
  "version" "5.0.0"

"resolve-global@^1.0.0", "resolve-global@1.0.0":
  "integrity" "sha512-zFa12V4OLtT5XUX/Q4VLvTfBf+Ok0SPc1FNGM/z9ctUdiU618qwKpWnd0CHs3+RqROfyEg/DhuHbMWYqcgljEw=="
  "resolved" "https://registry.npmmirror.com/resolve-global/-/resolve-global-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "global-dirs" "^0.1.1"

"resolve@^1.10.0":
  "integrity" "sha512-Sb+mjNHOULsBv818T40qSPeRiuWLyaGMa5ewydRLFimneixmVy2zdivRl+AF6jaYPC8ERxGDmFSiqui6SfPd+g=="
  "resolved" "https://registry.npmmirror.com/resolve/-/resolve-1.22.2.tgz"
  "version" "1.22.2"
  dependencies:
    "is-core-module" "^2.11.0"
    "path-parse" "^1.0.7"
    "supports-preserve-symlinks-flag" "^1.0.0"

"retry@^0.12.0":
  "version" "0.12.0"

"rimraf@^3.0.0", "rimraf@^3.0.2":
  "integrity" "sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho="
  "resolved" "https://registry.npmmirror.com/rimraf/download/rimraf-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "glob" "^7.1.3"

"safe-buffer@~5.2.0":
  "integrity" "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ=="
  "resolved" "https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.2.1.tgz"
  "version" "5.2.1"

"safer-buffer@>= 2.1.2 < 3.0.0":
  "integrity" "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo="
  "resolved" "https://registry.npmmirror.com/safer-buffer/download/safer-buffer-2.1.2.tgz"
  "version" "2.1.2"

"semver@^7.0.0", "semver@^7.1.1", "semver@^7.3.5", "semver@^7.3.7":
  "version" "7.3.7"
  dependencies:
    "lru-cache" "^6.0.0"

"semver@^7.3.4", "semver@7.5.0":
  "integrity" "sha512-+XC0AD/R7Q2mPSRuy2Id0+CGTZ98+8f+KvwirxOKIEyid+XSx6HbC63p+O4IndTHuX5Z+JxQ0TghCkO5Cg/2HA=="
  "resolved" "https://registry.npmmirror.com/semver/-/semver-7.5.0.tgz"
  "version" "7.5.0"
  dependencies:
    "lru-cache" "^6.0.0"

"semver@2 || 3 || 4 || 5":
  "integrity" "sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ=="
  "resolved" "https://registry.npmmirror.com/semver/-/semver-5.7.1.tgz"
  "version" "5.7.1"

"set-blocking@^2.0.0":
  "integrity" "sha1-BF+XgtARrppoA93TgrJDkrPYkPc="
  "resolved" "https://registry.npmmirror.com/set-blocking/download/set-blocking-2.0.0.tgz"
  "version" "2.0.0"

"shebang-command@^2.0.0":
  "integrity" "sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA=="
  "resolved" "https://registry.npmmirror.com/shebang-command/-/shebang-command-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "shebang-regex" "^3.0.0"

"shebang-regex@^3.0.0":
  "integrity" "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A=="
  "resolved" "https://registry.npmmirror.com/shebang-regex/-/shebang-regex-3.0.0.tgz"
  "version" "3.0.0"

"signal-exit@^3.0.3":
  "integrity" "sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ=="
  "resolved" "https://registry.npmmirror.com/signal-exit/-/signal-exit-3.0.7.tgz"
  "version" "3.0.7"

"signal-exit@^3.0.7":
  "version" "3.0.7"

"smart-buffer@^4.2.0":
  "version" "4.2.0"

"socks-proxy-agent@^7.0.0":
  "version" "7.0.0"
  dependencies:
    "agent-base" "^6.0.2"
    "debug" "^4.3.3"
    "socks" "^2.6.2"

"socks@^2.6.2":
  "version" "2.7.0"
  dependencies:
    "ip" "^2.0.0"
    "smart-buffer" "^4.2.0"

"spdx-correct@^3.0.0":
  "integrity" "sha512-kN9dJbvnySHULIluDHy32WHRUu3Og7B9sbY7tsFLctQkIqnMh3hErYgdMjTYuqmcXX+lK5T1lnUt3G7zNswmZA=="
  "resolved" "https://registry.npmmirror.com/spdx-correct/-/spdx-correct-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "spdx-expression-parse" "^3.0.0"
    "spdx-license-ids" "^3.0.0"

"spdx-exceptions@^2.1.0":
  "integrity" "sha512-/tTrYOC7PPI1nUAgx34hUpqXuyJG+DTHJTnIULG4rDygi4xu/tfgmq1e1cIRwRzwZgo4NLySi+ricLkZkw4i5A=="
  "resolved" "https://registry.npmmirror.com/spdx-exceptions/-/spdx-exceptions-2.3.0.tgz"
  "version" "2.3.0"

"spdx-expression-parse@^3.0.0":
  "integrity" "sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q=="
  "resolved" "https://registry.npmmirror.com/spdx-expression-parse/-/spdx-expression-parse-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "spdx-exceptions" "^2.1.0"
    "spdx-license-ids" "^3.0.0"

"spdx-license-ids@^3.0.0":
  "integrity" "sha512-XkD+zwiqXHikFZm4AX/7JSCXA98U5Db4AFd5XUg/+9UNtnH75+Z9KxtpYiJZx36mUDVOwH83pl7yvCer6ewM3w=="
  "resolved" "https://registry.npmmirror.com/spdx-license-ids/-/spdx-license-ids-3.0.13.tgz"
  "version" "3.0.13"

"split2@^3.0.0":
  "integrity" "sha512-9NThjpgZnifTkJpzTZ7Eue85S49QwpNhZTq6GRJwObb6jnLFNGB7Qm73V5HewTROPyxD0C29xqmaI68bQtV+hg=="
  "resolved" "https://registry.npmmirror.com/split2/-/split2-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "readable-stream" "^3.0.0"

"ssri@^9.0.0", "ssri@^9.0.1":
  "version" "9.0.1"
  dependencies:
    "minipass" "^3.1.1"

"string_decoder@^1.1.1":
  "integrity" "sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA=="
  "resolved" "https://registry.npmmirror.com/string_decoder/-/string_decoder-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "safe-buffer" "~5.2.0"

"string-width@^1.0.2 || 2 || 3 || 4":
  "integrity" "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA="
  "resolved" "https://registry.npmmirror.com/string-width/download/string-width-4.2.3.tgz?cache=0&sync_timestamp=1632421309919&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstring-width%2Fdownload%2Fstring-width-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "emoji-regex" "^8.0.0"
    "is-fullwidth-code-point" "^3.0.0"
    "strip-ansi" "^6.0.1"

"string-width@^4.1.0", "string-width@^4.2.0", "string-width@^4.2.3":
  "integrity" "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g=="
  "resolved" "https://registry.npmmirror.com/string-width/-/string-width-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "emoji-regex" "^8.0.0"
    "is-fullwidth-code-point" "^3.0.0"
    "strip-ansi" "^6.0.1"

"strip-ansi@^6.0.0", "strip-ansi@^6.0.1":
  "integrity" "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A=="
  "resolved" "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "ansi-regex" "^5.0.1"

"strip-final-newline@^2.0.0":
  "integrity" "sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA=="
  "resolved" "https://registry.npmmirror.com/strip-final-newline/-/strip-final-newline-2.0.0.tgz"
  "version" "2.0.0"

"strip-indent@^3.0.0":
  "integrity" "sha512-laJTa3Jb+VQpaC6DseHhF7dXVqHTfJPCRDaEbid/drOhgitgYku/letMUqOXFoWV0zIIUbjpdH2t+tYj4bQMRQ=="
  "resolved" "https://registry.npmmirror.com/strip-indent/-/strip-indent-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "min-indent" "^1.0.0"

"supports-color@^5.3.0":
  "integrity" "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow=="
  "resolved" "https://registry.npmmirror.com/supports-color/-/supports-color-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "has-flag" "^3.0.0"

"supports-color@^7.1.0":
  "integrity" "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw=="
  "resolved" "https://registry.npmmirror.com/supports-color/-/supports-color-7.2.0.tgz"
  "version" "7.2.0"
  dependencies:
    "has-flag" "^4.0.0"

"supports-preserve-symlinks-flag@^1.0.0":
  "integrity" "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w=="
  "resolved" "https://registry.npmmirror.com/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
  "version" "1.0.0"

"tar@^6.1.0", "tar@^6.1.11", "tar@^6.1.2":
  "version" "6.1.11"
  dependencies:
    "chownr" "^2.0.0"
    "fs-minipass" "^2.0.0"
    "minipass" "^3.0.0"
    "minizlib" "^2.1.1"
    "mkdirp" "^1.0.3"
    "yallist" "^4.0.0"

"text-extensions@^1.0.0":
  "integrity" "sha512-wiBrwC1EhBelW12Zy26JeOUkQ5mRu+5o8rpsJk5+2t+Y5vE7e842qtZDQ2g1NpX/29HdyFeJ4nSIhI47ENSxlQ=="
  "resolved" "https://registry.npmmirror.com/text-extensions/-/text-extensions-1.9.0.tgz"
  "version" "1.9.0"

"text-table@~0.2.0":
  "version" "0.2.0"

"through@>=2.2.7 <3":
  "integrity" "sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg=="
  "resolved" "https://registry.npmmirror.com/through/-/through-2.3.8.tgz"
  "version" "2.3.8"

"through2@^4.0.0":
  "integrity" "sha512-iOqSav00cVxEEICeD7TjLB1sueEL+81Wpzp2bY17uZjZN0pWZPuo4suZ/61VujxmqSGFfgOcNuTZ85QJwNZQpw=="
  "resolved" "https://registry.npmmirror.com/through2/-/through2-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "readable-stream" "3"

"tiny-relative-date@^1.3.0":
  "version" "1.3.0"

"treeverse@^2.0.0":
  "version" "2.0.0"

"trim-newlines@^3.0.0":
  "integrity" "sha512-c1PTsA3tYrIsLGkJkzHF+w9F2EyxfXGo4UyJc4pFL++FMjnq0HJS69T3M7d//gKrFKwy429bouPescbjecU+Zw=="
  "resolved" "https://registry.npmmirror.com/trim-newlines/-/trim-newlines-3.0.1.tgz"
  "version" "3.0.1"

"ts-node@^10.8.1", "ts-node@>=10":
  "integrity" "sha512-NtVysVPkxxrwFGUUxGYhfux8k78pQB3JqYBXlLRZgdGUqTO5wU/UyHop5p70iEbGhB7q5KmiZiU0Y3KlJrScEw=="
  "resolved" "https://registry.npmmirror.com/ts-node/-/ts-node-10.9.1.tgz"
  "version" "10.9.1"
  dependencies:
    "@cspotcode/source-map-support" "^0.8.0"
    "@tsconfig/node10" "^1.0.7"
    "@tsconfig/node12" "^1.0.7"
    "@tsconfig/node14" "^1.0.0"
    "@tsconfig/node16" "^1.0.2"
    "acorn" "^8.4.1"
    "acorn-walk" "^8.1.1"
    "arg" "^4.1.0"
    "create-require" "^1.1.0"
    "diff" "^4.0.1"
    "make-error" "^1.1.1"
    "v8-compile-cache-lib" "^3.0.1"
    "yn" "3.1.1"

"type-fest@^0.18.0":
  "integrity" "sha512-OIAYXk8+ISY+qTOwkHtKqzAuxchoMiD9Udx+FSGQDuiRR+PJKJHc2NJAXlbhkGwTt/4/nKZxELY1w3ReWOL8mw=="
  "resolved" "https://registry.npmmirror.com/type-fest/-/type-fest-0.18.1.tgz"
  "version" "0.18.1"

"type-fest@^0.6.0":
  "integrity" "sha512-q+MB8nYR1KDLrgr4G5yemftpMC7/QLqVndBmEEdqzmNj5dcFOO4Oo8qlwZE3ULT3+Zim1F8Kq4cBnikNhlCMlg=="
  "resolved" "https://registry.npmmirror.com/type-fest/-/type-fest-0.6.0.tgz"
  "version" "0.6.0"

"type-fest@^0.8.1":
  "integrity" "sha512-4dbzIzqvjtgiM5rw1k5rEHtBANKmdudhGyBEajN01fEyhaAIhsoKNy6y7+IN93IfpFtwY9iqi7kD+xwKhQsNJA=="
  "resolved" "https://registry.npmmirror.com/type-fest/-/type-fest-0.8.1.tgz"
  "version" "0.8.1"

"typescript@^4.6.4 || ^5.0.0", "typescript@>=2.7", "typescript@>=3":
  "integrity" "sha512-cW9T5W9xY37cc+jfEnaUvX91foxtHkza3Nw3wkoF4sSlKn0MONdkdEndig/qPBWXNkmplh3NzayQzCiHM4/hqw=="
  "resolved" "https://registry.npmmirror.com/typescript/-/typescript-5.0.4.tgz"
  "version" "5.0.4"

"unique-filename@^2.0.0":
  "version" "2.0.1"
  dependencies:
    "unique-slug" "^3.0.0"

"unique-slug@^3.0.0":
  "version" "3.0.0"
  dependencies:
    "imurmurhash" "^0.1.4"

"universalify@^2.0.0":
  "integrity" "sha512-hAZsKq7Yy11Zu1DE0OzWjw7nnLZmJZYTDZZyEFHZdUhV8FkH5MCfoU1XMaxXovpyW5nq5scPqq0ZDP9Zyl04oQ=="
  "resolved" "https://registry.npmmirror.com/universalify/-/universalify-2.0.0.tgz"
  "version" "2.0.0"

"uri-js@^4.2.2":
  "integrity" "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg=="
  "resolved" "https://registry.npmmirror.com/uri-js/-/uri-js-4.4.1.tgz"
  "version" "4.4.1"
  dependencies:
    "punycode" "^2.1.0"

"util-deprecate@^1.0.1":
  "integrity" "sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw=="
  "resolved" "https://registry.npmmirror.com/util-deprecate/-/util-deprecate-1.0.2.tgz"
  "version" "1.0.2"

"util-deprecate@^1.0.2":
  "integrity" "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8="
  "resolved" "https://registry.npmmirror.com/util-deprecate/download/util-deprecate-1.0.2.tgz"
  "version" "1.0.2"

"v8-compile-cache-lib@^3.0.1":
  "integrity" "sha512-wa7YjyUGfNZngI/vtK0UHAN+lgDCxBPCylVXGp0zu59Fz5aiGtNXaq3DhIov063MorB+VfufLh3JlF2KdTK3xg=="
  "resolved" "https://registry.npmmirror.com/v8-compile-cache-lib/-/v8-compile-cache-lib-3.0.1.tgz"
  "version" "3.0.1"

"validate-npm-package-license@^3.0.1":
  "integrity" "sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew=="
  "resolved" "https://registry.npmmirror.com/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "spdx-correct" "^3.0.0"
    "spdx-expression-parse" "^3.0.0"

"validate-npm-package-license@^3.0.4":
  "version" "3.0.4"
  dependencies:
    "spdx-correct" "^3.0.0"
    "spdx-expression-parse" "^3.0.0"

"validate-npm-package-name@^4.0.0":
  "version" "4.0.0"
  dependencies:
    "builtins" "^5.0.0"

"walk-up-path@^1.0.0":
  "version" "1.0.0"

"wcwidth@^1.0.0":
  "version" "1.0.1"
  dependencies:
    "defaults" "^1.0.3"

"which@^2.0.1":
  "integrity" "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA=="
  "resolved" "https://registry.npmmirror.com/which/-/which-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "isexe" "^2.0.0"

"which@^2.0.2":
  "integrity" "sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE="
  "resolved" "https://registry.npmmirror.com/which/download/which-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "isexe" "^2.0.0"

"wide-align@^1.1.5":
  "version" "1.1.5"
  dependencies:
    "string-width" "^1.0.2 || 2 || 3 || 4"

"wrap-ansi@^7.0.0":
  "integrity" "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q=="
  "resolved" "https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  "version" "7.0.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"

"wrappy@1":
  "integrity" "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8="
  "resolved" "https://registry.npmmirror.com/wrappy/download/wrappy-1.0.2.tgz?cache=0&sync_timestamp=1619133505879&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwrappy%2Fdownload%2Fwrappy-1.0.2.tgz"
  "version" "1.0.2"

"write-file-atomic@^4.0.0", "write-file-atomic@^4.0.1":
  "version" "4.0.2"
  dependencies:
    "imurmurhash" "^0.1.4"
    "signal-exit" "^3.0.7"

"y18n@^5.0.5":
  "integrity" "sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA=="
  "resolved" "https://registry.npmmirror.com/y18n/-/y18n-5.0.8.tgz"
  "version" "5.0.8"

"yallist@^4.0.0":
  "integrity" "sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A=="
  "resolved" "https://registry.npmmirror.com/yallist/-/yallist-4.0.0.tgz"
  "version" "4.0.0"

"yargs-parser@^20.2.3":
  "integrity" "sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w=="
  "resolved" "https://registry.npmmirror.com/yargs-parser/-/yargs-parser-20.2.9.tgz"
  "version" "20.2.9"

"yargs-parser@^21.1.1":
  "integrity" "sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw=="
  "resolved" "https://registry.npmmirror.com/yargs-parser/-/yargs-parser-21.1.1.tgz"
  "version" "21.1.1"

"yargs@^17.0.0":
  "integrity" "sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w=="
  "resolved" "https://registry.npmmirror.com/yargs/-/yargs-17.7.2.tgz"
  "version" "17.7.2"
  dependencies:
    "cliui" "^8.0.1"
    "escalade" "^3.1.1"
    "get-caller-file" "^2.0.5"
    "require-directory" "^2.1.1"
    "string-width" "^4.2.3"
    "y18n" "^5.0.5"
    "yargs-parser" "^21.1.1"

"yn@3.1.1":
  "integrity" "sha512-Ux4ygGWsu2c7isFWe8Yu1YluJmqVhxqK2cLXNQA5AcC3QfbGNpM7fu0Y8b/z16pXLnFxZYvWhd3fhBY9DLmC6Q=="
  "resolved" "https://registry.npmmirror.com/yn/-/yn-3.1.1.tgz"
  "version" "3.1.1"

"yocto-queue@^0.1.0":
  "integrity" "sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q=="
  "resolved" "https://registry.npmmirror.com/yocto-queue/-/yocto-queue-0.1.0.tgz"
  "version" "0.1.0"
