{"name": "distribution-operation-web", "version": "1.0.0", "description": "node server & web dev template", "keywords": ["node", "koa", "react", "framework", "front-end", "web"], "scripts": {"install": "sh scripts/install.sh", "dev": "cd web && npm run dev", "server": "cd server && npm run server", "dev:server": "cd dist && node src/index.js", "build": "sh scripts/build.sh", "web:build": "cd web && npm run build", "server:build": "cd server && npm run build", "build:test": "sh scripts/build-test.sh", "web:build:test": "cd web && npm run build:test", "server:build:test": "cd server && npm run build:test", "build:online": "sh scripts/build-online.sh", "web:build:online": "cd web && npm run build:online", "server:build:online": "cd server && npm run build:online", "clean": "sh scripts/clean.sh", "eslint": "cd web && npm run eslint", "eslint-stat": "cd web && npm run eslint-stat", "readmelint": "cd web && npm run readmelint", "postinstall": "git init && husky install && chmod 700 .husky/*"}, "repository": {"type": "git", "url": "https://git.yx.netease.com/sharkr/react-template/full"}, "license": "LGPL", "author": "the netesmail authors", "config": {"commitizen": {"path": "node_modules/cz-git"}}, "devDependencies": {"@tetris/commitlint-config": "^1.0.0", "commitlint": "^17.1.2", "cz-git": "^1.3.12", "husky": "^8.0.1"}, "dependencies": {"npm": "^8.19.4"}}