import { ITigerProxyOption } from '@tiger/proxy';
import BaseConfig from './config.base';
import { join } from 'path';
import { appLogger } from '@tiger/logger';
// @EnableApolloConfig('application') 这个注解给apollo配置中心的时候用
const productCode = 'distribution-operation';

export default class Config extends BaseConfig {
  loggerPath: string = join(this.rootPath, 'log');
  appProxyOptions: ITigerProxyOption = {
    target: 'http://test.yx.mail.netease.com',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path.replace(
        new RegExp(
          `^${this.contextPath}${this.xhrPrefix}/returnFirst`
        ),
        `${productCode}${this.contextPath}${this.xhrPrefix}/returnFirst`
      );
    }
  };


  lokiNodeOptions: ITigerProxyOption = {
    target: 'http://test.yx.mail.netease.com',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      appLogger.info('path=============' + path)
      appLogger.info(`^${this.contextPath}${this.xhrPrefix}/`)

      const url = path.replace(
        new RegExp(
          `^${this.contextPath}${this.xhrPrefix}`
        ),
        `/${productCode}/${this.contextPath}${this.xhrPrefix}`
      );

      appLogger.info(`url: =====${url}`)
      return url;
    }
  };


  umcProxyOptions: ITigerProxyOption = {
    target: 'http://************',
    changeOrigin: true,
    autoRewrite: true,
    headers: { Host: 'yxius.you.163.com' },
    rewrite: (path: string) => {
      appLogger.info('umcProxyOptions.path===== ' + path)
      const url = path.replace(
        new RegExp(
          `^${this.contextPath}${this.xhrPrefix}/userCenterManage`
        ),
        ''
      );

      appLogger.info('umcProxyOptions.url===== ' + url)
      return url
    }
  };

  distributionAdminProxyOptions: ITigerProxyOption = {
    target: 'http://test.yx.mail.netease.com',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path.replace(
        new RegExp(
          `^${this.contextPath}`
        ),
        ``
      );
    }
  };
  distributionShelvesProxyOptions: ITigerProxyOption = {
    target: 'http://test.yx.mail.netease.com',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path.replace(
        new RegExp(
          `^${this.contextPath}${this.xhrPrefix}`
        ),
        ''
      );
    }
  };
}
