import { ITigerProxyOption } from '@tiger/proxy';
import BaseConfig from './config.base';
import { appLogger } from '@tiger/logger';
import { join } from 'path';
import { AppModuleConfig } from './types';

// @EnableApolloConfig('application') 这个注解给apollo配置中心的时候用
export default class Config extends BaseConfig {
  loggerPath: string = join('/home/<USER>/', this.serviceCode);
  // app自己的转发配置，需要开发自己改成自己应用的 TODO
  appProxyOptions: ITigerProxyOption = {
    target: 'http://127.0.0.1:8550/proxy/test.distribution-combined-pic.service.mailsaas',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path.replace(
        new RegExp(
          `^${this.contextPath}${this.xhrPrefix}/returnFirst`
        ),
        ''
      );
    }
  };

  umcProxyOptions: ITigerProxyOption = {
    target: 'http://127.0.0.1:8550/proxy/test.yanxuan-ius.service.mailsaas',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path.replace(
        new RegExp(
          `^${this.contextPath}${this.xhrPrefix}/userCenterManage`
        ),
        ''
      );
    }
  };
  lokiNodeOptions: ITigerProxyOption = {
    target: 'http://127.0.0.1:8550/proxy/test.loki-node.service.mailsaas',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      appLogger.info(`lokiNodeOptions=====${path}`)
      const url = path.replace(
        new RegExp(
          `^${this.contextPath}${this.xhrPrefix}`
        ),
        ''
      );

      appLogger.info(`lokiNodeOptions=====${url}`)
      return url;
    }
  };
  distributionAdminProxyOptions: ITigerProxyOption = {
    target: 'http://127.0.0.1:8550/proxy/test.yanxuan-distribution-admin.service.mailsaas',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
        return path.replace(
            new RegExp(
                `^${this.contextPath}`
            ),
            ''
        );
    }
  };
  distributionShelvesProxyOptions: ITigerProxyOption = {
    target: 'http://127.0.0.1:8550/proxy/test.distribution-shelves-tool.service.mailsaas',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
        return path.replace(
            new RegExp(
                `^${this.contextPath}${this.xhrPrefix}`
            ),
            ''
        );
    }
  };

  modules: AppModuleConfig = {
    '@tiger/security': {
      enable: true,
      options: {
        csrf: true,
        'Strict-Transport-Security': true,
        'X-Frame-Options': true
      }
    },
    '@tiger/swagger': {
      enable: false
    }
  };
}
