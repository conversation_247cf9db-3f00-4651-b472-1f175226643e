import { TgModule } from '@tiger/boot';
import { UserModule } from './user/user.module';
import { TenantModule } from './tenant/tenant.module';
import { WorkflowModule } from './workflow/workflow.module';
// import { YXBussniessModule } from '@eagler/bussiness-components-node';

@TgModule({
    prefix: `/xhr`,
    // YXBussniessModule
    imports: [UserModule, TenantModule, WorkflowModule],
    controllers: []
})
export class XhrModule { }
