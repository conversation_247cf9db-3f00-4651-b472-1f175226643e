variables:
  SERVICE_CODE: 'distribution-operation-web'
  ARTIFACT_PATH: $CI_PROJECT_NAME.zip

image: $CI_IMAGE_FRONT_NODE_14

stages:
  - package
  - upload

################################
#    前置检查
################################
before_script:
  - preCheck

################################
#    编译打包(test)
################################
package-fed-test:
  stage: package
  script:
    - pwd
    - sh scripts/install-ci.sh
    - npm run eslint
    - npm run eslint-stat -- --service=$SERVICE_CODE --uid=$GITLAB_USER_EMAIL --branch=$CI_COMMIT_REF_NAME
    - npm run build:test
    - cp deploy/env/setenv_test.sh dist/setenv.sh
    - cd dist && zip -rq ../$ARTIFACT_PATH ./*
  tags:
    - ci-front
  artifacts:
    paths:
      - $ARTIFACT_PATH
      - web/test-code-temp
    expire_in: 1d
  cache:
    key: '$CI_PROJECT_NAME'
    paths:
      - web/node_modules/
      - server/node_modules/
      
  only:
    - /^(release|hotfix|dev|feature).*$/

################################
#    编译打包(online)
################################
package-fed-online:
  stage: package
  script:
    - pwd
    - sh scripts/install-ci.sh
    - npm run build:online
    - cp deploy/env/setenv_online.sh dist/setenv.sh
    - cd dist && zip -rq ../$ARTIFACT_PATH ./*
  tags:
    - ci-front
  artifacts:
    paths:
      - $ARTIFACT_PATH
    expire_in: 1d
  cache:
    key: '$CI_PROJECT_NAME'
    paths:
      - web/node_modules/
      - server/node_modules/
  only:
    - /^(release|hotfix).*$/

################################
#    编译打包(dev)
################################
package-fed-dev:
  stage: package
  script:
    - pwd
    - sh scripts/install-ci.sh
    - npm run build:betayun
    - cp deploy/env/setenv_betayun.sh dist/setenv.sh
    - cd dist && zip -rq ../$ARTIFACT_PATH ./*
  tags:
    - ci-front
  artifacts:
    paths:
      - $ARTIFACT_PATH
    expire_in: 1d
  cache:
    key: "$CI_PROJECT_NAME"
    paths:
      - server/node_modules/
      - web/node_modules/
  only:
    - betayun


################################
# 测试服上传代码制品自动发布  --autoDeploy=true --clusterId=2 // 2：云外测试环境的集群id
################################
upload_artifacts-test:
  stage: upload
  script:
    - pwd
    - version_tools time && CURRENT_TIMESTAMP=$(version_tools result)
    - version_tools version && PROJECT_VERSION=$(version_tools result)
    - ARTIFACT_VERSION="${PROJECT_VERSION}-${CI_COMMIT_REF_NAME##*/}-${CURRENT_TIMESTAMP}-${CI_PIPELINE_ID}"
    - eval opera truck $OPERA_ARGS --env=test --artifactPath=$ARTIFACT_PATH --artifactVersion=$ARTIFACT_VERSION --autoDeploy=true --clusterId=2

  tags:
    - ci-front
  only:
    - /^(release|hotfix|dev|feature).*$/
  dependencies:
    - package-fed-test

################################
# 线上传代码制品
################################
upload_artifacts-online:
  stage: upload
  script:
    - pwd
    - version_tools time && CURRENT_TIMESTAMP=$(version_tools result)
    - version_tools version && PROJECT_VERSION=$(version_tools result)
    - ARTIFACT_VERSION="${PROJECT_VERSION}-${CI_COMMIT_REF_NAME##*/}-${CURRENT_TIMESTAMP}-${CI_PIPELINE_ID}"
    - eval opera truck $OPERA_ARGS --env=online --artifactPath=$ARTIFACT_PATH --artifactVersion=$ARTIFACT_VERSION
  tags:
    - ci-front
  only:
    - /^(release|hotfix).*$/
  dependencies:
    - package-fed-online

################################
# 回归测试服上传代码制品  --autoDeploy=true --clusterId=0
################################
upload_artifacts-dev:
  stage: upload
  script:
    - pwd
    - version_tools time && CURRENT_TIMESTAMP=$(version_tools result)
    - version_tools version && PROJECT_VERSION=$(version_tools result)
    - ARTIFACT_VERSION="${PROJECT_VERSION}-${CI_COMMIT_REF_NAME##*/}-${CURRENT_TIMESTAMP}-${CI_PIPELINE_ID}"
    - eval opera truck $OPERA_ARGS --env=dev --artifactPath=$ARTIFACT_PATH --artifactVersion=$ARTIFACT_VERSION --module=dev --autoDeploy=true --clusterId=0
  tags:
    - betayun-runner
  only:
    - betayun
  dependencies:
    - package-fed-dev