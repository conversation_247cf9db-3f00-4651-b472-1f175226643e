import { BACK_IMAGE_URL } from "../consts"

export const getUrlParam = () => {
  const url = document.location.toString()
  let arrObj = url.split('?')
  const params = Object.create(null)
  if (arrObj.length > 1) {
    arrObj = arrObj[1].split('&')
    arrObj.forEach(item => {
      const newItem = item.split('=')
      params[newItem[0]] = decodeURI(newItem[1])
    })
  }
  return params
}

export const loadPicSize = (url: string): Promise<{ width: number, height: number }> => new Promise((resolve, reject) => {
  if (!url) reject('need url!')
  const img = new Image()
  img.src = url
  img.onload = () => {
    resolve({
      width: img.width,
      height: img.height
    })
  }
})

// 扩展模板数据，返回背景图imageUrl, 尺寸width，height
export const extendTemplateData = async (templateData) => {
  let { imageUrl, backImgUrl, templateSize } = templateData
  imageUrl = imageUrl || backImgUrl || BACK_IMAGE_URL[templateSize] || 'https://yanxuan.nosdn.127.net/static-union/165960430646c09c.png'

  const { width, height } = await loadPicSize(imageUrl)

  return {
    imageUrl,
    width,
    height,
  }
}
export const getQuery = (query: string): any => {
  const args: { [key: string]: string | number } = {};
  const pairs = query.replace(/\?/g, '').split('&');
  pairs.forEach(item => {
    const pos = item.indexOf('=');
    if (pos > -1) {
      const name = decodeURIComponent(item.substring(0, pos)).trim();
      const value = decodeURIComponent(item.substring(pos + 1)).trim();
      args[name] = value;
    }
  });
  return args;
};