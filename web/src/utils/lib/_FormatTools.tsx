/* 单位处理
 用于将rem转px，px转rem等过滤
*/
// import urlTools from '@/common/libs/_getUrlParam';
import { Tag } from 'antd';

import moment from 'moment';

interface ITime {
  value: number | string,
  format?: string,
}

class FormatTools {
  // constructor(parps) { 

  // }

  Time(param: ITime) {
    const { value, format } = param;
    const _format = format ?? 'YYYY-MM-DD HH:mm:ss';

    const str: string = moment(value).format(_format).valueOf();
    return str;

  }

  /**
 * 标签
 * @param arr 
 * @returns 
 */
  Tags(arr: string[]) {
    const DOM = arr?.map((item, index) => {
      return (
        // <span key={index}>
        //   <span>{item}</span>
        //   <Divider type="vertical" />
        // </span>
        <Tag key={index}>{item}</Tag>
      )
    })

    return DOM
  }

}

export default new FormatTools();

