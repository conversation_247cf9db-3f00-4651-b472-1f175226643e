/* 单位处理
 用于将rem转px，px转rem等过滤
*/
// import urlTools from '@/common/libs/_getUrlParam';

class UnitTools {
  constructor() {

    this.state = {
      baseSize: 100,
    };
  }

  /**
   *  px转rem
   * @param html
   * @returns {*|string}
   */
  pxToRem(html) {
    const array = html.match(/:\s*([1-9]\d*|0)?(\.\d*)?px/g);
    let str = html || '';
    const e = new Set(array);
    [...e].forEach((item) => {
      let unit = parseFloat(item.match(/\d+(\.\d+)?/g)) / 100;
      unit = `:${unit.toFixed(2)}rem`;
      const reg = new RegExp(item, 'g');
      str = str.replace(reg, unit);
    });
    return str;
  }

  /**
   *
   * @param html
   */
  remToPx(html) {
    const array = html.match(/:\s*([1-9]\d*|0)?(\.\d*)?rem/g);
    let str = html || '';
    const e = new Set(array);
    [...e].forEach((item) => {
      // 筛选出数字
      let unit = parseFloat(item.match(/\d+(\.\d+)?|(\.\d+)/g)) * 100;
      unit = `:${unit.toFixed(2)}px`;
      const reg = new RegExp(item, 'g');
      str = str.replace(reg, unit);
    });
    return str;
  }

  /**
   *  取指定字符串长度，并追加 ...
   * @param {*} str 
   * @param {*} max
   */
  subString(param){
    const { suffix,str,max} = param;
    if (str.length > max){
      return str.substring(0, max) + (!!suffix ? suffix:'...');
    }else{
      return str;
    }
  }



}

export default new UnitTools();

