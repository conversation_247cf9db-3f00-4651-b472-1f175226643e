/* 
  Image 的通用处理方法
*/
class _ImageTools {
  // constructor() {}

  getWH(url) {
    const result = {
      width: '0px',
      height: '0px'
    }
    if (!!url) {
      const img = new Image();
      img.src = url;
      result.width = `${img.width}px`;
      result.height = `${img.height}px`;
    }
    return result;
  }

  getWHPromiseByUrl(url) {
    return new Promise((resolve, reject) => {
      const result = {
        width: '0px',
        height: '0px',
      };
      if (!!url) {
        const image = new Image();
        image.addEventListener('load', () => {
          result.width = image.width;
          result.height = image.height;
          resolve(result);
        });
        image.addEventListener('error', () => {
          reject(new Error('图片加载失败，检查图片源'));
        });
        image.src = url;
      } else {
        resolve(result);
        // reject(new Error('Could not load image without url'));
      }
    });
  }

  async formatImageUrl(url) {
    if (!!url) {
      const size = await this.getWHPromiseByUrl(url);
      let _url = url;
      const opation = `_width=${size.width}&_height=${size.height}`;
      if (url.indexOf('?') < 0) {
        _url = `${url}?${opation}`;
      } else {
        _url = `${url.split('?')[0]}?${opation}`;
      }
      return _url;
    } else {
      return url;
    }


  }

  /**
   *  集中对图片后缀进行追加处理
   *  pageType 当前页面终端 1-》pc 2-》h5
   * @param {*} url 
   */
  addImageView(url, pageType) {
    const suffix = url.substring(url.lastIndexOf('.') + 1);
    let _url = url;

    // 只有h5才做处理
    if (pageType === 2 && ['jpg', 'png', 'jpeg', 'webp'].includes(suffix.toLocaleLowerCase())) {
      if (url.indexOf('?') > 0) {
        if (url.indexOf('imageView') > 0) {
          // 有追加，什么都不动
        } else {
          // 有携带参数，但是没有进行nos压缩
          _url = url + '&imageView';
        }
      } else {
        // 没有任何后缀
        _url = url + '?imageView';
      } 
    } 
    return _url
  }

}

const ImageTools =  new _ImageTools();


export {
  ImageTools
}
