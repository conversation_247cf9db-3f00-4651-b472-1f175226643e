/*
 * @Description: 渠道使用方法
 * @Author: chenxiaopan <EMAIL>
 * @Date: 2022-12-14 17:09:41
 */

// 获取item下skuId信息集合
export const getItemMap = (skuList: any[]) => {
  const itemMap = new Map()
  skuList.forEach((sku: any) => {
    const skuIds = itemMap.get(sku.itemId)
    if (skuIds) {
      itemMap.set(sku.itemId, skuIds.concat([sku.skuId]))
    } else {
      itemMap.set(sku.itemId, [sku.skuId])
    }
  })
  return itemMap
}
// 获取item下skuId信息集合
export const getAddProductParams = (itemId, itemCate: any) => {
  const idLen = itemCate.categoryIds?.length
  const categoryId = itemCate.categoryIds[idLen - 1]
  const params = {
    itemId,
    categoryId,
    fieldList: itemCate.fieldList.map(field => {
      const { id, name, type, rules, value, values } = field
      return { id, name, type, rules, value, values }
    })
  }
  return params
}

// 获取table的关键属性配置信息
export const getFieldList = (values: any[], itemCate) => {
  const cateWithFields = JSON.parse(JSON.stringify(itemCate))
  Object.keys(cateWithFields).forEach(key => {
    const formValue = values.filter(info => info.itemId === key)[0]
    const infos = cateWithFields[key]
    infos?.fieldList.forEach((field: any) => {
      if (['INPUT', 'SINGLECHECK'].includes(field.type)) {
        field.value = {
          value: formValue[field.id],
          attributes: field.value?.attributes || null,
          fieldMap: field.value?.fieldMap || null
        }
      } else if (['MULTICHECK', 'MULTIINPUT'].includes(field.type)) {
        field.values = formValue[field.id]
          ? formValue[field.id].map(info => {
              return {
                value: info,
                attributes: field.value?.attributes || null,
                fieldMap: field.value?.fieldMap || null
              }
            })
          : null
      } else if (['MULTICOMPLEX'].includes(field.type)) {
        field.values = getfieldMap(formValue[field.id], field.fieldList)
      } else {
        field.value = {
          value: formValue[field.id],
          attributes: field.value?.attributes || null,
          fieldMap: field.value?.fieldMap || null
        }
      }
    })
  })
  console.log('newItemCate', cateWithFields)

  return cateWithFields
}

const getfieldMap = (values: any[], fileList: any[]) => {
  const mapResList: any[] = []
  values.forEach(value => {
    const map = {}
    fileList.forEach(file => {
      const id = file.id
      map[id] = {
        ...file,
        value: {
          value: value[id]
        }
      }
    })
    mapResList.push({ fieldMap: map })
  })
  return mapResList
}
