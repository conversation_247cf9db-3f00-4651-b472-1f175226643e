
export const setLocalStorageByName = (name: string, obj: object | string) => {
  if (name && obj) {
    typeof obj === 'string' ? localStorage.setItem(name, obj) : localStorage.setItem(name, JSON.stringify(obj));
  }
  return obj;
};

export const getLocalStorageByName = (name: string) => {
  const shareData = localStorage.getItem(name);
  return shareData ? (typeof shareData === 'string' ? JSON.parse(shareData) : shareData) : null;
}

