
import React from 'react'
import { QuestionCircleOutlined } from '@ant-design/icons';
import { Form, Input, InputNumber, Tooltip, Radio, Select, Switch, Popover, Button, Row, Col } from 'antd';
import { SharkrUpload } from '@eagler/business-components';
import { IcacSingleUserSelect, IcacUser } from '@eagler/umc-select-web';



import { UnitTools } from './lib'


const FormItem = Form.Item;
const Option = Select.Option;



class CreateFormItem {
  state: any;
  constructor(param?: any) {


    this.state = {
      ...param,
      awardInfo: null
    };

  }


  /**
  * 简单实现state管理
  *
  * @param {*} json
  * @memberof createFormItem
  */
  setState = (json: any) => {
    this.state = {
      ...this.state,
      ...json
    }
  }


  /**
    * 为了更好的处理key格式，所以统一处理
    *
    * @memberof createFormItem
    */
  createKey = (param: any) => {

    const { name } = param;
    const _FormItemKey = name
    const _name = name
    return {
      _FormItemKey,
      _name
    }
  }


  /**
  * 格式化Label
  *
  * @memberof createFormItem
  */
  formatLabel = (param: any) => {
    const { label, info } = param;
    const _name = label;
    const _info = info;


    if (_info) {
      const _nameSub = UnitTools.subString({
        str: _name,
        max: 14,
        suffix: '..'
      })
      return (
        <span title={_name}>
          {_nameSub}&nbsp;
          <Tooltip title={_info}>
            <QuestionCircleOutlined translate={undefined} />
          </Tooltip>
        </span>
      );
    } else {
      const _nameSub = UnitTools.subString({
        str: _name,
        max: 14,
        suffix: '...'
      })

      return (
        <span title={_name}>
          {_nameSub}&nbsp;
        </span>
      )
    }
  }

  Input = (param: any) => {

    const { name, opations } = param;

    const TempKey = this.createKey({ name });
    const _FormItemKey = TempKey._FormItemKey;
    const _name = TempKey._name;


    const _label = this.formatLabel(param);

    const _formProps = {
      key: _FormItemKey,
      label: _label,
    }


    return (
      <FormItem
        {..._formProps}
        name={_name}
        {...opations}
       
      >
        {
          <Input />
        }
      </FormItem>
    )


  }

  TextArea = (param: any) => {

    const { name, opations } = param;

    const TempKey = this.createKey({ name });
    const _FormItemKey = TempKey._FormItemKey;
    const _name = TempKey._name;


    const _label = this.formatLabel(param);

    const _formProps = {
      key: _FormItemKey,
      label: _label,
    }


    return (
      <FormItem
        {..._formProps}
        name={_name}
        {...opations}
      >
        {
          <Input.TextArea />
        }
      </FormItem>
    )


  }


  Select = (param: any) => {
    const { name, opations, onChange, disabled, mode, placeholder, optionList } = param;

    const TempKey = this.createKey({ name });
    const _FormItemKey = TempKey._FormItemKey;
    const _name = TempKey._name;


    const _label = this.formatLabel(param);

    const _formProps = {
      key: _FormItemKey,
      label: _label,
    }

    const _Option: any = {
      placeholder: placeholder || '',
      disabled
    };
    if (onChange) {
      _Option.onChange = onChange;
    }
    if (mode) {
      _Option.mode = mode;
    }



    return (
      <FormItem
        {..._formProps}
        name={_name}
        {...opations}
       
      >
        <Select
          {..._Option}
          tokenSeparators={[',', ' ', '，', '.', '。', '/', '、']}
        >
          {
            optionList.map((d) => {
              return (
                <Option
                  value={parseInt(d.id, 10)}
                  key={`${_FormItemKey}_Select_${d.id}`}
                  disabled={d.disabled}
                >
                  {d.cname}
                </Option>
              )
            })
          }
        </Select>
      </FormItem>
    )

  }

  /**
   * 设置图片的返回值
   * @param e 
   * @returns 
   */
  normFile=(e:any)=>{
    if (Array.isArray(e)) {
      return e;
    }
    return e?.fileList;
    return e?.fileList[0].url
  }

  Image = (param: any) => {
    const { name, opations } = param;
    const TempKey = this.createKey({ name });
    const _FormItemKey = TempKey._FormItemKey;
    const _name = TempKey._name;
    const _label = this.formatLabel(param);
    const _formProps = {
      key: _FormItemKey,
      label: _label,
    }

    const fileList: any[] = [];

    if (opations.initialValue) {
      const obj = {
        uid: '-1',
        name: opations.initialValue.split('/').pop(),
        url: opations.initialValue
      }
      fileList.push(obj)
    }



    return (
      <FormItem
        {..._formProps}
        name={_name}
        {...opations}
        // valuePropName="fileList"
        getValueFromEvent={this.normFile}
      >
        {
          <SharkrUpload
            fileList={fileList}
            accept="image/*"
            listType="picture-card"
            max={1}
            serviceCode="distribution-operation-web" 
            uploadType="nos"
            onChange={(e: any) => {
              console.log(e);
            }}
          />
        }
      </FormItem>
    )

  }



  /**
   * 人员选择器
   * @param param 
   * @returns 
   */
  Person = (param: any) => {

    const { name, opations,onChange } = param;

    const TempKey = this.createKey({ name });
    const _FormItemKey = TempKey._FormItemKey;
    const _name = TempKey._name;


    const _label = this.formatLabel(param);

    const _formProps = {
      key: _FormItemKey,
      label: _label,
    }


    return (
      <FormItem
        {..._formProps}
        name={_name}
        {...opations}
        // valuePropName={'data'}
        // getValueFromEvent={(value: string | undefined, data: IcacUser | undefined)=>{
        
        //   return data
        // }}
      >
        {
          <IcacSingleUserSelect
            defaultValue={opations.initialValue}
            onChange={(value: string | undefined, data: IcacUser | undefined) => { 
              onChange({data,name});
            }}
             
          />
        }
      </FormItem>
    )
  }

}




// export default new CreateFormItem();
const createFormItem = new CreateFormItem()

export {
  createFormItem
}