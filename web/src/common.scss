// 以下为common的规则声明， common.scss在于集中对基础样式进行控制，减少因某些原因导致样式调整的点过于发散，成本升高

// font -> ft ， // 字体样式类
// color -> c // 颜色
// funciton ->fn  , 表示某个行为

// 以下为组合
// m -> margin , p -> padding

// l-> left, r-> right , t -> top,  b -> bottom
// mt -> margin top , mr-> margin right , ml -> margin left

.bodyWidth {
  // min-width: 1200px;
}

.fnLeft {
  float: left;
}

.fnClear:after {
  content: '\20';
  display: block;
  height: 0;
  clear: both;
}

.ftLeft {
  text-align: left;
}
.ftLeftImportant {
  text-align: left !important;
}

.ftCenter {
  text-align: center;
}

.ftRight {
  text-align: right;
}

.fnHide {
  display: none;
}

.fnHideImportant {
  display: none !important;
}

.ftCursor {
  cursor: pointer;
}

.ftMove {
  cursor: move;
}

.fnAnimate {
  transition: all 0.5s;
}

.fnHeight100 {
  height: 100%;
  overflow: hidden;
}

.cBlue {
  color: #2196f3;
}

.cRed {
  color: rgb(219, 55, 55);
}

.ftBold {
  font-weight: bold;
}

.pt5 {
  padding-top: 5px;
}
.pt10 {
  padding-top: 10px;
}
.pb10 {
  padding-bottom: 10px;
}

.pl15 {
  padding-left: 15px;
}

.ml10 {
  margin-left: 10px;
}

.mr10 {
  margin-right: 10px;
}

.mr15 {
  margin-right: 15px;
}

.mr20 {
  margin-right: 20px;
}

.mb10 {
  margin-bottom: 10px;
}

.ft14 {
  font-size: 14px;
}

.ft15 {
  font-size: 15px;
}

.ft16 {
  font-size: 16px;
}

.ft20 {
  font-size: 20px;
}

.iconWX {
  color: #00d12e;
}

.subTitle {
  font-size: 12px;
  line-height: 1.1;
}

.ftEllipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
