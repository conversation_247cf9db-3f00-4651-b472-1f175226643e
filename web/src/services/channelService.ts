/*
 * @Author: chenxiaopan <EMAIL>
 * @Date: 2022-09-29 14:14:14
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-11-29 14:36:35
 * @FilePath: /distribution-operation-web/web/src/services/channelService.ts
 * @Description: 淘系铺货
 */
import { PlainObject, AjaxPaginationResult } from '@shark/core'

import requestList from '../consts/requestList'
import * as ajaxService from './ajaxService'

const getChannelList = (params?: PlainObject): Promise<any> =>
  ajaxService.get(requestList.channel.queryAllChannelInfo, params)

const submitPutGoods = (params: PlainObject): Promise<any> =>
  ajaxService.postByJson(requestList.channel.putGoods, params)
const queryChannelItem = (params?: PlainObject): Promise<any> =>
  ajaxService.get(requestList.channel.queryChannelItem, params)

const queryChannelType = (params?: PlainObject): Promise<any> =>
  ajaxService.get(requestList.channel.queryChannelType, params)
const queryCategory = (params?: PlainObject): Promise<any> =>
  ajaxService.get(requestList.channel.queryCategory, params)
const queryPublicProps = (params?: PlainObject): Promise<any> =>
  ajaxService.get(requestList.channel.queryPublic, params)
const addProduct = (params: PlainObject): Promise<any> =>
  ajaxService.postByJson(requestList.channel.addProduct, params)

export const channelService = {
  getChannelList,
  submitPutGoods,
  queryChannelItem,
  queryChannelType,
  queryCategory,
  queryPublicProps,
  addProduct
}
