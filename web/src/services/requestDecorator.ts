/*
 * @Author: chen<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-09-29 14:14:14
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-11-29 12:32:18
 * @FilePath: /distribution-operation-web/web/src/services/requestDecorator.ts
 * @Description: 请求并发控制
 */
/* eslint-disable @typescript-eslint/explicit-module-boundary-types */
import { channelService } from './channelService'

class RequestDecorator {
  // 最大并发量
  maxLimit = 5
  // 请求队列,若当前请求并发量已经超过maxLimit,则将其他请求组合后加入到请求队列中
  requestQueue: any[] = []
  requestAjax
  // 当前并发量数目
  currentConcurrent = 0
  constructor({ requestAjax = channelService.submitPutGoods }) {
    this.requestAjax = requestAjax
  }

  // 发起请求api
  async request(params) {
    // 若当前请求数并发量超过最大并发量限制，则将其阻断在这里。
    // startBlocking会返回一个promise，并将该promise的resolve函数放在this.requestQueue队列里。这样的话，除非这个promise被resolve,否则不会继续向下执行。
    // 当之前发出的请求结果回来/请求失败的时候，则将当前并发量-1,并且调用this.next函数执行队列中的请求
    // 当调用next函数的时候，会从this.requestQueue队列里取出队首的resolve函数并且执行。这样，对应的请求则可以继续向下执行。
    if (this.currentConcurrent >= this.maxLimit) {
      await this.startBlocking()
    }
    try {
      this.currentConcurrent++
      return new Promise((resolve, reject) => {
        this.requestAjax(params).then(result => {
          console.log('ajax', result)
          this.currentConcurrent--
          this.next()
          resolve(result)
        }).catch((error)=>{ 
          reject(error)
        })
      }) 
    } catch (err) {
      return Promise.reject(err)
    } finally {
      console.log('当前并发数:', this.currentConcurrent)
    }
  }
  // 新建一个promise,并且将该reolsve函数放入到requestQueue队列里。
  // 当调用next函数的时候，会从队列里取出一个resolve函数并执行。
  startBlocking() {
    let _resolve: any
    const promise2 = new Promise((resolve, reject) => (_resolve = resolve))
    this.requestQueue.push(_resolve)
    return promise2
  }
  // 从请求队列里取出队首的resolve并执行。
  next() {
    if (this.requestQueue.length <= 0) return
    const _resolve = this.requestQueue.shift()
    _resolve()
  }
}
// const createRequestQueue = new RequestDecorator({maxLimit: 6})

export default RequestDecorator
