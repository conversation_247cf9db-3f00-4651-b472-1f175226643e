/*
 * @Author: cpf <EMAIL>
 * @Date: 2022-12-01 10:33:20
 * @LastEditors: cpf <EMAIL>
 * @LastEditTime: 2022-12-01 10:35:29
 * @FilePath: \distribution-operation-web\web\src\services\templateClassService.ts
 * @Description: 
 * 
 * Copyright (c) 2022 <NAME_EMAIL>, All Rights Reserved. 
 */

import { PlainObject, AjaxPaginationResult } from '@shark/core';
import { axiosService } from '@sharkr/request';

let ApiHost = '/xhr/returnFirst';
if (window.location.host.indexOf('local.yx.mail.netease.com') > -1) {
  ApiHost = '';
}

// 获取模板分类
export const getTemClass = (params?: PlainObject): Promise<any> =>
  axiosService.get(`${ApiHost}/xhr/combinedPic/dict/tree/query`, params);

//创建模板副本
export const createATemplateCopy = (params?: PlainObject): Promise<any> =>
  axiosService.get(`${ApiHost}/xhr/combinedPic/template/createATemplateCopy`, params);

//分享编辑权限人
export const sharedEditingPermissionUser = (params?: PlainObject): Promise<any> =>
  axiosService.postByJson(`${ApiHost}/xhr/combinedPic/template/sharedEditingPermissionUser`, params);

//编辑模板
export const templateFormEdit = (params?: PlainObject): Promise<any> =>
  axiosService.postByJson(`${ApiHost}/xhr/combinedPic/template/edit`, params);

// 创建模板集
export const createTemplateSet = (params?: PlainObject): Promise<any> =>
  axiosService.postByJson(`${ApiHost}/xhr/combinedPic/template/createTemplateSet`, params);

//  添加到模板集
export const addTemplateSet = (params?: PlainObject): Promise<any> =>
  axiosService.postByJson(`${ApiHost}/xhr/combinedPic/template/addTemplateSet`, params);

//  查询模板集内部模板信息
export const queryTemplateListByTemplateSetId = (params?: PlainObject): Promise<any> =>
  axiosService.get(`${ApiHost}/xhr/combinedPic/template/queryTemplateListByTemplateSetId`, params);

//  从模板集移除
export const removeTemplateSetTemplate = (params?: PlainObject): Promise<any> =>
  axiosService.postByJson(`${ApiHost}/xhr/combinedPic/template/removeTemplateSetTemplate`, params);
