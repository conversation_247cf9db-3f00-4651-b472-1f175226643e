/* eslint-disable @typescript-eslint/explicit-module-boundary-types */
import { AjaxResultCode, AjaxPaginationResult, PlainObject } from '@shark/core';
import { axiosService } from '@sharkr/request';
import { message } from 'antd';

/**
 * get请求-异步
 * 
 * @param {any} obj 
 * @returns 
 * @memberof requestTools
 */
const get = (url: string, obj?: PlainObject, setLoading?: Function) => {
    return new Promise((resolve, reject) => {
        setLoading && setLoading(true);
        axiosService.get(url, obj).then((response:any) => {
            // 请求当前模块数据，动态更改模块表单
            // 只有 premodal.json 因是静态json文件，没有code
            shunt(response, resolve, reject);
            setLoading && setLoading(false);
        }).catch((e) => {
            reject(e);
            setLoading && setLoading(false);
        });
    });
}
/**
 * post请求-异步
 * 
 * @param {any} obj 
 * @returns 
 * @memberof requestTools
 */
const post = (url: string, obj?: PlainObject, setLoading?: Function) => {
    return new Promise((resolve, reject) => {
        setLoading && setLoading(true);
        axiosService.post(url, obj).then((response:any) => {
            // 请求当前模块数据，动态更改模块表单
            // 只有 premodal.json 因是静态json文件，没有code
            shunt(response, resolve, reject);
            setLoading && setLoading(false);
        }).catch((e) => {
            reject(e);
            setLoading && setLoading(false);
        });
    });
}
/**
* postByJson请求-异步
* 
* @param {any} obj 
* @returns 
* @memberof requestTools
*/
const postByJson = (url: string, obj: PlainObject, setLoading?: Function) => {
    return new Promise((resolve, reject) => {
        setLoading && setLoading(true);
        axiosService.postByJson(url, obj).then((response:any) => {    
            // 请求当前模块数据，动态更改模块表单
            // 只有 premodal.json 因是静态json文件，没有code
            shunt(response, resolve, reject);
            setLoading && setLoading(false);
        }).catch((e) => {
            reject(e);
            setLoading && setLoading(false);
        });
    });
}
/**
* upload请求-异步
* 
* @param {any} obj 
* @returns 
* @memberof requestTools
*/
const upload = (url: string, obj: any, setLoading?: Function) => {
    return new Promise((resolve, reject) => {
        setLoading && setLoading(true);
        axiosService.upload(url, obj).then((response:any) => {
            // 请求当前模块数据，动态更改模块表单
            // 只有 premodal.json 因是静态json文件，没有code
            shunt(response, resolve, reject);
            setLoading && setLoading(false);
        }).catch((e) => {
            reject(e);
            setLoading && setLoading(false);
        });
    });
}

/**
* 
*  用于集中容错处理
* @param {any} response 
* @memberof requestTools
*/
const shunt = (response: any, resolve: any, reject: any) => {
    // 接口返回信息字段兼容,code类型和data数据
    const data = response.data || response.result || null;
    const errorMsg = response.errorMsg || response.content || response.errMsg || response.desc || response.msg;
    const pagination = response.pagination || response.pageInfo || null;
    if (Number(response.code) === 200) {
        if(pagination){
            resolve({data, pagination});
        }else{
            resolve(data);
        }
    } else if (Number(response.code) === 400) {
        message.error(errorMsg || '系统错误', 4, () => {
            reject(response);
        });
    } else if (Number(response.code) === 401 || Number(response.code) === 302) {
        message.error(errorMsg || '当前登录失效，正在跳转至登录界面', 1, () => {
            // login();
            reject(response);
        });
    } else if (Number(response.code) === 403) {
        message.error(errorMsg || '您没有权限,请检查分类是否正确', 4, () => {
            reject(response);
        });
    } else if (Number(response.code) === 405) {
        message.error(errorMsg || '您没有权限,请检查分类是否正确', 4, () => {
            reject(response);
        });
    } else {
        message.error(errorMsg || '系统错误', 4, () => {
            reject(response);
        });
    }
}


// 过滤http的code
const filterCode = (result: any) => {
  const code = Number(result.code);

  if (code === AjaxResultCode.unauthorized) {
    // 未登录 TODO
    // logout();
  } else if (code === AjaxResultCode.forbidden) {
    // 没权限
    location.href = '/nav/error/403?product=yanxuan-store'
  } else if (code === 200) {
    // 正常返回
    return true;
  } else {
    // 其他异常
    const errorMsg = result.errorMsg || result.errorCode || result.message || result.desc;
    message.error(errorMsg || '服务器出了点小问题，正在全力修复哦~');
  }
}

export {
    filterCode,
    get,
    post,
    postByJson,
    upload
}
