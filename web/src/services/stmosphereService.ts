import { PlainObject, AjaxPaginationResult } from '@shark/core';
import { axiosService } from '@sharkr/request';

export let ApiHost = '/xhr/returnFirst';
if (window.location.host.indexOf('local.yx.mail.netease.com') > -1) {
  ApiHost = '';
}

// 获取合图列表
export const getImgList = (params?: PlainObject): Promise<any> =>
  axiosService.get(`${ApiHost}/xhr/combinedPic/task/query`, params);
// 删除合图任务
export const deleteTask = (params?: PlainObject): Promise<any> =>
  axiosService.postByJson(`${ApiHost}/xhr/combinedPic/task/delete`, params);
// 获取合图详情
export const getImgDetail = (params?: PlainObject): Promise<any> =>
  axiosService.get(`${ApiHost}/xhr/combinedPic/task/get`, params);
// 获取图片详情
export const getPicElement = (params?: PlainObject): Promise<any> =>
  axiosService.get(`${ApiHost}/xhr/combinedPic/task/picMeta/get`, params);

// 新增合图任务
export const addTask = (params?: PlainObject): Promise<any> =>
  axiosService.postByJson(`${ApiHost}/xhr/combinedPic/task/upsert`, params);
// 重新合图
export const replyAddTask = (params?: PlainObject): Promise<any> =>
  axiosService.postByJson(`${ApiHost}/xhr/combinedPic/task/restart`, params);
// 批量新增合图任务
export const batchAddTask = (params?: PlainObject): Promise<any> =>
  axiosService.postByJson(`${ApiHost}/xhr/combinedPic/task/batch/add`, params);
// 获取模板列表
export const getTemplateList = (params?: PlainObject): Promise<any> =>
  axiosService.get(`${ApiHost}/xhr/combinedPic/template/query`, params)
// 获取模板详情
export const getTemplateDetail = (params?: PlainObject): Promise<any> =>
  axiosService.get(`${ApiHost}/xhr/combinedPic/template/schema/get`, params)
// 获取spu图片列表
export const getSpuList = (params?: PlainObject): Promise<any> =>
  axiosService.get(`${ApiHost}/xhr/picGallery/spu/query`, params);

// 获取sku图片列表
export const getSkuList = (params?: PlainObject): Promise<any> =>
  axiosService.get(`${ApiHost}/xhr/picGallery/sku/query`, params);

// 获取spu图片详情
export const getSpuDetail = (params?: PlainObject): Promise<any> =>
  axiosService.get(`${ApiHost}/xhr/picGallery/spu/detail/get`, params);

// 获取sku图片详情
export const getSkuDetail = (params?: PlainObject): Promise<any> =>
  axiosService.get(`${ApiHost}/xhr/picGallery/sku/detail/get`, params);

// 新增spu
export const addSpu = (params?: PlainObject): Promise<any> =>
  axiosService.postByJson(`${ApiHost}/xhr/picGallery/spu/upsert`, params);

// 新增sku图片
export const addSku = (params?: PlainObject): Promise<any> =>
  axiosService.postByJson(`${ApiHost}/xhr/picGallery/sku/upsert`, params);

// 批量新增spu
export const addBatchSpu = (params?: PlainObject): Promise<any> =>
  axiosService.postByJson(`${ApiHost}/xhr/picGallery/spu/batch/add`, params);

// 批量新增sku图片
export const addBatchSku = (params?: PlainObject): Promise<any> =>
  axiosService.postByJson(`${ApiHost}/xhr/picGallery/sku/batch/add`, params);

// 拉取商品图片
export const getPic = (params?: PlainObject): Promise<any> =>
  axiosService.get(`${ApiHost}/xhr/picGallery/pic/get`, params);

// 删除图片
export const delPicMeta = (params?: PlainObject): Promise<any> =>
  axiosService.postByJson(`${ApiHost}/xhr/combinedPic/task/picMeta/delete`, params);

// 获取模板列表分页
export const getgetTemplates = (params?: PlainObject): Promise<any> =>
  axiosService.get(`${ApiHost}/xhr/combinedPic/template/page/query`, params);
// 删除模板
export const deleteTemplate = (params?: PlainObject): Promise<any> =>
  axiosService.postByJson(`${ApiHost}/xhr/combinedPic/template/delete`, params);
// 新增模板 更新模板
export const templateUpsert = (params?: PlainObject): Promise<any> =>
  axiosService.postByJson(`${ApiHost}/xhr/combinedPic/template/upsert`, params);
// 删除sku图片
export const delSkuImg = (params?: PlainObject): Promise<any> =>
  axiosService.postByJson(`${ApiHost}/xhr/picGallery/sku/delete`, params);
// 删除spu图片
export const delSpuImg = (params?: PlainObject): Promise<any> =>
  axiosService.postByJson(`${ApiHost}/xhr/picGallery/spu/delete`, params);
// 按名称和id获取模板列表
export const getTemplateListByIdOrName = (params?: PlainObject): Promise<any> =>
  axiosService.get(`${ApiHost}/xhr/combinedPic/template/listByTemplateIdOrName`, params);
// 修改合图任务的编辑权限
export const sharedEditingPermissionUser = (params?: PlainObject): Promise<any> =>
  axiosService.postByJson(`${ApiHost}/xhr/combinedPic/task/sharedEditingPermissionUser`, params);


