import { PlainObject, AjaxPaginationResult } from '@shark/core';
import { axiosService } from '@sharkr/request';

import { IGroup } from '../interfaces';
import requestList from '../consts/requestList'

 
 
 

const getTemplateList = (params?: PlainObject): Promise<AjaxPaginationResult<IGroup[]>> =>
axiosService.get(requestList.activity.templateList, params);

const updateStatus = (params?: PlainObject): Promise<AjaxPaginationResult<IGroup[]>> =>
axiosService.postByJson(requestList.activity.templateStatus, params);

const saveTemplateInfo= (params?: PlainObject): Promise<AjaxPaginationResult<IGroup[]>> =>
axiosService.postByJson(requestList.activity.templateSave, params);

const createTemplateInfo= (params?: PlainObject): Promise<AjaxPaginationResult<IGroup[]>> =>
axiosService.postByJson(requestList.activity.templateCreate, params);

/**
 *  防重
 */
export const  activeService = {
  getTemplateList,
  updateStatus,
  saveTemplateInfo,
  createTemplateInfo,
}