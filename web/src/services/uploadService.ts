import { axiosService } from '@sharkr/request'
import axios from 'axios'

const contextPath = '/admin';
let url = `${contextPath}/xhr/nos/uploadStatic.json`
if (window.location.host.indexOf('local.yx.mail.netease.com') > -1) {
    url = '/xhr/nos/upload.json'
} else if (window.location.host.indexOf('remote.yx.mail.netease.com') > -1) {
    url = `${contextPath}/xhr/nos/uploadStatic.json`
}

// 上传文件
export async function uploadFile(file: File): Promise<any> {
    const formData = new FormData()
    formData.append('file', file)
    const res = await axios({
        method: 'post',
        url: url,
        data: formData,
        headers: {
            'Content-Type': 'application/json',
            'Name': 'name'
        },
        xsrfCookieName: 'YX_CSRF_TOKEN',
        xsrfHeaderName: 'Yx-Csrf-Token'
    })
    return res
}

export function downloadFile(file: any) {
    const pathName = window.location.pathname
        .split('/')
        .filter(v => v)
        .join('/')
    let fKey = file.url || file.fileUrl
    const reg = /(http|https):\/\/([\w.]+\/?)\S*/
    if (reg.test(fKey)) {
        const fKeyArr = fKey.split('/')
        fKey = fKeyArr[fKeyArr.length - 1]
    }
    return `/${pathName}/xhr/file/downLoadFile.do?fileName=${encodeURIComponent(
        file.name || file.fileName
    )}&fileKey=${fKey}`
}

// 下载图片并指定文件名
export async function downloadImageByUrl(imageUrl: string, fileName: string): Promise<void> {
  try {
    // 1. 使用fetch获取图片（支持跨域请求）
    const response = await fetch(imageUrl, {
      mode: 'cors', // 尝试跨域请求
      credentials: 'same-origin' // 根据需要设置
    });
    
    // 检查响应是否成功
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    // 2. 获取Blob数据
    const blob = await response.blob();
    
    // 3. 创建对象URL
    const blobUrl = URL.createObjectURL(blob);
    
    // 4. 创建下载链接
    const link = document.createElement('a');
    link.href = blobUrl;
    link.download = fileName || 'download.jpg'; // 默认文件名
    
    // 5. 触发下载
    document.body.appendChild(link);
    link.click();
    
    // 6. 清理
    setTimeout(() => {
      document.body.removeChild(link);
      URL.revokeObjectURL(blobUrl); // 释放内存
    }, 100);
    
  } catch (error) {
    console.error('下载失败:', error);
    // 回退方法（可能无法指定文件名）
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = fileName || '';
    link.target = '_blank'; // 防止在当前页面打开
    link.click();
  }
}