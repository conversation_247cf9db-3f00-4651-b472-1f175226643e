
import { PlainObject, AjaxPaginationResult } from '@shark/core';
import { axiosService } from '@sharkr/request';

let ApiHost = '/xhr/returnFirst';
if (window.location.host.indexOf('local.yx.mail.netease.com') > -1) {
  ApiHost = '';
}

// 获取所在团队空间
export const getTeam = (params?: PlainObject): Promise<any> =>
  axiosService.get(`${ApiHost}/xhr/combinedPic/team/user/query`, params);

// 增加团队空间成员
export const addTeamMember = (params?: PlainObject): Promise<any> =>
  axiosService.postByJson(`${ApiHost}/xhr/combinedPic/team/user/batch/add`, params);