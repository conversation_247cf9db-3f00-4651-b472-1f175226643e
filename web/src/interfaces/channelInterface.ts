/*
 * @Author: chenxiaopan <EMAIL>
 * @Date: 2022-09-29 14:14:14
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-11-29 14:32:21
 * @FilePath: /distribution-operation-web/web/src/interfaces/channelInterface.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// interface以I开头
export interface IChannelInfo {
  accessType?: number
  accountType?: number
  channelId: number
  channelName: string
  currentStatus?: number
  marketPrincipal?: string
  marketPrincipalName?: string
  operatorPrincipal?: string
  operatorPrincipalName?: string
  settleType?: number
  verifyStatus?: number
  verifyType?: number
  reconciliationWay?: number
}
export interface ISkuInfo {
  itemId: number
  itemName: string
  displayString: string
  skuId: number
  phyCategoryPathList: any[]
  channelItemId?: number
  channelSkuId?: number
  reason?: string
  categoryStr?: string
  channelCategory?: number[]

  // 页面展示使用
  rowSpan: number
  selectedCate?: number[]
}
export interface IChannelTypeInfo {
  channelId: number
  type: number
}

export interface IDelColProps {
  info: ISkuInfo
  itemType: string
  handleDel: Function
  delDisabled?: boolean
}
export interface IColProps {
  info: ISkuInfo
  disabled?: boolean
  // 数据修改
  handleChange: Function
  channelId: number
  defaultOptions?: any[]
  cateInfo?: {
    isLeaf: boolean
    categoryId: number
  }
}
export interface IColAttriProps {
  info: ISkuInfo
  disabled?: boolean
  // 数据修改
  channelId: number
  cateInfo?: {
    isLeaf: boolean
    categoryId: number
    fieldList?: any[]
  }
}
export interface ICateProps {
  isLeaf: boolean
  categoryId: number
}
