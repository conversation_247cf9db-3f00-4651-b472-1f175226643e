/*
 * @Description:
 * @Author: chenxiaopan <EMAIL>
 * @Date: 铺货字典表
 */
export enum PutGoodRes {
  'success' = 1,
  'fail' = 2
}
export enum ChannelType {
  'JD' = 1,
  'TianMao' = 2,
  'Ali' = 3
}
// 铺货接口状态显示成功，实际没有返回铺货成功后的渠道skuId, 设置默认值；情况较少
export const DefaultChannelSkuId = 88888

export enum FileRuleType {
  required = 'requiredRule',
  hidden = 'disableRule',
  tip = 'tipRule',
  maxNum = 'maxInputNumRule'
}
export enum FileRuleValue {
  true = 'true'
}

export enum ColKey {
  keyAttr = '产品关键属性',
  channelCate = '渠道类目'
}
export enum ItemType {
  item = 'item',
  sku = 'sku'
}

export enum OperatorType {
  or = 'or',
  and = 'and'
}

export enum SymbolType {
  equal = '==',
  unEqual = '!=',
  include = 'contains',
  unInclude = 'not contains',
  bigger = '>',
  less = '<',
  biggerAndEqual = '>=',
  lessAndEqual = '<=',
  isNull = 'is null',
  isEmpty = 'is empty'
}
