import axios from 'axios'

export const sizeOptions = [
  {
    name: '800*800',
    value: 1
  },
  {
    name: '750*1000',
    value: 2
  },
  {
    name: '800*1200',
    value: 3
  },
]

export const sizeOptionsText = [
  {
    name: '主图800*800',
    value: 1
  },
  {
    name: '主图750*1000',
    value: 2
  },
  {
    name: '主图800*1200',
    value: 3
  },
]

export const operateTypeOptions = [
  {
    name: '拉取严选商品图',
    value: 1
  },
  {
    name: '自行上传',
    value: 2
  },
]

export const templateFrameOptions = [
  {
    name: '日常版',
    value: 1
  },
  {
    name: '大促活动版',
    value: 2
  },
]

export const serviceTypeOptions = [
  {
    name: '有免息',
    value: 1
  },
  {
    name: '无免息',
    value: 2
  },
]

export const giftTypeOptions = [
  {
    name: '1个赠品',
    value: 1
  },
  {
    name: '2个赠品',
    value: 2
  },
  {
    name: '3个赠品',
    value: 3
  },
  {
    name: '多个赠品',
    value: 4
  },
]
export const taskTypeOptions = [
  {
    name: '单张合图',
    value: 1
  },
  {
    name: '批量合图',
    value: 2
  },
]

export const choiceStatus = (status: any, progressRate) => {
  switch (status) {
    case 0:
      return '未开始'
    case 1:
      return `进行中(${progressRate}%)`
    case 2:
      return '已完成'
    case 3:
      return '已完成，已过期无法导出'
    case 4:
      return '合图失败'
    default:
      return '未知状态'
  }

}
export const downloadImage = (imageUrl: string, name: string): void => {
  const image = new Image();
  image.setAttribute('crossOrigin', 'anonymous');
  image.onload = function (): void {
    // console.log(image);
    const canvas = document.createElement('canvas');
    canvas.width = image.width;
    canvas.height = image.height;
    const context = canvas.getContext('2d') as CanvasRenderingContext2D;
    context.drawImage(image, 0, 0, image.width, image.height);
    const url = canvas.toDataURL('image/png');
    const a = document.createElement('a');
    const event = new MouseEvent('click');
    a.download = name;
    a.href = url;
    a.dispatchEvent(event);
  };
  image.src = imageUrl;
};
// 获取文件名称
export const getUrlName = (url: string) => {
  const http = url.split('?')[0];
  const file = http.split('/')
  const name = file[file.length - 1].split('.')[0]
  return name;
}
// 下载图片
export const downloadPic = (url: string) => {
  downloadImage(`${url }?time=${ new Date().valueOf()}`, getUrlName(url))
}

export const giftOptions = [
  {
    name: '1个赠品',
    value: 1
  },
  {
    name: '2个赠品',
    value: 2
  },
  {
    name: '3个赠品',
    value: 3
  },
  {
    name: '多个赠品',
    value: 4
  },
]

export enum BACK_IMAGE_URL {
  'https://yanxuan.nosdn.127.net/static-union/165960430646c09c.png' = 1,
  'https://yanxuan.nosdn.127.net/static-union/16596043068589c3.png'
}
export const imgTypeOptions = [
  {
    name: 'jpg',
    value: 'jpg'
  },
  {
    name: 'png',
    value: 'png'
  },
]