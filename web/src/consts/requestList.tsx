/*
 * @Author: chenxiaopan <EMAIL>
 * @Date: 2022-09-29 14:14:14
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-11-29 14:12:18
 * @FilePath: /distribution-operation-web/web/src/consts/requestList.tsx
 * @Description: 渠道铺货请求
 */
const xhrPrefix = 'xhr'
const service = 'loki-node'
const activityPath = `/${xhrPrefix}/${service}`
const channelPath = `/${xhrPrefix}/channel`

export default {
  activity: {
    templateList: `${activityPath}/template/query`,
    templateStatus: `${activityPath}/template/status`,
    templateSave: `${activityPath}/template/save`,
    templateCreate: `${activityPath}/template/create`
  },
  // TODO
  channel: {
    queryAllChannelInfo: `${channelPath}/queryAllChannelInfo`,
    putGoods: `/${xhrPrefix}/shelves/submit`,
    queryChannelItem: '/xhr/channelItem/queryChannelItemByType',
    queryChannelType: `/${xhrPrefix}/shelves/queryChannelType`,
    queryCategory: `/${xhrPrefix}/shelves/queryCategory`,
    queryPublic: `/${xhrPrefix}/shelves/queryProductPublicProps`,
    addProduct: `/${xhrPrefix}/shelves/addProduct`
  }
}
