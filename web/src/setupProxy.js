/* eslint-disable @typescript-eslint/no-var-requires */
const path = require('path');
const fs = require('fs');

const { createProxyMiddleware } = require('http-proxy-middleware');

const CONTEXT_PATH = process.env.CONTEXT_PATH;
const MOCK_PATH = process.env.MOCK_PATH;
const XHR_PREFIX = process.env.XHR_PREFIX;
const REMOTE_URL = process.env.REMOTE_URL;
const PRODUCT_CODE = process.env.PRODUCT_CODE;

console.log('CONTEXT_PATH', CONTEXT_PATH);
console.log('MOCK_PATH', MOCK_PATH);
console.log('REMOTE_URL', REMOTE_URL);
console.log('PRODUCT_CODE', PRODUCT_CODE);

const filter = function (pathname, req) {
    const rst = pathname.match(`^${CONTEXT_PATH}${XHR_PREFIX}`) 
    return rst;
};
module.exports = function (app) {
    // 权限中心的转发
    app.use(
        createProxyMiddleware(`${CONTEXT_PATH}/xhr/eagler`, {
            target: 'https://test.yx.mail.netease.com/distribution-operation',
            changeOrigin: true,
            ws: true
        })
    );

    // 区分 local 与 remote
    app.use(async (req, res, next) => {
        // remote模式
        if (req.hostname.indexOf('remote.yx.mail.netease.com') > -1) {
            createProxyMiddleware(filter, {
                target: REMOTE_URL,
                changeOrigin: true,
                ws: true,
                pathRewrite: function (path) {
                    // const target_path = path.replace(`/${CONTEXT_PATH}`, '')
                    const targetPath = path;
                    console.log('CONTEXT_PATH', CONTEXT_PATH);
                    console.log('MOCK_PATH', MOCK_PATH);
                    console.log('proxy: ', path, '--> ', targetPath);
                    return targetPath;
                }
            })(req, res, next);
        } else {
            // local模式
            await next();
        }
    });

    // 权限中心的转发
    app.use(
        createProxyMiddleware(`${CONTEXT_PATH}*/xhr/userCenterManage`, {
            target: 'http://yxius.you.163.com',
            changeOrigin: true,
            pathRewrite: path => path.replace(`${CONTEXT_PATH}/xhr/userCenterManage`, ''),
            ws: true
        })
    );

    // 人员选择器的转发
    app.use(
        createProxyMiddleware(`${CONTEXT_PATH}/xhr/eagle/umc-selector`, {
            target: 'http://yxius.you.163.com',
            changeOrigin: true,
            pathRewrite: path =>
                path.replace(`${CONTEXT_PATH}/xhr/eagle/umc-selector`, PRODUCT_CODE),
            ws: true
        })
    );

    // icac转发
    app.use(
        createProxyMiddleware('/icac', {
            target: 'http://test.yx.mail.netease.com',
            changeOrigin: true,
            ws: true
        })
    );

    // 本地 mock
    app.use((req, res, next) => {
        const reg = new RegExp(`${XHR_PREFIX}`);
        if (!reg.test(req.path)) {
            next();
        } else {
            console.log('CONTEXT_PATH', CONTEXT_PATH);
            console.log('MOCK_PATH', MOCK_PATH);
            const emitContentPath = req.path.replace(`${CONTEXT_PATH}/`, '');
            const mockFilePath = path.join(MOCK_PATH, emitContentPath);
            console.log('mock data->', mockFilePath);
            if (fs.existsSync(mockFilePath)) {
                res.set('Content-Type', 'application/json; charset=UTF-8');
                let body = fs.readFileSync(mockFilePath, 'utf-8');
                if (typeof body === 'string') {
                    body = JSON.parse(body);
                }
                res.status(200).send(body);
            } else {
                res.status(404).send({ code: 404, data: mockFilePath });
            }
        }
    });

};
