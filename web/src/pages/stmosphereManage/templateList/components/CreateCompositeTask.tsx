/* eslint-disable no-useless-concat */
/* eslint-disable prettier/prettier */
/* eslint-disable max-lines-per-function */
/* eslint-disable array-callback-return */
import React, { useMemo, useEffect, useRef, useState, useImperativeHandle, forwardRef } from 'react'
import { Button, message, Modal, Upload } from 'antd'
import Postmate from 'postmate'
import { useHistory } from 'react-router-dom'
import { SharkRForm, registerComponent } from '@sharkr/form'
import { SharkRLoading, SharkRPreview } from '@sharkr/components'
import { BACK_IMAGE_URL, imgTypeOptions, taskTypeOptions } from '../../../../consts/schemaForm'
import { batchAddTask, getTemplateDetail } from '../../../../services/stmosphereService'
import { extendTemplateData, loadPicSize } from '../../../../utils'
import CustomSelect from '../../components/CustomSelect'
import CustomUpload from '../../components/CustomUpload'


registerComponent('CustomSelect', CustomSelect)

registerComponent('CustomUpload', CustomUpload)
const CreateCompositeTask: React.FC<any> = (props: any, ref) => {
    const { savePageInfo, template, callback } = props
    const history = useHistory()
    const [taskModal, setTaskModal] = useState(false)
    const formRef = useRef<any>(null)
    const [loading, setLoading] = useState(false)


    console.log("template", template)


    const diffOption = !!template ? {
        type: 'Select',
        props: {
            showSearch: true,
            allowClear: true,
            placeholder: '请输入模板ID/名称',
            disabled: true
        },
        value: template?.templateId,
        options: [{
            name: template.templateName,
            value: template.templateId,
            rawData: template,
        }],
    } : {
        type: 'CustomSelect',
    };

    const schema = useMemo(() => {
        const data = [
            {
                key: 'templateId',
                label: '模板',
                ui: {
                    required: true,
                },
                rules: [
                    {
                        required: true,
                        message: '请输入模板ID/名称',
                    },
                ],
                ...diffOption,
                placeholder: '请输入模板ID/名称',
            },
            {
                key: 'activityName',
                type: 'Input',
                label: '活动名称',
                props: {
                    placeholder: '请输入活动名称',
                },
                ui: {
                    required: true,
                },
                rules: [
                    {
                        required: true,
                        message: '请输入活动名称',
                    },
                ],

            },
            // {
            //     key: 'taskType',
            //     type: 'Radio',
            //     label: '任务类型',
            //     ui: {
            //         required: true,
            //         // message: '请选择任务类型',
            //     },
            //     rules: [
            //         {
            //             required: true,
            //             message: '请选择任务类型',
            //         },
            //     ],
            //     options: taskTypeOptions,
            // },
            {
                key: 'outputFormat',
                type: 'Radio',
                label: '图片导出类型',
                options: imgTypeOptions,
                ui: {
                    required: true,
                },
                rules: [
                    {
                        required: true,
                        message: '请选择图片导出类型',
                    },
                ],
            },
            {
                key: 'file',
                type: 'CustomUpload',
                label: '上传批量合图表格',
                ui: {
                    required: true,
                },
                rules: [
                    {
                        required: true,
                        message: '请上传文件',
                    },
                ],
                // status: 'disabled',
                listeners: [
                    {
                        watch: ['templateId'],
                        condition: true,
                        set: (valid, field, form) => {
                            const templateId = form.getValue('templateId')


                            // field.set({
                            //     status: !!templateId ? 'edit' : 'disabled',

                            // })
                            
                            if (!!templateId) {
                                field.set({
                                    props: {
                                        ...field.props,
                                        templateData: !!template ? template : templateId?.rawData,
                                    }

                                })
                            }
                        },
                    },
                ],
            },
        ]
        return data
    }, [template])


    useImperativeHandle(ref, () => {
        return {
            showTaskModal,
        }
    })

    // 展示新建合图弹窗
    const showTaskModal = () => {
        setTaskModal(true)
    }

    // 转化为 formdata格式
    const formdataify = (params: object) => {
        const formData = new FormData()
        Object.keys(params).forEach((key) => {
            if (typeof params[key] === 'string') {
                formData.append(key, params[key])
            } else {
                formData.append(key, params[key])
            }
        })
        return formData
    }
    // 展示模板错误详情
    const error = (value: string) => {
        Modal.error({
            title: '错误提示',
            content: value,
        })
    }


    const handleOk = () => {
        formRef &&
            formRef.current.submit().then(async (data) => {
                const { activityName, file, templateId, outputFormat } = data;

                const params = { templateId: !!template ? templateId : templateId.value, file: file[0], activityName, outputFormat };

                setLoading(true);
                const res = await batchAddTask(formdataify(params))
                if (res.code === 200) {
                    message.success('操作成功')
                    setTaskModal(false);

                    if (callback) {
                        callback();
                    } else {
                        setTimeout(() => {
                            history.push('/atmosphere/list')
                        }, 100)
                    }

                } else {
                    error(res?.message)
                }
                setLoading(false);
            }).catch(err => {
                console.log('err', err)
                setLoading(false);
            })
    }

    return (
        <>
            <Modal
                destroyOnClose
                className="taskModal"
                key="taskModal"
                title="创建合图任务"
                visible={taskModal}
                width={600}
                onCancel={() => setTaskModal(false)}
                confirmLoading={loading}
                onOk={() => handleOk()}>
                <SharkRForm ref={formRef} schema={schema} />
            </Modal>
        </>
    )
}
export default forwardRef(CreateCompositeTask)
