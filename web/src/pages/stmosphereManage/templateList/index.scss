.template-card {
  width: 100%;
  .ant-col-5 {
    display: block;
    flex: 0 0 20% !important;
    max-width: 20% !important;
  }
  //  img{
  //     aspect-ratio :1
  //    }
  .ant-card-body {
    padding: 10px;
  }
  .ant-card-cover {
    padding: 1px;
  }
}
.sharkr-form-react.ant-form-horizontal .ant-form-item {
  margin-bottom: 0 !important;
  min-height: 56px !important;
}
.m-img-wrap {
  // margin: auto;
  background: rgb(246, 247, 249);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 200px;
  border-radius: 8px;

  .img {
    max-width: 100%;
    height: 170px;
    border-radius: 6px;
    z-index: 10;
    // background: white;
  }
}
.m-card-name {
  margin-top: 10px;
  white-space: normal;
  height: 22px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.header-text {
  font-weight: 700;
  color: rgba(0, 0, 0, 0.85);
  font-size: 1rem;
}
//鼠标移入的hover效果
.hoverCard {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.4);
  transition: opacity 0.7s;
  padding: 3% 5%;
  border-radius: 5px;
  border: 2px solid transparent;
  height: 200px;
  margin: 0 7%;
  width: 87%;
  cursor: pointer;
  .hoverBox {
    position: relative;
    width: 100%;
    margin-top: 10%;
    color: white;
    .hoverItem {
      padding-left: 5%;
      line-height: 24px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .tagHoverItem {
      padding-left: 5%;
      line-height: 24px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
    .hoverFooter {
      margin-top: 10px;
      .hoverButton {
        display: inline-block;
        width: 77%;
        height: 32px;
        border-radius: 5px;
        padding: 4px;
        background-color: white;
        text-align: center;
        line-height: 24px;
        margin-right: 5%;
        font-size: 0.875rem;
        letter-spacing: 2px;
        color: black;
        cursor: pointer;
      }
      .hoverMoreOutlined {
        display: inline-block;
        width: 18%;
        height: 32px;
        border-radius: 6px;
        padding: 5px;
        background-color: white;
        color: black;
        text-align: center;
        margin: auto;
        cursor: pointer;
        position: relative;
      }
    }
    // .item {
    //   white-space: normal;
    //   height: 28px;
    //   overflow: hidden;
    //   text-overflow: ellipsis;
    //   display: -webkit-box;
    //   -webkit-box-orient: vertical;
    //   -webkit-line-clamp: 2;
    // }
  }
}
.itemBox {
  // position: absolute;
  // top: 98%;
  // left: 73.5%;
  width: 130px;
  background: white;
  border-radius: 20px;
  z-index: 200;
  .moreOutlinedItem {
    line-height: 24px;
    font-size: 0.8125rem;
    cursor: pointer;
    color: black;
    letter-spacing: 1px;
    margin-top: 2%;
    padding: 2px 8px 2px 8px;
  }
}
.ant-popover-content {
  border-radius: 0;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}
.ant-popover-arrow {
  display: none;
}
.hoverBorder {
  z-index: 100;
  border: 2px solid #1890ff;
}
.checkbox {
  position: absolute;
  left: 4%;
  top: 3%;
}
.close {
  position: absolute;
  top: 40%;
  left: 38%;
  cursor: pointer;
}
.checkArr {
  display: inline-block;
  width: 18px;
  margin-left: 3px;
  text-align: center;
  margin: auto;
}
.spanMarginLeft {
  margin-left: 5px;
}
// .Checkbox .ant-checkbox-checked .ant-checkbox-inner::after {
//   display: none;
// }
// .Checkbox .ant-checkbox-inner {
//   width: 15px; /* 默认是16px */
//   height: 15px; /* 默认是16px */
// }
.no-padding-popover .ant-popover-inner-content {
  padding: 5px !important; /* 去除padding */
}
.smallImg {
  width: 4.6rem;
  height: 1.4rem;
  position: absolute;
  bottom: 24%;
  left: 10%;
  background-image: url(../../../assets/mbj.png);
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  border-radius: 21%;
  z-index: 20;
  .smallSpan {
    font-size: 12px;
    color: white;
    margin-left: 35%;
    vertical-align: text-top;
  }
}
