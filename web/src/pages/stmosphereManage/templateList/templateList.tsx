/* eslint-disable prettier/prettier */
/* eslint-disable react/jsx-key */
/* eslint-disable max-lines-per-function */
/* eslint-disable array-callback-return */
import React, { useMemo, useEffect, useRef, useState } from 'react'
import { umcService } from '@eagler/authorizion-base'
import {
	ExclamationCircleOutlined,
	MoreOutlined,
	CloseOutlined
} from '@ant-design/icons'
import { UmcAuth } from '@eagler/authorizion'
import {
	Button,
	Row,
	Col,
	Table,
	Modal,
	Pagination,
	Input,
	Upload,
	message,
	Space,
	Card,
	Tag,
	Checkbox,
	Popover,
	Drawer,
	Divider,
	Form
} from 'antd'
import Postmate from 'postmate'
import './index.scss'
import { registerComponent, SharkRForm } from '@sharkr/form'
import { Link, useHistory } from 'react-router-dom'
import { UploadTmp, UploadImg } from '../../../components'
import {
	getgetTemplates,
	deleteTemplate,
	getTemplateDetail,
	templateUpsert,
	addTask
} from '../../../services/stmosphereService'
import RadioConfig from '../components/radioConfig'
import { IAntDPaginationProps } from '../list/list'
import AddDrawingTask from './components/addDrawingTask'
import { openUrl } from '@sharkr/utils'
import { extendTemplateData, getQuery } from '../../../utils'
import { TemplateClass } from './components/tempClass'
import { TeamSpace } from '../../components/teamSpace'
import {
	getTemClass,
	createATemplateCopy,
	sharedEditingPermissionUser,
	templateFormEdit,
	createTemplateSet,
	addTemplateSet
} from '../../../services/templateClassService'
// import { transferToTeamSpace } from '../../../services/personalServices'
import { getTeam } from '../../../services/teamServices'
import copy from 'copy-to-clipboard'
import { debounce } from 'lodash'
import { MultiUserSelect, IcacUser } from '@eagler/umc-select-web'
import { DetailModal } from './components/detailModal'
import { CoreHelper } from '@sharkr/util'
import { FilterHead } from '../personalList/components/FilterHead'
import { getAllShopList } from '../../../services/personalServices'
import CreateCompositeTask from './components/CreateCompositeTask'
import IframeModal from './components/iframeModal'
import { getUserInfo } from '@sharkr/components'


interface IECSQueryProps {
	pageNo: number
	pageSize: number
	keyword?: string
	scene: number | null
	purpose: number | null
	topic: number | null
	teamId: number | null
}
registerComponent('UploadTmp', UploadTmp)
registerComponent('UploadImg', UploadImg)

registerComponent('RadioConfig', RadioConfig)

export const TemplateLists: React.FC<{}> = (props: {}) => {
	const classFormRef = useRef<any>(null)
	const localTeam = JSON.parse(localStorage.getItem('select') || 'null')
	const { confirm } = Modal
	const history = useHistory()
	const taskModalRef = useRef<any>()
	const IframeModalRef = useRef<any>()
	const editUrl = `${`${location.protocol}` + '//'}${window.location.host
		}/distribution-operation/oly-miriam-editor#/micro/edit`
	const iframe = useRef<Postmate.ParentAPI>()

	let defaultSearchParam = {
		pageNo: 1,
		pageSize: 50,
		// scene: 0,
		// purpose: 0,
		// topic: 0,
		// teamId: null
		templateName: '',
		templateId: '',
		shopId: '',
		tagList: [] as any[],
		addTime: [] as any[]
	}

	try {
		const searchParamObj = JSON.parse(
			localStorage.getItem('searchParam') || 'null'
		)

		if (searchParamObj) {
			defaultSearchParam = searchParamObj
		}
	} catch (e: any) {
		console.log(e.message)
	}

	const [searchParam, setSearchParam] = useState<any>(defaultSearchParam)
	const [options, setOptions] = useState<any>([])
	const [team, setTeam] = useState<number | null>(null)
	const [teamId, setTeamId] = useState<any>(null)
	const [data, setData] = useState<any>([])
	const [dateSource, setDateSource] = useState<any[]>([])
	const [pagination, setPagination] = useState<IAntDPaginationProps | {}>({})
	const [isModalVisible, setIsModalVisible] = useState(false)
	const [isMoveVisible, setIsMovelVisible] = useState(false)
	const [template, setTemplate] = useState<any>(undefined)
	// const [temClass, setTemClass] = useState<any>(null)
	const formRef = useRef<any>(null)
	const moveFormRef = useRef<any>(null)
	const [drawer, setDrawer] = useState(false)
	const [checkArr, setCheckArr] = useState<any[]>([])
	const [allCheck, setAllCheck] = useState(false)
	// const [allIndeterminate, setAllIndeterminate] = useState(false)
	const [templateSetTitle, setTemplateSetTitle] = useState(null)
	const [templateSetModal, setTemplateSetModal] = useState(false)
	const templateSetFormRef = useRef<any>(null)
	const [shareModal, setShareModal] = useState(false)
	const [templateItem, setTemplateItem] = useState({
		authorityUser: '',
		templateId: null
	})
	const [isModalTitle, setIsModalTitle] = useState<any>(null)
	const [templateForm, setTemplateForm] = useState<any>({})
	const [detailModalFlag, setDetailModalFlag] = useState<any>(false)
	const [detailModalId, setDetailModalId] = useState<any>(null)
	const [detailModalName, setDetailModalName] = useState<any>(null)
	const [addTemplateIdList, setAddTemplateIdList] = useState<any>([])
	const [shopList, setShopList] = useState<any[]>([])
	const [detailModalEditFlag, setDetailModalEditFlag] = useState<any>(false)
	const [style, setStyle] = useState<any>('')
	const [innerWidth, setInnerWidth] = useState<any>(null)


	const userEmail = getUserInfo()?.email;

	const changeSenceData = (value: any[]) => {
		if (value.length <= 0) return []
		const dataValues = [{ name: '全部', value: 0 }]
		value?.forEach((item: any) => {
			item.value = item?.id
			dataValues.push({ ...item })
		})
		return dataValues
	}
	const changePurData = (data: any[], senceId: number) => {
		let dataValues: any = []
		const senceValue = data.find(item => item.id === senceId)
		if (senceValue?.subNode && senceValue?.subNode?.length) {
			dataValues = [...changeSenceData(senceValue?.subNode)]
		}
		return dataValues
	}

	const moveSchema = useMemo(() => {
		if (data.length <= 0 && options.length <= 0) return null
		const value: any = [
			{
				key: 'scene',
				type: 'Select',
				label: '场景',
				ui: { required: true },
				value: 0,
				options: data && changeSenceData(data)
			},
			{
				key: 'purpose',
				type: 'Select',
				label: '用途',
				ui: { required: true },
				status: 'hidden',
				listeners: [
					{
						watch: ['scene'],
						set: (valid, field, form) => {
							const scene = form.getValue('scene')
							const purOptions = changePurData(data, scene)
							field.set({
								status: purOptions.length <= 0 ? 'hidden' : 'edit',
								value: purOptions.length <= 0 || !scene ? null : 0,
								options: purOptions
							})
						}
					}
				]
			},
			{
				key: 'topic',
				type: 'Select',
				label: '主题',
				ui: { required: true },
				status: 'hidden',
				listeners: [
					{
						watch: ['purpose', 'scene'],
						condition: (field, form) =>
							form.getValue('purpose') && form.getValue('scene'),
						set: (valid, field, form) => {
							const purpose = form.getValue('purpose')
							const scene = form.getValue('scene')
							const purposeOptions = changePurData(data, scene)
							const topicOptions = changePurData(purposeOptions, purpose)
							field.set({
								status: valid && topicOptions.length > 0 ? 'edit' : 'hidden',
								value: topicOptions.length <= 0 || !purpose ? null : 0,
								options: JSON.parse(JSON.stringify(topicOptions))
							})
						}
					}
				]
			},
			{
				key: 'teamId',
				type: 'Select',
				label: '所属团队空间',
				ui: { required: true },
				options: options.map(i => {
					return { name: i.name, value: i.id }
				})
			}
		]
		return value
	}, [data, options])

	const templateSchema: any = [
		{
			key: 'templateName',
			type: 'Input',
			label: '模板名称',
			ui: {
				size: 'lg',
				required: true
				//extra: `建议以“活动名称-日常版/大促活动版-有免息/无免息-通用型/赠品型-800*800/750*1000”的格式命名`
			},
			rules: [
				{
					required: true,
					message: '请填写模板名称'
				}
			],
			props: {
				maxLength: 50,
				placeholder: '请输入模板名称',
				allowClear: true
			}
		},
		{
			key: 'templateSize',
			type: 'Radio',
			label: '模板尺寸',
			ui: {
				required: true
			},
			rules: [
				{
					required: true,
					message: '请选择模板尺寸'
				}
			],
			options: [
				{
					name: '主图800*800',
					value: 1
				},
				{
					name: '主图750*1000',
					value: 2
				},
				{
					name: '自定义',
					value: 3
				}
			]
		},
		{
			key: 'backImgUrl',
			type: 'SharkrUpload',
			label: '自定义模板底图',
			status: 'hidden',
			ui: {
				required: true,
				extra: `自定义模板的尺寸由底图尺寸决定`
			},
			props: {
				max: 1,
				listType: 'picture-card',
				accept: 'image/png, image/jpeg, image/jpg',
				imgSize: 3072
			},
			listeners: [
				{
					watch: ['templateSize'],
					condition: true,
					set: (valid, field, form) => {
						const size = form.getValue('templateSize')
						field.set({
							status: size === 3 ? 'edit' : 'hidden'
						})
					}
				}
			]
		}
	]

	const editTemplateSchema: any = [
		{
			key: 'templateName',
			type: 'Input',
			label: isModalTitle == '编辑模板' ? '模板名称' : '模板集名称',
			ui: {
				size: 'lg',
				required: true
				//extra: `建议以“活动名称-日常版/大促活动版-有免息/无免息-通用型/赠品型-800*800/750*1000”的格式命名`
			},
			rules: [
				{
					required: true,
					message:
						isModalTitle == '编辑模板' ? '请填写模板名称' : '请填写模板集名称'
				}
			],
			props: {
				maxLength: 50,
				placeholder:
					isModalTitle == '编辑模板' ? '请输入模板名称' : '请输入模板集名称',
				allowClear: true
			}
		}
	]

	useEffect(() => {
		setInnerWidth(window.innerWidth)
		getAllShopList().then(res => {
			if (res?.code === 200) {
				const data =
					res?.data?.map(item => {
						item.label = item.channelName
						item.value = item.channelId

						return item
					}) ?? []
				setShopList(data)
			} else {
				message.error('获取店铺列表失败')
				return []
			}
		})
	}, [])

	useEffect(() => {
		getList()
	}, [searchParam])

	useEffect(() => {
		team &&
			setSearchParam({ ...searchParam, teamId: team, pageNo: 1, pageSize: 50 })
	}, [team])

	// useEffect(() => {
	// 	// temClass && setSearchParam({ ...searchParam, ...temClass, pageNo: 1, pageSize: 50 })
	// 	const isSame =
	// 		temClass?.scene === searchParam?.scene &&
	// 		temClass?.purpose === searchParam?.purpose &&
	// 		temClass?.topic === searchParam?.topic
	// 	if (!isSame) {
	// 		setSearchParam({ ...searchParam, ...temClass, pageNo: 1, pageSize: 50 })
	// 	}
	// }, [temClass])
	// 移除从编辑页未关闭的modal蒙层
	useEffect(() => {
		const modalRef = document.getElementsByClassName('ant-modal-root')
		if (modalRef) {
			Array.from(modalRef).map(item => {
				item?.remove()
			})
		}
		// getTemplateClass()
		// getTeamList()
		// const args = getQuery(history.location.search) || ''
		// if (args.teamId) {
		// 	setTeamId(args.teamId)
		// 	setTimeout(() => {
		// 		const layout: any = document.querySelector(
		// 			'.yx-micro-sharkr-layout-content'
		// 		)
		// 		args.pageScroll && (layout.scrollTop = Number(args.pageScroll))
		// 	}, 500)
		// }
		return () => {
			console.log('清理查询参数')
			localStorage.removeItem('searchParam')
		}
	}, [])

	useEffect(() => {

		const storageKey = JSON.parse(localStorage.getItem('storageKey') || 'null')
		if (storageKey && storageKey.status === 'done') {
			if (storageKey.type === 'pic') {
				coverImg(storageKey)
			} else {
				upset(storageKey)
			}
		} else {
			// localStorage.removeItem('storageKey')
		}
	}, [])

	// const getTeamList = async () => {
	// 	const res = await getTeam()
	// 	if (res?.code === 200) {
	// 		// eslint-disable-next-line array-callback-return
	// 		res?.data.map(item => {
	// 			item.value = item?.id
	// 			item.label = item?.name
	// 		})
	// 		if (localTeam && res?.data?.find(item => item.id === localTeam)) {
	// 			setTeamId(localTeam)
	// 			setTeam(localTeam)
	// 		} else {
	// 			setTeamId(res.data[0]?.value)
	// 			setTeam(res.data[0]?.value)
	// 		}
	// 		setOptions(res?.data)
	// 	}
	// }

	// const getTemplateClass = async () => {
	// 	const res = await getTemClass({ type: 1 })
	// 	if (res?.code === 200) {
	// 		setData(res?.data)
	// 	}
	// }
	// 更新图片
	const coverImg = async storageKey => {
		const params = {
			activityName: storageKey.activityName,
			schemaDTO: storageKey.message.schema,
			// picId: template.picId,
			outputFormat: storageKey?.outputFormat,
			picName: storageKey.picName,
			taskId: 0,
			templateId: storageKey.message.template.templateId,
			teamId: storageKey?.teamId
		}
		const res = await addTask(params)
		if (res && res.code === 200) {
			localStorage.removeItem('storageKey')
			message.success('配置成功')
			getList()
		} else {
			localStorage.removeItem('storageKey')
			message.error(res.message)
		}
		history.push('/atmosphere/list')
	}
	const againGetList = async (teamId: any) => {
		const args = getQuery(history.location.search) || ''
		if (args) {
			const params = {
				pageNo: 1,
				pageSize: 50,
				teamId,
				scene: args.scene === 'null' ? null : Number(args.scene),
				purpose: args.purpose === 'null' ? null : Number(args.purpose),
				topic: args.topic === 'null' ? null : Number(args.topic)
			}
			const res = await getgetTemplates(params)
			if (res && res.code === 200) {
				res.data &&
					res.data.map(item => {
						item.flag = false
						item.checked = false
					})
				const { result, paginationVO } = res.data || {
					result: [],
					paginationVO: {}
				}
				const antdPagination: IAntDPaginationProps = {
					current: paginationVO.page,
					pageSize: paginationVO.size,
					total: paginationVO.total
				}
				setPagination(antdPagination)
				setDateSource(result)
			} else {
				message.error('获取列表失败')
			}
		}
	}
	// 更新模板
	const upset = async storageKey => {
		const params = {
			schemaDTO: storageKey.message.schema,
			...storageKey.message.template
		}
		try {
			const extendData = await extendTemplateData(storageKey.message.template)
			const res = await templateUpsert({
				...params,
				backImgUrl: extendData.imageUrl,
				teamId: storageKey?.teamId
			})
			if (res && res.code === 200) {
				message.success('配置成功')
				// againGetList(storageKey?.teamId)
				getList()
				localStorage.removeItem('storageKey')
			} else {
				localStorage.removeItem('storageKey')
				message.error(res.message)
			}
		} catch (e) {
			localStorage.removeItem('storageKey')
			message.error(`操作失败`)
		}
	}

	const onOk = () => {
		//新增时填完表单后进入千画页面,编辑时直接调后端接口
		if (isModalTitle == '新建模板' || isModalTitle == '新建模板集') {
			formRef &&
				formRef.current &&
				formRef.current.submit().then(async data => {
					const extendData = await extendTemplateData(data)

					const templateData = {
						templateId: 0,
						templateName: data.templateName,
						templateSize: data.templateSize,
						...extendData
					}

					const microStorageMessage = {
						message: {
							template: { ...templateData },
							schema: {},
							type: 'template'
						},
						redirectUrl: window.location.href,
						status: 'pending', // 状态 pending:等待编辑结果 done：已编辑完成
						teamId: team
					}
					localStorage.setItem(
						'storageKey',
						JSON.stringify(microStorageMessage)
					)
					savePageInfo()
					setIsModalVisible(false)
					setTimeout(() => {
						window.location.href = editUrl + `?storageKey=storageKey&draft=1`
					}, 100)
				})
		} else if (isModalTitle == '编辑模板' || isModalTitle == '编辑模板集') {
			formRef &&
				formRef.current &&
				formRef.current.submit().then(async data => {
					data.templateId = templateForm.templateId
					const res = await templateFormEdit(templateForm)
					if (res && res.code === 200) {
						setIsModalVisible(false)
						message.success('操作成功')
						getList()
					} else {
						message.error(res.message)
					}
				})
		}
	}

	const onMoveOk = () => {
		moveFormRef &&
			moveFormRef.current &&
			moveFormRef.current.submit().then(async data => {
				const value = await getTemDetail(template?.templateId)
				const extendData = await extendTemplateData(data)
				// console.log('222223123',extendData)
				if (value) {
					const res = await templateUpsert({
						backImgUrl: extendData.imageUrl,
						teamId: team,
						schemaDTO: value,
						...template,
						...data
					})
					if (res.code === 200) {
						message.success('操作成功')
						setIsMovelVisible(false)
						getList()
					} else {
						message.error(res.message)
					}
				}
			})
	}

	// 获取列表
	const getList = async () => {
		// if (!team) return [];
		const query = JSON.parse(JSON.stringify(searchParam));
		let { templateName, templateId, shopId, tagList = [], pageNo = 1, pageSize = 50, teamId } = query;

		shopId = shopId?.value ?? ''
		tagList = tagList?.map(item => item.value)?.join(',') ?? ''

		let [startTime, endTime] = searchParam.addTime ?? []

		startTime = startTime?.format('YYYY-MM-DD') ?? ''
		endTime = endTime?.format('YYYY-MM-DD') ?? ''

		const querParam = {
			templateName,
			templateId,
			shopId,
			tagList,
			startTime,
			endTime,
			pageSize,
			pageNo,
			teamId
		}

		const res = await getgetTemplates(querParam)
		localStorage.removeItem('searchParam')
		if (res && res.code === 200) {
			const { result, paginationVO } = res.data || {
				result: [],
				paginationVO: {}
			}
			const antdPagination: IAntDPaginationProps = {
				current: paginationVO.page,
				pageSize: paginationVO.size,
				total: paginationVO.total
			}
			setPagination(antdPagination)
			result.map(item => {
				if (
					userEmail == item.createUser ||
					item.authorityUser.includes(userEmail)
				) {
					//判断是否用编辑权限，有的话则给true，没有则给false
					item.editFlag = true
				} else {
					item.editFlag = false
				}
			})

			console.log(result, 'result')
			setDateSource(result)
		} else {
			message.error('获取列表失败')
		}
	}
	// 打开合图弹窗
	const openModal = (value: any) => {
		umcService.runCondition('1636131000023').then(res => {
			if (res) {
				setTemplate(value)
				taskModalRef &&
					taskModalRef.current &&
					taskModalRef.current.showTaskModal()
			}
		})
	}

	const savePageInfo = () => {
		// const layoutScroll: any = document.querySelector(
		// 	'.yx-micro-sharkr-layout-content'
		// )?.scrollTop
		// const scene = classFormRef.current.getValue('scene') || searchParam.scene
		// const purpose =
		// 	classFormRef.current.getValue('purpose') || searchParam.purpose
		// const topic = classFormRef.current.getValue('topic') || searchParam.topic
		// // const templateListPageInfo = {
		// // 	pageScroll: layoutScroll,
		// // 	scene: classFormRef.current.getValue('scene'),
		// // 	purpose: classFormRef.current.getValue('purpose'),
		// // 	topic: classFormRef.current.getValue('topic'),
		// // 	teamId: teamId,
		// // }
		// // localStorage.setItem('templateListPageInfo', JSON.stringify(templateListPageInfo))
		// window.location.href = `#/templatesList/list?pageScroll=${layoutScroll}&scene=${scene}&purpose=${purpose}&topic=${topic}&teamId=${searchParam?.teamId}`

		// const layoutScroll: any = document.querySelector(
		// 	'.sharkr-layout-content'
		// )?.scrollTop;

		// console.log("layoutScroll", layoutScroll)

		// const addTime = searchParam.addTime;
		// const query = JSON.parse(JSON.stringify());
		// const pagination = searchParam.pagination;
		// searchParam.addTime = searchParam.filter.addTime?.map(item => {

		// 	// console.log("type", typeof item.format('YYYY-MM-DD'))
		// 	return item = item?.format ? item?.format('YYYY-MM-DD') : item

		// }) ?? []

		// let urlParamStr = `?activeKey=${activeKey}`;

		// for (const key in searchParam.filter) {
		// 	const val = searchParam.filter[key]
		// 	if (val?.length > 0) {
		// 		urlParamStr += Array.isArray(val) ? `&${key}=${JSON.stringify(val)}` : `&${key}=${val}`
		// 	}
		// }

		// for (const key in searchParam.pagination) {
		// 	const val = searchParam.pagination[key]
		// 	if (val) {
		// 		urlParamStr += `&${key}=${val}`
		// 	}
		// }

		// console.log("urlParamStrStr", urlParamStr)
		// window.location.href = `#/templatesList/list${urlParamStr}`

		const searchParamObj = JSON.parse(JSON.stringify(searchParam))

		console.log('searchParamObj', JSON.stringify(searchParam))

		searchParamObj.addTime =
			searchParam.addTime?.map(item => {
				return (item = item?.format ? item?.format('YYYY-MM-DD') : item)
			}) ?? []

		localStorage.setItem('searchParam', JSON.stringify({ ...searchParamObj }))
	}
	// 改变页码
	const handlePaginationChange = (page: number, pageSize = 50) => {
		setSearchParam({
			...searchParam,
			pageNo: page,
			pageSize
		})
	}
	// 删除模板/模板集
	const deleteTem = m => {
		confirm({
			title:
				m.templateFlag == 0
					? '确认删除该模板?'
					: '是否确认删除模板集中所有模板?',
			icon: <ExclamationCircleOutlined />,
			content: '',
			async onOk() {
				try {
					const res = await deleteTemplate({ templateId: m.templateId })
					if (res && res.code === 200) {
						message.success(`删除成功`)
						getList()
					} else {
						message.error(res.message)
					}
				} catch (e) {
					message.error(`删除失败`)
				}
			}
		})
	}
	// 复制 编辑 模板
	const operateTem = async (type: number, templateData: any) => {
		const values = await getTemDetail(templateData?.templateId)
		if (!values) return null
		if (type === 1) {
			templateData.templateId = 0
		}
		const extendData = await extendTemplateData(templateData)
		const microStorageMessage = {
			message: {
				template: { ...templateData, ...extendData },
				schema: values
			},
			redirectUrl: window.location.href,
			status: 'pending', // 状态 pending:等待编辑结果 done：已编辑完成
			type: 'template',
			teamId: team
		}
		localStorage.setItem('storageKey', JSON.stringify(microStorageMessage))
		savePageInfo()
		setTimeout(() => {
			window.location.href = editUrl + `?storageKey=storageKey`
		}, 100)
	}

	const getTemDetail = async (templateId: number) => {
		const res = await getTemplateDetail({ templateId })
		if (res.code === 200 && res.data) {
			return res.data
		} else {
			message.error('获取详情失败')
		}
	}
	const checkboxChange = (e, m) => {
		//点击选中当前选择的图片
		const checkDateSource = [...dateSource]
		checkDateSource.map(item => {
			if (item.templateId == m.templateId) {
				item.checked = e.target.checked
			}
		})
		setDateSource(checkDateSource)
		const arr = checkDateSource.filter(item => item.checked)
		const arr2 = checkDateSource.filter(item => item.templateFlag == 0)
		setCheckArr(arr)
		if (arr.length == 0) {
			// setAllIndeterminate(false)
			setAllCheck(false)
		} else if (arr.length > 0 && arr.length != arr2.length) {
			setDrawer(true)
			// setAllIndeterminate(true)
			setAllCheck(false)
		} else if (arr.length == arr2.length) {
			setDrawer(true)
			// setAllIndeterminate(false)
			setAllCheck(true)
		}
	}
	const hover = (m, flag) => {
		//鼠标移入直接变成可选择的modal
		const hoverDateSource = [...dateSource]
		hoverDateSource.map(item => {
			item.flag = false
			if (item.templateId == m.templateId) {
				item.flag = flag
			}
		})
		setDateSource(hoverDateSource)
	}
	const onClose = () => {
		//关闭抽屉
		setDrawer(false)
	}

	const allCheckChange = e => {
		//全选
		if (e.target.checked == true) {
			const hoverDateSource = [...dateSource]
			hoverDateSource.map(item => {
				if (item.templateFlag == 0) {
					//勾选所有模板
					item.checked = true
				}
			})
			setDateSource(hoverDateSource)
			// setAllIndeterminate(false)
			setAllCheck(true)
			setCheckArr(hoverDateSource.filter(item => item.checked))
		} else {
			const hoverDateSource = [...dateSource]
			hoverDateSource.map(item => {
				if (item.templateFlag == 0) {
					item.checked = false
				}
			})
			setDateSource(hoverDateSource)
			// setAllIndeterminate(false)
			setAllCheck(false)
			setCheckArr([])
		}
	}

	const openTemplateSetModal = text => {
		//点击创建模板集/添加到模板集
		setTemplateSetTitle(text)
		setTemplateSetModal(true)
	}

	const addTemplateSetSchema: any = [
		{
			key: 'templateName',
			type: 'Input',
			label: '模板集名称',
			ui: {
				size: 'lg',
				required: true
			},
			rules: [
				{
					required: true,
					message: '请输入'
				}
			],
			props: {
				placeholder: '请输入',
				allowClear: true
			}
		}
	]
	const editTemplateSetSchema: any = [
		{
			key: 'templateId',
			type: 'Input',
			label: '模板集ID',
			ui: {
				size: 'lg',
				required: true
			},
			rules: [
				{
					required: true,
					message: '请输入'
				}
			],
			props: {
				placeholder: '请输入',
				allowClear: true
			}
		}
	]

	const copyId = m => {
		//复制模板ID
		const textToCopy = m.templateId
		copy(textToCopy)
		message.success('已复制到剪切板')
	}

	const createReplica = debounce(async m => {
		//创建副本模板/模板集
		m.templateFlag == 0
			? message.info('创建副本模板中,请稍等')
			: message.info('创建副本模板集中,请稍等')
		const arr = [...dateSource]
		arr.map(item => {
			if (m.templateId == item.templateId) {
				item.flag = false
			}
		})
		setDateSource(arr)
		const res = await createATemplateCopy({ templateId: m.templateId })
		if (res?.code === 200) {
			m.templateFlag == 0
				? message.success('创建副本模板成功')
				: message.success('创建副本模板集成功')
			getList()
		} else {
			message.error(res.message)
		}
	}, 500)

	const modalVisibleChange = (text, m) => {
		if (text == '新建') {
			setIsModalTitle('新建模板')
			setIsModalVisible(true)
		} else if (text == '编辑') {
			m.templateFlag == 0
				? setIsModalTitle('编辑模板')
				: setIsModalTitle('编辑模板集')
			const obj = {
				templateId: m.templateId,
				templateName: m.templateName
				// templateSize: m.templateSize,
			}
			setTemplateForm(obj)
			setIsModalVisible(true)
		}
	}

	const templateSchemaChange = (changedValue, allValues) => {
		//新增/编辑模板change事件
		allValues.templateId = templateForm.templateId
		setTemplateForm(allValues)
	}

	const templateSetFormRefOk = () => {
		//添加/创建模板集提交
		if (templateSetTitle == '新建') {
			templateSetFormRef &&
				templateSetFormRef.current &&
				templateSetFormRef.current.submit().then(async data => {
					const arr: any[] = []
					checkArr.map(item => {
						arr.push(item.templateId)
					})
					const result = {
						templateName: data?.templateName,
						templateIdList: arr
					}
					const res = await createTemplateSet(result)
					if (res.code === 200) {
						message.success('操作成功')
						const hoverDateSource = [...dateSource]
						hoverDateSource.map(item => {
							item.checked = false
						})
						setDateSource(hoverDateSource)
						// setAllIndeterminate(false)
						setAllCheck(false)
						setCheckArr([])
						setTemplateSetModal(false)
						setDrawer(false)
						setAddTemplateIdList([])
						getList()
					} else {
						message.error(res.message)
					}
				})
		} else if (templateSetTitle == '添加到' || templateSetTitle == '移入') {
			templateSetFormRef &&
				templateSetFormRef.current &&
				templateSetFormRef.current.submit().then(async data => {
					if (addTemplateIdList.length == 0) {
						//批量移入
						const arr: any[] = []
						checkArr.map(item => {
							arr.push(item.templateId)
						})
						const result = {
							templateId: data?.templateId,
							templateIdList: arr
						}
						const res = await addTemplateSet(result)
						if (res.code === 200) {
							message.success('操作成功')
							const hoverDateSource = [...dateSource]
							hoverDateSource.map(item => {
								item.checked = false
							})
							setDateSource(hoverDateSource)
							// setAllIndeterminate(false)
							setAllCheck(false)
							setCheckArr([])
							setTemplateSetModal(false)
							setDrawer(false)
							setAddTemplateIdList([])
							getList()
						} else {
							message.error(res.message)
						}
					} else {
						//单个移入
						const result = {
							templateId: data?.templateId,
							templateIdList: addTemplateIdList
						}
						const res = await addTemplateSet(result)
						if (res.code === 200) {
							message.success('操作成功')
							const hoverDateSource = [...dateSource]
							hoverDateSource.map(item => {
								item.checked = false
							})
							setDateSource(hoverDateSource)
							// setAllIndeterminate(false)
							setAllCheck(false)
							setCheckArr([])
							setTemplateSetModal(false)
							setDrawer(false)
							setAddTemplateIdList([])
							getList()
						} else {
							message.error(res.message)
						}
					}
				})
		}
	}
	// const transferTeamSpace = debounce(async m => {
	// 	// 转到团队空间
	// 	const obj = {
	// 		templateId: m.templateId,
	// 		teamId: 3
	// 	}
	// 	const res = await transferToTeamSpace(obj)
	// 	if (res?.code === 200) {
	// 		message.success('操作成功')
	// 	} else {
	// 		message.error(res.message)
	// 	}
	// }, 500)

	const batchMerging = m => {
		// 导出批量合图模板
		if (m?.downloadUrl && m?.downloadUrl != '') {
			window.open(m.downloadUrl)
		} else {
			message.error('未获取到模板文件')
		}
	}

	const share = m => {
		//打开分享弹窗（设置id和authorityUser）
		setTemplateItem({
			authorityUser: m.authorityUser === '' ? undefined : m.authorityUser,
			templateId: m.templateId
		})
		setShareModal(true)
	}

	const authorityUserChange = value => {
		//分享编辑人change事件
		const copyTemplateItem = { ...templateItem }
		copyTemplateItem.authorityUser = value.join(',')
		setTemplateItem(copyTemplateItem)
	}

	const shareModalOk = debounce(async () => {
		//分享编辑权限提交
		const obj = {
			templateId: templateItem.templateId,
			authorityUser: templateItem.authorityUser
		}
		const res = await sharedEditingPermissionUser(obj)
		if (res?.code === 200) {
			message.success('操作成功')
			setShareModal(false)
			getList()
		} else {
			message.error(res.message)
		}
	}, 500)

	const openDetailModal = m => {
		//点击查看模板
		setDetailModalFlag(true)
		setDetailModalId(m.templateId)
		setDetailModalName(m.templateName)
		setDetailModalEditFlag(m.editFlag)
	}

	const detailModalOk = () => {
		setDetailModalFlag(false)
	}

	const onDataChange = dateSource => {
		//删除模板后回调请求数据
		if (dateSource.length == 0) {
			setDetailModalFlag(false)
			getList()
		}
	}

	const getRemoveList = () => {
		//移出模板后回调请求数据
		getList()
	}

	const add = (text, m) => {
		//移入模板集
		setTemplateSetTitle(text)
		setTemplateSetModal(true)
		setAddTemplateIdList([m.templateId])
	}

	const getshopName = m => {
		const shopName = shopList.filter(item => m.shopId === item.value)
		return shopName[0] ? shopName[0].label : null
	}

	const browse = m => {
		// console.log(m, "查看")
		umcService.runCondition('1636131000023').then(res => {
			if (res) {
				IframeModalRef &&
					IframeModalRef.current &&
					IframeModalRef.current.showTaskModal(m)
			}
		})
	}

	const buttonHover = text => {
		setStyle(text)
	}

	return (
		<>
			<section className="sharkr-section">
				<div
					className="sharkr-section-header header-flex"
					style={{ justifyContent: 'space-between' }}>
					<div style={{ display: 'flex' }}>
						<span className="sharkr-section-header-title header-text">
							主图营销模板库
						</span>
						<TeamSpace setTeam={setTeam} />
					</div>
					{/* <div className="sharkr-tools">
						<UmcAuth condition="1636131000023">
							<Button
								className="tool"
								type="default">
								<Link to={`/atmosphere/list`}>合图任务记录</Link>
							</Button>
						</UmcAuth>
						<UmcAuth condition="1636131000022">
							<Button
								className="tool"
								type="primary"
								onClick={() => modalVisibleChange('新建', undefined)}>
								新建模板
							</Button>
						</UmcAuth>
					</div> */}
				</div>

				<div className="sharkr-section-content">
					{/* {data.length > 0 && (
						<TemplateClass
							classFormRef={classFormRef}
							data={data}
							searchParam={searchParam}
							setTemClass={setTemClass}
							teamId={team}
						/>
					)} */}
					<FilterHead
						searchParam={searchParam}
						onChangeSearchParam={(param) => {
							setSearchParam({
								...param,
								pageNo: 1,
								pageSize: 50
							})
						}}
					/>
					<Row
						className="margin-b-base template-card"
						gutter={[30, 30]}>
						{dateSource &&
							dateSource.map((m: any, idx: number) => (
								<Col
									key={`${m}${idx}`}
									span={5}>
									{m.flag ? (
										<>
											<div>
												<div
													style={
														innerWidth > 1500
															? { height: '250px' }
															: { height: '200px' }
													}
													className="m-img-wrap"
													onMouseLeave={() => hover(m, false)}>
													<div
														className={
															m.checked ? 'hoverBorder hoverCard' : 'hoverCard'
														}
														style={
															innerWidth > 1500
																? {
																	height: '250px', margin: '0 5.5%',
																	width: '89%', zIndex: 100
																}
																: {
																	height: '200px', margin: '0 5.5%',
																	width: '89%', zIndex: 100
																}
														}>
														{m.templateFlag == 0 && (
															<Checkbox
																checked={m.checked}
																className="checkbox"
																style={{ position: 'absolute' }}
																onChange={e => checkboxChange(e, m)}></Checkbox>
														)}

														<div className="hoverBox">
															{m.templateFlag == 0 ? (
																<div className="hoverItem">
																	模板ID:
																	<span
																		title={m.templateId}
																		className="spanMarginLeft">
																		{m.templateId}
																	</span>
																</div>
															) : (
																<div className="hoverItem">
																	模板集ID:
																	<span
																		title={m.templateId}
																		className="spanMarginLeft">
																		{m.templateId}
																	</span>
																</div>
															)}
															<div className="hoverItem">
																店铺:
																<span
																	title={getshopName(m)}
																	className="spanMarginLeft">
																	{getshopName(m)}
																</span>
															</div>
															<div className="tagHoverItem">
																标签:
																<span
																	title={m.tagName}
																	className="spanMarginLeft">
																	{m.tagName}
																</span>
															</div>
															<div
																className="hoverItem"
																style={
																	m.templateFlag == 0
																		? { color: 'white' }
																		: { color: 'rgba(0, 0, 0, 0)' }
																}>
																尺寸:
																<span
																	title={m.templateSizeName}
																	className="spanMarginLeft">
																	{m.templateSizeName}
																</span>
															</div>
															<div className="hoverFooter">
																{m.templateFlag == 0 ? (
																	<div
																		className="hoverButton"
																		onClick={() => {
																			m.editFlag ? operateTem(2, m) : browse(m)
																		}}>
																		{m.editFlag ? '编辑' : '查看'}
																	</div>
																) : (
																	<div
																		className="hoverButton"
																		onClick={() => {
																			openDetailModal(m)
																		}}>
																		查看模板
																	</div>
																)}
																<Popover
																	overlayClassName="no-padding-popover"
																	placement="bottom"
																	style={{
																		color: 'red',
																		position: 'absolute',
																		top: '98%',
																		left: '73.5%'
																	}}
																	content={
																		<div className="itemBox">
																			<div
																				onMouseEnter={() => buttonHover('复制')}
																				onMouseLeave={() => buttonHover('')}
																				style={
																					style == '复制'
																						? {
																							background: 'rgba(0, 0, 0, 0.1)'
																						}
																						: { background: 'white' }
																				}
																				id="copyBtn"
																				className="moreOutlinedItem"
																				onClick={() => copyId(m)}>
																				{m.templateFlag == 0
																					? '复制模板ID'
																					: '复制模板集ID'}
																			</div>
																			{m.editFlag && (
																				<div
																					onMouseEnter={() =>
																						buttonHover('创建')
																					}
																					onMouseLeave={() => buttonHover('')}
																					style={
																						style == '创建'
																							? {
																								background:
																									'rgba(0, 0, 0, 0.1)'
																							}
																							: { background: 'white' }
																					}
																					className="moreOutlinedItem"
																					onClick={() => createReplica(m)}>
																					{m.templateFlag == 0
																						? '创建模板副本'
																						: '创建模板集副本'}
																				</div>
																			)}
																			{/* <div
																				className="moreOutlinedItem"
																				onClick={() => transferTeamSpace(m)}>
																				转到团队空间
																			</div> */}
																			{m.templateFlag == 0 && (
																				<div
																					onMouseEnter={() =>
																						buttonHover('导出')
																					}
																					onMouseLeave={() => buttonHover('')}
																					style={
																						style == '导出'
																							? {
																								background:
																									'rgba(0, 0, 0, 0.1)'
																							}
																							: { background: 'white' }
																					}
																					className="moreOutlinedItem"
																					onClick={() => batchMerging(m)}>
																					{/* 查看批量合图表 */}
																					导出批量合图模板
																				</div>
																			)}
																			{m.editFlag && (
																				<div
																					onMouseEnter={() =>
																						buttonHover('分享')
																					}
																					onMouseLeave={() => buttonHover('')}
																					style={
																						style == '分享'
																							? {
																								background:
																									'rgba(0, 0, 0, 0.1)'
																							}
																							: { background: 'white' }
																					}
																					className="moreOutlinedItem"
																					onClick={() => share(m)}>
																					分享编辑权限
																				</div>
																			)}
																			{m.editFlag && (
																				<div
																					onMouseEnter={() =>
																						buttonHover('编辑')
																					}
																					onMouseLeave={() => buttonHover('')}
																					style={
																						style == '编辑'
																							? {
																								background:
																									'rgba(0, 0, 0, 0.1)'
																							}
																							: { background: 'white' }
																					}
																					className="moreOutlinedItem"
																					onClick={() =>
																						modalVisibleChange('编辑', m)
																					}>
																					{m.templateFlag == 0
																						? '编辑模板信息'
																						: '编辑模板集信息'}
																				</div>
																			)}
																			{m.templateFlag == 0 && m.editFlag && (
																				<div
																					onMouseEnter={() =>
																						buttonHover('移入')
																					}
																					onMouseLeave={() => buttonHover('')}
																					style={
																						style == '移入'
																							? {
																								background:
																									'rgba(0, 0, 0, 0.1)'
																							}
																							: { background: 'white' }
																					}
																					className="moreOutlinedItem"
																					onClick={() => add('移入', m)}>
																					移入模板集
																				</div>
																			)}
																			{m.templateFlag == 0 && (
																				<div
																					onMouseEnter={() =>
																						buttonHover('合图')
																					}
																					onMouseLeave={() => buttonHover('')}
																					style={
																						style == '合图'
																							? {
																								background:
																									'rgba(0, 0, 0, 0.1)'
																							}
																							: { background: 'white' }
																					}
																					className="moreOutlinedItem"
																					onClick={() => openModal(m)}>
																					创建合图任务
																				</div>
																			)}
																			{m.editFlag && (
																				<div
																					onMouseEnter={() =>
																						buttonHover('删除')
																					}
																					onMouseLeave={() => buttonHover('')}
																					style={
																						style == '删除'
																							? {
																								background:
																									'rgba(0, 0, 0, 0.1)'
																							}
																							: { background: 'white' }
																					}
																					className="moreOutlinedItem"
																					onClick={() => deleteTem(m)}>
																					删除
																				</div>
																			)}
																		</div>
																	}>
																	<div className="hoverMoreOutlined">
																		<MoreOutlined />
																	</div>
																</Popover>
															</div>
														</div>
													</div>
													<img
														alt="example"
														className="img"
														style={
															innerWidth > 1500
																? { height: '180px' }
																: { height: '150px' }
														}
														src={
															m.casePicUrl
																? m.casePicUrl
																: 'https://yanxuan.nosdn.127.net/static-union/16589077933d0a4a.png'
														}
													/>
												</div>
												<div
													className="m-card-name"
													title={m.templateName}>
													{m.templateName}
												</div>
											</div>
										</>
									) : (
										<>
											<div>
												<div
													style={
														innerWidth > 1500
															? { height: '250px' }
															: { height: '200px' }
													}
													className="m-img-wrap"
													onMouseEnter={() => hover(m, true)}>
													<div
														style={
															innerWidth > 1500
																? {
																	height: '250px',
																	margin: '0 5.5%',
																	width: '89%',
																	background: 'rgba(0, 0, 0, 0)'
																}
																: {
																	height: '200px',
																	margin: '0 5.5%',
																	width: '89%',
																	background: 'rgba(0, 0, 0, 0)'
																}
														}
														className={
															m.checked ? 'hoverBorder hoverCard' : 'hoverCard'
														}>
														{m.checked && m.templateFlag == 0 && (
															<Checkbox
																checked={m.checked}
																className="checkbox"
																onChange={e => checkboxChange(e, m)}></Checkbox>
														)}
													</div>
													<img
														style={
															innerWidth > 1500
																? { height: '180px' }
																: { height: '150px' }
														}
														alt="example"
														className="img"
														src={
															m.casePicUrl
																? m.casePicUrl
																: 'https://yanxuan.nosdn.127.net/static-union/16589077933d0a4a.png'
														}
													/>
													{m.templateFlag == 1 && (
														<div className="smallImg">
															<span className="smallSpan">模板集</span>
														</div>
													)}
												</div>
												<div
													className="m-card-name"
													title={m.templateName}>
													{m.templateName}
												</div>
											</div>
										</>
									)}
								</Col>
							))}
					</Row>
					{dateSource.length ? (
						<div className={'pagination-area'}>
							<Pagination
								{...pagination}
								defaultCurrent={1}
								onChange={handlePaginationChange}
							/>
						</div>
					) : null}
				</div>
				{/* 模板查看弹窗 */}
				<IframeModal
					ref={IframeModalRef}
					savePageInfo={savePageInfo}
					teamId={teamId}
					template={template}
				/>
				{/* 创建合图任务 */}
				<AddDrawingTask
					ref={taskModalRef}
					savePageInfo={savePageInfo}
					teamId={teamId}
					template={template}
				/>
				{/* <CreateCompositeTask
					ref={taskModalRef}
					savePageInfo={savePageInfo}
					teamId={teamId}
					template={template}
				/> */}

				<Modal
					destroyOnClose
					className="templateModal"
					key="templateModal"
					title={isModalTitle}
					visible={isModalVisible}
					width={600}
					onCancel={() => {
						setTemplateForm({})
						setIsModalVisible(false)
					}}
					onOk={onOk}>
					<SharkRForm
						values={templateForm}
						ref={formRef}
						schema={
							isModalTitle == '新建模板' || isModalTitle == '新建模板集'
								? templateSchema
								: editTemplateSchema
						}
						onValuesChange={templateSchemaChange}
					/>
				</Modal>

				<Modal
					destroyOnClose
					title="移动模板"
					visible={isMoveVisible}
					width={600}
					onCancel={() => {
						setIsMovelVisible(false)
					}}
					onOk={onMoveOk}>
					<SharkRForm
						ref={moveFormRef}
						schema={moveSchema}
					/>
				</Modal>
			</section>
			<Drawer
				mask={false}
				placement="bottom"
				closable={false}
				visible={drawer}
				height={80}
				maskClosable={true}
				onClose={onClose}>
				<div>
					<CloseOutlined
						className="close"
						onClick={onClose}></CloseOutlined>
					<Checkbox
						// className='Checkbox'
						// indeterminate={allIndeterminate}
						checked={allCheck}
						onChange={e => allCheckChange(e)}>
						全选
					</Checkbox>
					<span>已选择</span>
					<div className="checkArr">{checkArr.length}</div>
					<Divider
						type="vertical"
						style={{
							background: 'black',
							marginLeft: '20px',
							marginRight: '30px'
						}}
					/>
					<Button
						onClick={() => openTemplateSetModal('新建')}
						style={{ marginLeft: '20px' }}>
						创建模板集
					</Button>
					<Button
						onClick={() => openTemplateSetModal('添加到')}
						style={{ marginLeft: '20px' }}>
						添加到模板集
					</Button>
				</div>
			</Drawer>
			{/* 添加/创建模板集 */}
			<Modal
				destroyOnClose
				className="templateModal"
				key="templateSetModal"
				title={`${templateSetTitle}模板集`}
				visible={templateSetModal}
				width={600}
				onCancel={() => {
					setTemplateSetModal(false)
					setAddTemplateIdList([])
				}}
				onOk={templateSetFormRefOk}>
				<SharkRForm
					ref={templateSetFormRef}
					schema={
						templateSetTitle == '新建'
							? addTemplateSetSchema
							: editTemplateSetSchema
					}
				/>
			</Modal>
			{/*分享编辑权限弹窗  */}
			<Modal
				destroyOnClose
				title="分享编辑权限"
				visible={shareModal}
				onOk={shareModalOk}
				onCancel={() => {
					setShareModal(false)
				}}>
				<Form
					name="shareForm"
					labelCol={{ span: 6 }}
					wrapperCol={{ span: 16 }}
					style={{ maxWidth: 600 }}>
					<Form.Item
						label="添加编辑人"
						name="authorityUser"
					// initialValue={[templateItem?.authorityUser]}
					// rules={[{ required: true, message: '请输入编辑人！' }]}
					>
						<MultiUserSelect
							defaultValue={
								templateItem?.authorityUser
									? templateItem?.authorityUser.split(',')
									: undefined
							}
							onChange={authorityUserChange}
							style={{ width: '100%' }}
						/>
					</Form.Item>
				</Form>
			</Modal>
			{/* 查看模板弹窗 */}
			<Modal
				footer={null}
				destroyOnClose
				keyboard={false}
				maskClosable={false}
				// title="分享编辑权限"
				width="90%"
				visible={detailModalFlag}
				onOk={detailModalOk}
				onCancel={() => {
					setDetailModalFlag(false)
				}}>
				<DetailModal
					innerWidth={innerWidth}
					getRemoveList={getRemoveList}
					onDataChange={onDataChange}
					detailModalEditFlag={detailModalEditFlag}
					detailModalName={detailModalName}
					detailModalId={detailModalId}
					detailModalFlag={detailModalFlag}
					savePageInfo={savePageInfo}
				/>
			</Modal>
		</>
	)
}
