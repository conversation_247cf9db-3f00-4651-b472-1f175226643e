/* eslint-disable @fe-sdk/comments/require-function-comment */
/* eslint-disable complexity */
/* eslint-disable array-callback-return */
import React, { useEffect, useRef, useState } from 'react'
import { Button, Row, Col, Table, Space, Modal, Pagination, Input, Tabs, message, Form } from 'antd'
import './index.scss'
import { PlainObject } from '@shark/core'
import { registerComponent, SharkRForm } from '@sharkr/form'
import _ from 'lodash'
import { SharkRPreviewImg } from '@sharkr/components'
import moment from 'moment'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import { Event } from '@sharkr/utils'
import { getSkuList, getSpuList, getSkuDetail, getSpuDetail, addSku, addSpu, getPic, addBatchSpu, addBatchSku, delSkuImg, delSpuImg } from '../../../services/stmosphereService'
import { UploadTmp, UploadImg } from '../../../components'
import { downloadPic, operateTypeOptions, sizeOptions } from '../../../consts/schemaForm'
import { getUserInfo } from '../../../services'
import { TeamSpace } from '../../components/teamSpace'

interface IAntDPaginationProps {
  current: number
  pageSize: number
  total: number
}

interface IECSQueryProps {
  pageNo: number
  pageSize: number
  teamId: number | null,
  keyword?: string
}

// eslint-disable-next-line max-lines-per-function
export const GalleryList: React.FC<{}> = (props: {}) => {
  const { TabPane } = Tabs
  registerComponent('UploadTmp', UploadTmp)
  registerComponent('UploadImg', UploadImg)
  const [searchParam, setSearchParam] = useState<IECSQueryProps>({
    keyword: undefined,
    pageNo: 1,
    pageSize: 10,
    teamId: null
  })
  const [team, setTeam] = useState<number | null>(null)
  const [dateSource, setDateSource] = useState<any[]>([])
  const [tabKey, setTabKey] = useState<string>('1')
  const [pagination, setPagination] = useState<IAntDPaginationProps | {}>({})
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [imgShow, setImgShow] = useState(false)
  const [imgType, setImgType] = useState<any>(undefined)
  const [imgUrl, setImgUrl] = useState<any>(undefined)
  const [record, setRecord] = useState<any>(null)
  const [storageId, setStorageId] = useState<any>(null)
  const formRef = useRef<any>(null)
  const searchRef = useRef<any>(null)
  const schema: any = [
    {
      key: 'picSize',
      type: 'Radio',
      label: '模板尺寸',
      ui: {
        required: true
      },
      value: 1,
      rules: [
        {
          required: true,
          message: '请选择模板尺寸'
        }
      ],
      options: sizeOptions
    },
    {
      key: 'distributionSpuId',
      type: 'Input',
      label: `渠道${imgType === 1 ? '商品id' : 'skuId'}`,
      ui: {
        required: true
      },
      props: {
        placeholder: '请输入'
      },
      listeners: [
        {
          watch: ['operateType'],
          condition: true,
          set: (valid, field, form) => {
            const type = form.getValue('operateType')
            field.set({
              status: type === 1 || storageId ? 'edit' : 'hidden',
            })
          }
        }
      ],
      rules: [
        {
          required: true,
          message: '请输入渠道商品id'
        },
        {
          pattern: /^\S+/,
          message: '请输入数字、符号或字母'
        }
      ]
    },
    {
      key: 'operateType',
      type: 'Radio',
      label: '商品图片',
      ui: {
        required: true,
        extra: `请将图片名称命名为渠道商品id后再进行上传，相同名称的图片将按最新上传的进行覆盖`,
      },
      rules: [
        {
          required: true,
          message: '请选择模板框架'
        }
      ],
      options: operateTypeOptions
    },
    {
      key: 'picUrl',
      type: 'UploadTmp',
      label: '图片上传',
      status: 'hidden',
      ui: {
        required: true,
      },
      listeners: [
        {
          watch: ['operateType', 'picSize'],
          condition: true,
          set: (valid, field, form) => {
            const type = form.getValue('operateType')
            const picType = form.getValue('picSize')
            const imgSize1 = { width: 800, height: 800 }
            const imgSize2 = { width: 750, height: 1000 }
            const imgSize3 = { width: 800, height: 1200 }
            field.set({
              status: type === 2 && !storageId ? 'edit' : 'hidden',
              props: {
                maxFiles: 20,
                listType: 'picture-card',
                accept: 'image/*',
                imgSize: 3 * 1024,
                imgWh: picType
                  ? picType === 1
                    ? imgSize1
                    : picType === 2
                    ? imgSize2
                    : imgSize3
                  : null
              }
            })
          }
        }
      ]
    },


    {
      key: 'picUrlImg',
      type: 'UploadImg',
      label: '图片上传',
      status: 'hidden',
      ui: {
        required: true
      },
      listeners: [
        {
          watch: ['operateType', 'picSize'],
          condition: true,
          set: (valid, field, form) => {
            const type = form.getValue('operateType')
            const picType = form.getValue('picSize')
            const imgSize1 = { width: 800, height: 800 }
            const imgSize2 = { width: 750, height: 1000 }
            const imgSize3 = { width: 800, height: 1200 }
            field.set({
              status: type === 2 && storageId ? 'edit' : 'hidden',
              props: {
                max: 1,
                listType: 'picture-card',
                accept: 'image/*',
                imgSize: 3072,
                imgWh: picType ? (picType === 1 ? imgSize1 : picType === 2 ? imgSize2 : imgSize3) : null
              }
            })
          }
        }
      ]
    },


    {
      key: 'id',
      type: 'Input',
      label: `严选${imgType === 1 ? '商品id' : 'skuId'}`,
      ui: {
        required: true,
        size: 'lg'
      },
      props: {
        addonAfter: (
          <Button type="primary" onClick={() => getPicurl()}>
            拉取商品图
          </Button>
        )
      },
      rules: [
        {
          required: true,
          message: '请输入'
        },
        {
          pattern: /^[1-9]\d*|0$/,
          message: '请输入正确的数字'
        }
      ],
      listeners: [
        {
          watch: ['operateType'],
          condition: true,
          set: (valid, field, form) => {
            const type = form.getValue('operateType')
            field.set({
              status: type === 1 ? 'edit' : 'hidden',
              props: {
                placeholder: `请输入严选${imgType !== 2 ? '商品id' : 'skuId'}`
              }
            })
          }
        }
      ]
    }
  ]
  const searchSchema: any = [
    {
      key: 'keyword',
      type: 'Input',
      label: '渠道商品id',
      props: {
        placeholder: '请输入渠道商品id进行搜索',
        allowClear: true
      }
    }
  ]

  const [editSchema, setEditSchema] = useState(schema)
  useEffect(() => {
    if (team) {
      setSearchParam({ ...searchParam, teamId: team })
    }
  }, [team])
  const showModal = () => {
    setIsModalVisible(true)
  }
  // 新增主图或sku图片
  const onAdd = (type: boolean, sku: any) => {
    if (type) {
      setImgType(2)
    } else {
      setImgType(1)
    }
    showModal()
  }
  const imgMsg = (res: any) => {
    if (res?.code === 4811) {
      Modal.confirm({
        title: '已存在相同名称的图片，确定要按当前上传的图片进行覆盖吗?',
        icon: <ExclamationCircleOutlined translate={undefined} />,
        content: res?.message || 'xxx.png冲突',
        okText: '确认',
        onOk: () => handleOk(true),
        cancelText: '取消',
      });
    } else {
      message.error(res?.message)
    }
  }
  const handleOk = (isCoverl = false) => {
    formRef &&
      formRef.current &&
      formRef.current.submit().then(async data => {
        const isImg = chickImg(data.operateType)
        if (!isImg) {
          return null
        }

        if (record || data.operateType === 1) {
          const params = {
            id: record ? record.id : 0,
            operateType: data.operateType,
            picUrl: data.operateType === 1 ? imgUrl[0]?.fileUrl : data.picUrlImg[0].fileUrl,
            cover: isCoverl,
            teamId: team
          }
          if (data.picSize) {
            const res = await addSpu({
              ...params,
              picSize: data.picSize,
              distributionSpuId: data.distributionSpuId,
              spuId: data.operateType === 1 ? Number(data.id) : null
            })
            if (res.code === 200) {
              message.success('操作成功')
              handleCancel()
              tabKey !== '3' && choiceList()
            } else {
              imgMsg(res)
            }
          } else {
            const res = await addSku({
              ...params,
              distributionSkuId: Number(data.distributionSpuId),
              skuId: data.operateType === 1 ? Number(data.id) : null
            })
            if (res.code === 200) {
              message.success('操作成功')
              handleCancel()
              tabKey === '3' && choiceList()
            } else {
              imgMsg(res)
            }
          }
        } else {
          // const error: string[] = []
          // data.picUrl && data.picUrl.map((item) => {
          //   if (isNaN(Number(item.fileName.split('.')[0]))) {
          //     error.push(item.fileName)
          //   }
          //   return item
          // })

          // if (error.length !== 0) {
          //   Modal.info({
          //     title: '文件命名错误 文件名（即渠道商品id）',
          //     content: (
          //       <div>
          //         {
          //           error.map((item, idx) => {
          //             return (
          //               <p key={idx}>{item}</p>
          //             )
          //           })
          //         }
          //       </div>
          //     ),
          //     onOk() { },
          //   });
          //   return;
          // }

          if (data.picSize) {
            const params: any[] = []
            data.picUrl && data.picUrl.map((item) => {
              params.push({
                id: 0,
                operateType: data.operateType,
                picSize: data.picSize,
                picUrl: item.fileUrl.split('?')[0],
                distributionSpuId: item.fileName.split('.')[0],
                spuId: null,
                cover: isCoverl,
                teamId: team
              })
            })
            const res = await addBatchSpu(params)
            if (res.code === 200) {
              message.success('操作成功')
              handleCancel()
              tabKey !== '3' && choiceList()
            } else {
              imgMsg(res)
            }
          } else {
            const params: any[] = []
            data.picUrl && data.picUrl.map((item) => {
              params.push({
                id: 0,
                operateType: data.operateType,
                picUrl: item.fileUrl.split('?')[0],
                distributionSkuId: Number(item.fileName.split('.')[0]),
                skuId: null,
                cover: isCoverl,
                teamId: team
              })
            })
            const res = await addBatchSku(params)
            if (res.code === 200) {
              message.success('操作成功')
              handleCancel()
              tabKey === '3' && choiceList()
            } else {
              imgMsg(res)
            }
          }
        }
      })
  }

  const chickImg = (type: any) => {
    let hasImg = true
    if (type === 1) {
      if (!imgUrl || !imgUrl?.length) {
        message.warning('请先点击拉取商品图获取商品图片后再确定')
        hasImg = false
      }
    }
    return hasImg
  }

  const handleCancel = () => {
    setIsModalVisible(false)
    setEditSchema(schema)
    setImgType(undefined)
    setImgUrl([])
    setImgShow(false)
    setRecord(null)
    setStorageId(null)
  }
  // 拉取商品图片
  const getPicurl = async () => {
    if (formRef && formRef.current && formRef.current.getValue('id')) {
      const type = formRef.current.getValue('picSize') ? 1 : 2
      const res = await getPic({ id: formRef.current.getValue('id'), type })
      if (res.code === 200) {
        res.data !== '' && setImgUrl([{ fileName: res.data, fileUrl: res.data }])
        res.data === '' && showPicError()
      } else {
        message.error('获取详情失败')
      }
    }
  }
  const showPicError = () => {
    message.error('该商品id没有商品图，请重新输入拉取')
    formRef && formRef.current.setValue({ id: null })
    setImgUrl([])
  }
  // 搜索
  const onSearch = () => {
    searchRef && searchRef.current && searchRef.current.submit().then(data => {
      const params = {
        ...searchParam,
        ...data,
        pageNo: 1,
        pageSize: 10
      }
      setSearchParam(params)
    })
  }
  // 编辑
  const onEdit = (id: string) => {
    if (tabKey === '1' || tabKey === '2' || tabKey === '4') {
      setImgType(1)
      setStorageId(id)
    } else {
      setImgType(2)
      setStorageId(id)
    }
    showModal()
  }

  const columns: any = [
    {
      title: '渠道商品id',
      width: 100,
      dataIndex: tabKey === '3' ? 'distributionSkuId' : 'distributionSpuId',
      key: tabKey === '3' ? 'distributionSkuId' : 'distributionSpuId'
    },
    {
      title: '添加时间',
      dataIndex: 'createTime',
      key: 'createTime',
      render: (createTime: any) => {
        return <span>{moment(createTime).format('YYYY-MM-DD HH:mm:ss')}</span>
      }
    },
    {
      title: '修改时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      render: (createTime: any) => {
        return <span>{moment(createTime).format('YYYY-MM-DD HH:mm:ss')}</span>
      }
    },
    {
      title: '上次操作者',
      dataIndex: 'updateUser',
      key: 'updateUser'
    },
    {
      title: '缩略图',
      dataIndex: 'picUrl',
      key: 'picUrl',
      render: (picUrl: any) => {
        return picUrl && <SharkRPreviewImg alt={picUrl} className="bg-gray" src={picUrl} />
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 300,
      render: (text: any, record: PlainObject) => {
        const isOneUser = (getUserInfo()?.email === record?.updateUser)
        return (
          <div className="sharkr-table-actions Operation">
            <Button className="action" type="link" onClick={() => onEdit(record.id)}>
              编辑
            </Button>
            <Button className="action" type="link" onClick={() => downloadPic(record.picUrl)}>
              下载
            </Button>
            <Button className="action" disabled={!isOneUser} type="link" onClick={() => confirm(record.id, tabKey === '3' ? false : true)}>
              删除
            </Button>
          </div>
        )
      }
    }
  ]

  const changeSchema = (data: any, type) => {
    const isSpu = type === 1
    const schemas = schema.map((item: any) => {
      if (item.key === 'distributionSpuId') {
        item.value = isSpu ? data.distributionSpuId : data.distributionSkuId
        item.status = 'view'
      } else if (item.key === 'operateType') {
        item.value = data.operateType
      } else if (item.key === 'picSize') {
        item.value = data.picSize
        item.status = 'view'
      } else if (item.key === 'id') {
        data.operateType === 1 && (item.value = isSpu ? data.spuId : data.skuId)
      } else if (item.key === 'picUrlImg') {
        data.operateType === 2 && (item.value = [{ fileName: data.picUrl, fileUrl: data.picUrl }])
        data.operateType === 1 && setImgUrl([{ fileName: data.picUrl, fileUrl: data.picUrl }])
        data.operateType === 1 && setImgShow(true)
      }
      return item
    })
    type === 2 && schemas.splice(0, 1)
    setEditSchema(_.cloneDeep(schemas))
    setRecord(data)
  }

  // sku编辑对配置化表单赋值
  const skuDetail = async (id: any) => {
    const res = await getSkuDetail({ id })
    if (res.code === 200) {
      const { data } = res
      changeSchema(data, 2)
    } else {
      message.error('获取详情失败')
    }
  }
  // 主图编辑对配置化表单赋值
  const spuDetail = async (id: any) => {
    const res = await getSpuDetail({ id })
    if (res.code === 200) {
      const { data } = res
      changeSchema(data, 1)
    } else {
      message.error('获取详情失败')
    }
  }

  // 获取spu列表
  const getSpu = async (params: any) => {
    const res = await getSpuList(params && params)
    if (res && res.code === 200) {
      const { result, paginationVO } = res.data || { result: [], paginationVO: {} }
      const antdPagination: IAntDPaginationProps = {
        current: paginationVO.page,
        pageSize: paginationVO.size,
        total: paginationVO.total
      }
      setPagination(antdPagination)
      setDateSource(result)
    } else {
      setPagination( {
        current: 1,
        pageSize: 10,
        total: 0
      })
      setDateSource([])
      message.error('获取信息失败')
    }
  }
  // 获取sku列表
  const getSku = async (params: any) => {
    const res = await getSkuList(params && params)
    if (res && res.code === 200) {
      const { result, paginationVO } = res.data || { result: [], paginationVO: {} }
      const antdPagination: IAntDPaginationProps = {
        current: paginationVO.page,
        pageSize: paginationVO.size,
        total: paginationVO.total
      }
      setPagination(antdPagination)
      setDateSource(result)
    } else {
      message.error('获取详情失败')
    }
  }
  const onChange = (activeKey: string) => {
    const params = {
      ...searchParam,
      pageNo: 1,
      pageSize: 10
    }
    setTabKey(activeKey)
    setSearchParam(params)
  } // 改变页码
  const handlePaginationChange = (page: number, pageSize = 10) => {
    setSearchParam({
      ...searchParam,
      pageNo: page,
      pageSize
    })
  }
  const confirm = (id: number, isSpu = true) => {
    Modal.confirm({
      title: '删除图片',
      icon: <ExclamationCircleOutlined translate={undefined} />,
      content: '是否确认删除该图片',
      okText: '确认',
      cancelText: '取消',
      onOk: () => imgDelete(id, isSpu)
    });
  };
  const imgDelete = async (id: number, isSpu = true) => {
    const res = isSpu ? await delSpuImg({ id }) : await delSkuImg({ id })
    if (res?.code === 200) {
      choiceList()
      message.success('删除成功')
    } else {
      message.error('删除失败，请重试')
    }
  }

  const onValuesChange = (changedValues, allValues) => {
    if (changedValues.operateType) {
      changedValues.operateType === 1 && setImgShow(true)
      changedValues.operateType === 2 && setImgShow(false)
    }
  }

  const choiceList = () => {
    if (!team) return null
    if (tabKey === '1' || tabKey === '2' || tabKey === '4') {
      getSpu({
        ...searchParam,
        picSize: tabKey === '1' ? 2 : tabKey === '2' ? 1 : 3
      })
    } else {
      getSku(searchParam)
    }
  }

  useEffect(() => {
    if (storageId) {
      imgType === 2 && skuDetail(storageId)
      imgType === 1 && spuDetail(storageId)
    } else {
      const addSchema = [...schema]
      if (imgType === 2) {
        addSchema.splice(0, 1)
      }
      setEditSchema(addSchema)
    }
  }, [imgType, storageId])

  useEffect(() => {
    choiceList()
  }, [searchParam, tabKey])


  const renderTable = (
    <>
      <Table
        className="sharkr-table"
        columns={columns}
        dataSource={dateSource}
        pagination={false}
      />
      {dateSource.length ? (
        <div className={'pagination-area'}>
          <Pagination {...pagination} defaultCurrent={1} onChange={handlePaginationChange} />
        </div>
      ) : null}
    </>
  )

  return (
    <>
      <section className="sharkr-section">
        <div className="sharkr-section-header header-flex">
          <span className="sharkr-section-header-title">商品主图库</span>
          <TeamSpace setTeam={setTeam} />
        </div>
        <div className="sharkr-section-content">
          <SharkRForm
            className="sharkr-form-inline searchForm"
            ref={searchRef}
            schema={searchSchema}
            {...{
              labelCol: { span: 5 },
              wrapperCol: { span: 19 }
            }}
          />
          <Row>
            <Col span={8} xl={8} xxl={6}>
              <Row>
                <Col offset={5}>
                  <Space>
                    <Button style={{ marginBottom: '10px' }} type="primary" onClick={onSearch}>
                      搜索
                    </Button>
                  </Space>
                </Col>
              </Row>
            </Col>
          </Row>
          <Tabs
            destroyInactiveTabPane
            className="sharkr-section-tabs sharkr-section-tabs-border"
            defaultActiveKey="1"
            tabBarExtraContent={
              <div className="sharkr-tools">
                <Space>
                  <Button className="tool" type="primary" onClick={() => onAdd(false, 1)}>
                    新增主图
                  </Button>
                  <Button className="tool" type="primary" onClick={() => onAdd(true, 2)}>
                    新增sku图
                  </Button>
                </Space>
              </div>
            }
            type="card"
            onChange={onChange}>
            <TabPane key="1" tab="750*1000主图">
              {renderTable}
            </TabPane>
            <TabPane key="2" tab="800*800主图">
              {renderTable}
            </TabPane>
            <TabPane key="4" tab="800*1200主图">
              {renderTable}
            </TabPane>
            <TabPane key="3" tab="sku图">
              {renderTable}
            </TabPane>
          </Tabs>
        </div>
        <Modal
          destroyOnClose
          title={`${record ? '编辑' : '新增'}${imgType === 1 ? '商品图' : 'sku图'}`}
          visible={isModalVisible}
          width={600}
          onCancel={handleCancel}
          onOk={() => handleOk()}>
          <SharkRForm ref={formRef} schema={editSchema} onValuesChange={onValuesChange} />
          {imgShow && imgUrl && imgUrl.length ? (
            <Row>
              <Col offset={6}>
                <SharkRPreviewImg
                  alt={imgUrl[0].fileUrl}
                  className="bg-gray"
                  height={96}
                  src={imgUrl[0].fileUrl}
                  width={96}
                />
              </Col>
            </Row>
          ) : null}
        </Modal>
      </section>
    </>
  )
}
