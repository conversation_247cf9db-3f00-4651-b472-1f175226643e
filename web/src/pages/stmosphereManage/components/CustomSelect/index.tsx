import { Select, Spin, message } from 'antd';
import type { SelectProps } from 'antd/es/select';
import debounce from 'lodash/debounce';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { getTemplateListByIdOrName } from '../../../../services/stmosphereService';

export interface DebounceSelectProps<ValueType = any>
    extends Omit<SelectProps<ValueType | ValueType[]>, 'options' | 'children'> {
    fetchOptions: (search: string) => Promise<ValueType[]>;
    debounceTimeout?: number;
}

function DebounceSelect<
    ValueType extends { key?: string; label: React.ReactNode; value: string | number } = any,
>({ fetchOptions, debounceTimeout = 800, ...props }: DebounceSelectProps<ValueType>) {
    const [fetching, setFetching] = useState(false);
    const [options, setOptions] = useState<ValueType[]>([]);
    const fetchRef = useRef(0);

    const debounceFetcher = useMemo(() => {
        const loadOptions = (value: string) => {
            fetchRef.current += 1;
            const fetchId = fetchRef.current;
            setOptions([]);
            setFetching(true);

            fetchOptions(value).then(newOptions => {
                if (fetchId !== fetchRef.current) {
                    // for fetch callback order
                    return;
                }

                setOptions(newOptions);
                setFetching(false);
            });
        };

        return debounce(loadOptions, debounceTimeout);
    }, [fetchOptions, debounceTimeout]);

    return (
        <Select
            labelInValue
            filterOption={false}
            onSearch={debounceFetcher}
            notFoundContent={fetching ? <Spin size="small" /> : null}
            {...props}
            options={options}
            allowClear
        />
    );
}

// Usage of DebounceSelect
interface UserValue {
    label: string;
    value: string;
}

async function fetchTemplateList(keyWord: string): Promise<UserValue[]> {
    // console.log('keyWord', keyWord);

    const res = await getTemplateListByIdOrName({ keyWord })

    if (res?.code === 200) {
        const data =
            res?.data?.map(item => {
                // item.label = `#${item.templateId} ${item.templateName}`
                // item.label = item.templateName
                // item.value = item.templateId
                // item.rawData = item;

                const { templateName:label, templateId:value} = item || {};

                return {
                    label,
                    value,
                    rawData: item
                }
            }) ?? []
            
        console.log('data', data);
        return data;
    } else {
        message.error('查询模板出问题了')
        return []
    }

    // return fetch('https://randomuser.me/api/?results=5')
    //     .then(response => response.json())
    //     .then(body =>
    //         body.results.map(
    //             (user: { name: { first: string; last: string }; login: { keyWord: string } }) => ({
    //                 label: `${user.name.first} ${user.name.last}`,
    //                 value: user.login.keyWord,
    //             }),
    //         ),
    //     );
}

const CustomSelect: React.FC<any> = ({ onChange, value, field, ...otherProps }) => {

    const [val, setVal] = useState<any>(value);

    // 添加自定义校验逻辑
    const onValidate = useCallback(async (trigger, value) => {
        let valid = true;
        let message = '';
        if ((['submit', 'blur'].includes(trigger)) && (value === undefined ||value === null)) {
            valid = false;
            message = '请输入模板ID/名称';
        }
        const status = valid ? 'normal' : 'error';
        return { valid, message, status };
    }, []);

    // 添加重置逻辑
    const onReset = useCallback(() => {
        setVal(value);

        onChange(undefined);
    }, []);

    useEffect(() => {
        // 注册自定义校验方法
        field.onValidate(onValidate);
        // 订阅重置事件
        field.on('RESET', onReset);

        return () => {
            field.off('RESET', onReset);
        };
    }, []);

    const handleChange = (newValue, option) => {
        setVal(newValue);


        const { rawData } = option || {};

        // 调用onChange方法，将更新的值传递出去
        onChange(newValue? { ...newValue, rawData } : rawData);
    }

    return (
        <DebounceSelect
            value={val}
            placeholder="输入模板ID/名称"
            fetchOptions={fetchTemplateList}
            onChange={handleChange}
            showSearch
            {...otherProps}
        // style={{ width: '100%' }}
        />
    );
};

export default CustomSelect;