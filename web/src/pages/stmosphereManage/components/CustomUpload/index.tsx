import { Button, Col, Row, Select, Spin, Upload, UploadFile, UploadProps, message, } from 'antd';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { getTemplateListByIdOrName } from '../../../../services/stmosphereService';
import { UploadOutlined } from '@ant-design/icons';


const CustomUpload: React.FC<any> = ({ onChange, value, field, ...otherProps }) => {

    console.log("CustomUpload", field, otherProps)

    const { disabled = false, templateData } = otherProps || {};

    const downloadUrl = templateData?.downloadUrl;

    console.log("downloadUrl", downloadUrl)

    const [fileList, setFileList] = useState<UploadFile[]>([]);

    // 添加自定义校验逻辑
    const onValidate = useCallback(async (trigger, value) => {
        let valid = true;
        let message = '';
        if ((['submit', 'blur'].includes(trigger)) && (value === undefined || value === null || value?.length === 0)) {
            valid = false;
            message = '请上传批量合图表格';
        }
        const status = valid ? 'normal' : 'error';
        return { valid, message, status };
    }, []);

    // 添加重置逻辑
    const onReset = useCallback(() => {
        setFileList(value);

        onChange(undefined);
    }, []);

    // 下载excel模板
    const downLoadExcel = () => {
        if (downloadUrl) {
            window.open(downloadUrl)
        } else {
            message.warn("未选择模板或无模板文件")
        }
    }

    useEffect(() => {
        // 注册自定义校验方法
        field.onValidate(onValidate);
        // 订阅重置事件
        field.on('RESET', onReset);

        return () => {
            field.off('RESET', onReset);
        };
    }, []);

    const props: UploadProps = {
        onRemove: file => {
            setFileList([]);
            onChange([]);
        },
        beforeUpload: file => {
            const isExcel = ['application/vnd.ms-excel', "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"].includes(file.type); //xls格式文件';
            if (!isExcel) {
                message.error(`${file.name} 不是excel文件`);
                return;
            }

            setFileList([file]);
            onChange([file]);
            return false;
        },
        maxCount: 1,
        fileList,
        // accept
    };

    return (
        <Row>
            <Col span={8}>
                <Upload {...props} fileList={fileList} >
                    <Button icon={<UploadOutlined />} disabled={disabled} size="middle">上传文件</Button>
                </Upload>
            </Col>

            {/* {!!downloadUrl && */}
            <Button type="link" disabled={disabled} onClick={downLoadExcel} >
                    点击下载模板
                </Button>
            {/* } */}
        </Row>

    );
};

export default CustomUpload;