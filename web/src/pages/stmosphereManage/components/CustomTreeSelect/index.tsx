import { Select, Spin, TreeSelect, message } from 'antd';
import type { SelectProps } from 'antd/es/select';
import debounce from 'lodash/debounce';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { getTemplateListByIdOrName } from '../../../../services/stmosphereService';
import { getTagList } from '../../../../services/personalServices';


const CustomTreeSelect: React.FC<any> = ({ onChange, value, field, ...otherProps }) => {

    const [val, setVal] = useState<any>(value);
    const [treeData, setTreeData] = useState<any>([]);

    // 添加自定义校验逻辑
    const onValidate = useCallback(async (trigger, value) => {
        let valid = true;
        let message = '';
        if ((['submit', 'blur'].includes(trigger)) && (value === undefined || value === null || value?.length === 0)) {
            valid = false;
            message = '请选择标签';
        }
        const status = valid ? 'normal' : 'error';
        return { valid, message, status };
    }, []);

    // 添加重置逻辑
    const onReset = useCallback(() => {
        setVal(value);

        onChange(undefined);
    }, []);

    useEffect(() => {
        // 注册自定义校验方法
        field.onValidate(onValidate);
        // 订阅重置事件
        field.on('RESET', onReset);

        getAllTags();

        return () => {
            field.off('RESET', onReset);
        };
    }, []);

    const getAllTags = async () => {
        const res = await getTagList();
        let result = []
        if (res?.code === 200) {
            result = res?.data?.map(item => {
                item.fakeTitle = item.title
                item?.children?.forEach(child => {
                    child.key = `${item.key}-${child.key}`;
                    child.fakeTitle = `${item.title} > ${child.title}`;
                })
                return item

            }) ?? []

        } else {
            message.error('获取标签列表失败')
        }

        setTreeData(result);
    }

    const handleChange = (newValue) => {
        setVal(newValue);

        onChange(newValue)
    }

    const tProps = {
        treeData,
        value: val,
        onChange: handleChange,
        placeholder: '请选择标签',
        allowClear: true,
        treeCheckable: true,
        showCheckedStrategy: TreeSelect.SHOW_CHILD,
        treeDefaultExpandAll: true,
        multiple: true,
        treeNodeLabelProp: "fakeTitle",
    };

    return <TreeSelect {...tProps} {...otherProps} />;
};

export default CustomTreeSelect;