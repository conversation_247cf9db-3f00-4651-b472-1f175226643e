/* eslint-disable array-callback-return */
import { SharkRPreviewImg } from '@sharkr/components'
import { message, Radio, RadioChangeEvent } from 'antd'
import React, { useCallback, useEffect, useState } from 'react'
import { getTemplateList } from '../../../services/stmosphereService'

const RadioConfig: React.FC<any> = (props: any) => {
  const { onChange, onBlur, size, field, templateList, giftType, serviceType, templateFrame } =
    props
  const [value, setValue] = useState<any>()
  const [sizeType1, setSizeType1] = useState<any[]>()
  const [sizeType2, setSizeType2] = useState<any[]>()
  // 添加自定义校验逻辑
  const onValidate = useCallback(async (trigger, value) => {
    let valid = true
    let message = ''
    if (trigger === 'submit' && !value) {
      valid = false
      message = '请输入关键词'
    }
    const status = valid ? 'normal' : 'error'
    return { valid, message, status }
  }, [])

  // 添加重置逻辑
  const onReset = useCallback(() => {
    setValue(null)

    onChange({
      val: null
    })
  }, [])

  // 去掉过滤条件值空的键
  const removeEmpty = (value: any) => {
    const param = value
    Object.keys(param).map((item: any) => {
      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
      param[item] === null && delete param[item]
    })
    return param
  }

  // data：原始数据，类型为数组；params：过滤条件，类型为对象；
  const productFilter = (data, params) => {
    const filters = removeEmpty(params)
    const values = data?.filter(item =>
      Object.keys(filters).every(key => item[key] && item[key] === filters[key])
    )
    if (values.length) {
      const type1: any = []
      const type2: any = []
      values.map((item: any) => {
        item.templateType === 2 && type1.push(item)
        item.templateType === 3 && type2.push(item)
      })
      setSizeType1(type1)
      setSizeType2(type2)
    } else {
      setSizeType1([])
      setSizeType2([])
    }
  }

  // 获取对应模板
  const obtainTemplate = () => {
    const params = {
      templateSize: size,
      giftType: giftType && giftType,
      serviceType: serviceType && serviceType,
      templateFrame: templateFrame && templateFrame
    }
    productFilter(templateList, params)
    // return dataItem[0]
  }
  const inputChange = (e: any) => {
    const value = e.target.value
    setValue(value)
    // 调用onChange方法，将更新的值传递出去
    onChange(value)
  }
  useEffect(() => {
    obtainTemplate()
  }, [size, templateList, giftType, serviceType, templateFrame])

  useEffect(() => {
    // obtainTemplate()
    // getTemplate()
    // 注册自定义校验方法
    field.onValidate(onValidate)
    // 订阅重置事件
    field.on('RESET', onReset)

    return () => {
      field.off('RESET', onReset)
    }
  }, [])

  return (
    <Radio.Group style={{ marginBottom: '24px' }} value={value} onChange={inputChange}>
      <Radio value={2}>
        <span>通用型</span>
        {sizeType1 && sizeType1.length ? (
          <SharkRPreviewImg
            alt={sizeType1[0].casePicUrl}
            className="bg-gray radioStyle"
            height={96}
            src={sizeType1[0].casePicUrl}
            width={96}
          />
        ) : null}
      </Radio>
      <Radio value={3}>
        <span>赠品型</span>
        {sizeType2 && sizeType2.length ? (
          <SharkRPreviewImg
            alt={sizeType2[0].casePicUrl}
            className="bg-gray radioStyle"
            height={96}
            src={sizeType2[0].casePicUrl}
            width={96}
          />
        )
          : null}
      </Radio>
    </Radio.Group>
  )
}

export default RadioConfig
