import React, { useEffect, useRef, useState, useMemo } from 'react'
import { Button, Row, Col, Space, Input, Card, Modal, message, Spin, Breadcrumb } from 'antd'
import { Link } from 'react-router-dom'
import './index.scss'
import Postmate from 'postmate'
import { SharkRLoading, SharkRPreview, getUserInfo } from '@sharkr/components'
import { SharkRForm } from '@sharkr/form'
import {
  addTask,
  delPicMeta,
  getImgDetail,
  getPicElement,
  replyAddTask
} from '../../../services/stmosphereService'
import { extendTemplateData, getUrlParam } from '../../../utils/tools'

export const StmosphereDetail: React.FC<{}> = (props: {}) => {
  const container = useRef<any>()
  const editUrl = `${location.protocol}` + '//' + window.location.host + '/distribution-operation/oly-miriam-editor#/micro/save'
  const host = `${location.protocol}//${window.location.host}`
  const iframe = useRef<Postmate.ParentAPI>()
  const urlParms = getUrlParam()
  const id = urlParms && urlParms.id
  const [searchId, setSearchId] = useState<any>(null)
  const [data, setData] = useState<any>(null)
  const [visible, setVisible] = useState(false)
  const [picUrl, setPicUrl] = useState<any[]>([])
  const [canEdit, setCanEdit] = useState(false)


  const [isSpin, setIsSpin] = useState(false)
  const [isIframeModalVisible, setIsIframeModalVisible] = useState(false)
  const searchRef = useRef<any>(null)

  const userEmail = getUserInfo()?.email;


  const handleCancel = () => {
    setVisible(false)
  }
  // 根据id搜索
  const onSearch = () => {
    searchRef && searchRef.current && searchRef.current.submit().then(data => {
      setSearchId(data.keyword)
    })
  }

  // 获取详情
  const getDetail = async () => {
    const res = await getImgDetail({ taskId: id, keyword: searchId && searchId })
    if (res && res.code === 200) {
      res.data && setData(res.data)

      //判断是否显示分享编辑权限，有的话则给true，没有则给false
      const editFlag = userEmail === res.data?.createUser || res.data?.authorityUser?.includes(userEmail);
      setCanEdit(editFlag)

    } else {
      message.error('获取详情失败')
    }
  }

  const searchSchema = useMemo(() => {
    const value: any = [
      {
        key: 'templateName',
        type: 'Input',
        label: '已选模板',
        status: 'view',
        value: data?.templateName
      },
      {
        key: 'activityName',
        type: 'Input',
        label: '活动名称',
        status: 'view',
        value: data?.activityName
      },
      {
        key: 'keyword',
        type: 'Input',
        label: '图片名称',
        props: {
          placeholder: '请输入图片名称进行搜索',
          allowClear: true
        }
      }
    ]
    return value

  }, [data])

  // 预览
  const handlePreview = (picUrl: string) => {
    const url = picUrl ? picUrl : 'https://yanxuan.nosdn.127.net/static-union/16589077933d0a4a.png'
    setPicUrl([`${url}?imageView&thumbnail=600x600&quality=60`])
    // setPicUrl([url])
    setVisible(true)
  }

  const imgEdit = (record: any) => {
    setIsSpin(true)
    setIsIframeModalVisible(true)
    getElement(record)
  }
  const getElement = async (record: any) => {
    const res = await getPicElement({ taskId: Number(data.taskId), picId: Number(record.picId) })
    if (res && res.code === 200 && res.data) {
      console.log(res)
      const extendData = await extendTemplateData(res.data.templateVO)

      const microStorageMessage = {
        message: {
          template: { ...res.data?.templateVO, ...extendData },
          schema: { ...res.data?.schemaDTO },
        },
        outputFormat: res.data?.outputFormat,
        redirectUrl: window.location.href,
        status: 'pending', // 状态 pending:等待编辑结果 done：已编辑完成,
        record: record,
        data: data,
      }
      localStorage.setItem('storageKey', JSON.stringify(microStorageMessage))
      setTimeout(() => {
        window.location.href = editUrl + `?storageKey=storageKey`
      }, 100);

    } else {
      message.error('获取详情失败')
    }
  }

  const reset = () => {
    searchRef && searchRef.current && searchRef.current.setValue({ keyword: null })
    setSearchId(null)
  }

  const batchDownload = () => {
    data && data.downloadUrl && window.open(data.downloadUrl)
    // 下载
  }
  // 删除图片
  const delImg = async (m) => {
    console.log(m)
    const res = await delPicMeta({ taskId: data.taskId, picId: m.picId })
    if (res && res.code === 200) {
      message.success('删除成功')
      getDetail()
    } else {
      message.error(res.message)
    }
  }
  const recombining = async () => {
    const res = await replyAddTask({ taskId: data.taskId, })
    if (res && res.code === 200) {
      message.success('操作成功')
      getDetail()
    } else {
      message.error(res.message)
    }
  }


  useEffect(() => {
    const storageKey = JSON.parse(localStorage.getItem('storageKey') || "null")
    if (storageKey && storageKey.status === 'done') {
      const params = {
        activityName: storageKey.data.activityName,
        schemaDTO: storageKey.message.schema,
        picId: storageKey.record.picId,
        taskId: storageKey.data.taskId,
        picName: storageKey.record.picName,
        teamId: storageKey.message.template.teamId,
        outputFormat: storageKey.outputFormat,
        templateId: storageKey.message.template.templateId
      }
      addTaskFn(params)
    } else {
      // localStorage.removeItem('storageKey')
    }
  }, [])

  // 移除从编辑页未关闭的modal蒙层
  useEffect(() => {
    const modalRef = document.getElementsByClassName('ant-modal-root')
    if (modalRef) {
      Array.from(modalRef).map((item) => {
        item?.remove()
      })
    }
  }, [])

  const addTaskFn = async (params) => {
    const res = await addTask(params)
    if (res && res.code === 200) {
      message.success('编辑成功')
      setIsIframeModalVisible(false)
      getDetail()
      localStorage.removeItem('storageKey')
    } else {
      message.error(res.message)
      localStorage.removeItem('storageKey')
    }
  }

  useEffect(() => {
    getDetail()
  }, [searchId])

  const actions = []

  return (
    <>
      <Breadcrumb separator=">" style={{ marginBottom: 10 }}>
        <Breadcrumb.Item><Link to={`/templatesList/list`}>模板中心</Link></Breadcrumb.Item>
        <Breadcrumb.Item><Link to={`/atmosphere/list`}>合图任务记录</Link></Breadcrumb.Item>
        <Breadcrumb.Item>合图任务详情</Breadcrumb.Item>
      </Breadcrumb>
      <section className="sharkr-section">
        <div className="sharkr-section-header">
          <span className="sharkr-section-header-title">合图任务详情</span>
        </div>
        <div className="sharkr-section-content">
          <Row>
            <Col span={8}>
              <SharkRForm
                className=" searchForm"
                ref={searchRef}
                schema={searchSchema}
                {...{
                  labelCol: { span: 6 },
                  wrapperCol: { span: 18 }
                }}
              />
            </Col>
          </Row>
          <Row>
            <Col span={8} xl={8} xxl={6}>
              <Row>
                <Col offset={6}>
                  <Space>
                    <Button className="tool" type="primary" onClick={onSearch}>
                      搜索
                    </Button>
                    <Button className="tool" onClick={reset}>
                      重置
                    </Button>
                  </Space>
                </Col>
              </Row>
            </Col>
          </Row>
          <div className="sharkr-section-global-header tableBottoms">
            <div className="sharkr-tools">
              <Space style={{ display: 'flex', justifyContent: 'right', marginBottom: '5px' }}>
                {canEdit && <Button
                  className="tool"
                  disabled={data?.status === 2 || data?.status === 4 ? false : true}
                  type="primary"
                  onClick={recombining}>
                  重新合图
                </Button>
                }
                <Button
                  className="tool"
                  disabled={data?.status === 2 ? false : true}
                  type="primary"
                  onClick={batchDownload}>
                  批量下载
                </Button>
              </Space>
              {canEdit && <div>(二次编辑修改图片之后，需要重新合图后才能生效)</div>}
            </div>
          </div>
          <Row className="margin-b-base img-card" gutter={[20, 20]}>
            {data &&
              data?.combinedPicVOS.map((m: any, idx: number) => {
                let actions = [
                  <Button key="setting" onClick={() => handlePreview(m?.combinedPicUrl)}>
                    预览
                  </Button>
                ]

                if (canEdit) {
                  actions = actions.concat(
                    [
                      <Button
                        disabled={data.status === 2 || data.status === 4 ? false : true}
                        key="ellipsis"
                        onClick={() => imgEdit(m)}>
                        编辑
                      </Button>,
                      <Button key="delPic" onClick={() => delImg(m)}>
                        删除
                      </Button>
                    ]
                  )
                }

                return (<Col key={`${m}${idx}`} span={6}>
                  <Card
                    actions={actions}
                    cover={
                      <img
                        alt="example"
                        src={
                          m.combinedPicUrl
                            ? m.combinedPicUrl
                            : 'https://yanxuan.nosdn.127.net/static-union/16589077933d0a4a.png'
                        }
                      />
                    }
                    title={m?.picName}
                  />
                </Col>)
              })}
          </Row>
          <SharkRPreview activeIndex={-4} url={picUrl} visible={visible} onCancel={handleCancel} />
        </div>
      </section>
    </>
  )
}
