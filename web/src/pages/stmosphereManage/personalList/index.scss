.personal-template-card {
  width: 100%;
  .ant-col-5 {
    display: block;
    flex: 0 0 20% !important;
    max-width: 20% !important;
  }
  //  img{
  //     aspect-ratio :1
  //    }
  .ant-card-body {
    padding: 10px;
  }

  .ant-card-cover {
    padding: 1px;
  }

  .ant-empty {
    width: 100%;
  }
}
.sharkr-form-react.ant-form-horizontal .ant-form-item {
  margin-bottom: 0 !important;
  min-height: 56px !important;
}
.m-img-wrap {
  // margin: auto;
  background: rgb(246,247,249);
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  border-radius: 8px;

  .img {
    max-width: 100%;
    height: 170px;
    border-radius: 6px;
    z-index: 10;
    // background: white;
  }
}
.m-card-name {
  margin-top: 10px;
  white-space: normal;
  height: 44px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.header-text {
  font-weight: 700;
  color: rgba(0, 0, 0, 0.85);
  font-size: 16px;
}

.personal-design-tabs {
  padding: 16px 24px;
}
.smallImg {
  width: 4.6rem;
  height: 1.4rem;
  position: absolute;
  bottom: 24%;
  left: 10%;
  background-image: url(../../../assets/mbj.png);
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  border-radius: 21%;
  z-index: 20;
  .smallSpan {
    font-size: 12px;
    color: white;
    margin-left: 35%;
    vertical-align: text-top;
  }
}
