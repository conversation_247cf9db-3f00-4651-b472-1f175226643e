import React, { useEffect, useState } from 'react'
import { DatePicker, Input, InputNumber, Popconfirm, Row, Select, TreeSelect, message } from 'antd'
import { useHistory } from 'react-router-dom'
import "./index.scss";
import 'moment/locale/zh-cn';
import locale from 'antd/es/date-picker/locale/zh_CN';
import { CloseCircleFilled, DownOutlined } from '@ant-design/icons';
import { getAllShopList, getShopList, getTagList } from '../../../../../services/personalServices';

interface FilterHeadProps {
    searchParam?: any;
    onChangeSearchParam?: any;
}

const RangePicker: any = DatePicker.RangePicker;

export const FilterHead: React.FC<FilterHeadProps> = (props) => {
    const { searchParam, onChangeSearchParam } = props;

    // const [activeType, setActiveType] = useState("all");

    const [filterType, setFilterType] = useState("");

    const [filterObj, setFilterObj] = useState({
        templateName: "",
        templateId: 0,
        shopId: "",
        tagList: [] as any[],
        addTime: [] as any[]
    });

    const [filterObjOpen, setFilterObjOpen] = useState({
        templateNameOpen: false,
        templateIdOpen: false,
        shopOpen: false,
        lableOpen: false,
        addTimeOpen: false
    });
    const [shopList, setShopList] = useState<any[]>([]);
    const [tagList, setTagList] = useState<any[]>([]);

    useEffect(() => {
        setFilterObj(searchParam);
    }, [searchParam])

    useEffect(() => {
        getAllShopList().then(res => {
            if (res?.code === 200) {
                const data = res?.data?.map((item) => {
                    item.label = item.channelName;
                    item.value = item.channelId;

                    return item;
                }) ?? [];
                setShopList(data)
            } else {
                message.error('获取店铺列表失败')
                return [];
            }
        });
        getTagList().then((res => {
            let result = [];
            if (res?.code === 200) {
                result = res?.data?.map((item) => {
                    item.fakeTitle = item.title
                    item?.children?.forEach(child => {
                        child.key = `${item.key}-${child.key}`;
                        child.fakeTitle = `${item.title} > ${child.title}`;
                    })
                    console.log("item", item)
                    return item
                }) ?? [];
               
            } else {
                message.error('获取标签列表失败');
            }

            setTagList(result)
        }));
    }, [])

    // const templateTypeOptions = [
    //     {
    //         name: '全部',
    //         value: 'all'
    //     },
    //     {
    //         name: '活动主图模板',
    //         value: '1'
    //     },
    //     {
    //         name: '活动模板',
    //         value: '2'
    //     }
    // ];

    const handleChangeTemplateTags = (arr) => {
        setFilterObj({
            ...filterObj,
            tagList: arr
        })
    };

    const tProps = {
        treeData: tagList,
        value: filterObj.tagList || undefined,
        onChange: handleChangeTemplateTags,
        treeCheckable: true,
        showCheckedStrategy: TreeSelect.SHOW_CHILD,
        placeholder: '请选择标签',
        className: 'filter-type-tree-select',
        getPopupContainer: (triggerNode: any) => triggerNode.parentNode,
        treeDefaultExpandAll: true,
        labelInValue: true,
        treeNodeLabelProp: "fakeTitle",
    };

    const filterTypeOptions = [
        {
            name: '模板名称',
            value: 'templateName',
            content: <Input placeholder="请输入模板名称" value={filterObj.templateName} allowClear onChange={(e) => {
                setFilterObj({
                    ...filterObj,
                    templateName: e.target.value
                })
            }} />,
        },
        {
            name: '模板ID',
            value: 'templateId',
            content:
                <InputNumber className="filter-type-templateId" min={1} placeholder="请输入模板ID" value={filterObj.templateId} onChange={(value) => {
                    setFilterObj({
                        ...filterObj,
                        templateId: value || 0
                    })
                }} />
        },
        {
            name: '店铺',
            value: 'shopId',
            content: <Select
                className='filter-type-shop-select'
                placeholder="请选择店铺名称"
                style={{ width: 150 }}
                value={filterObj.shopId || undefined}
                allowClear
                onChange={(value) => {
                    setFilterObj({
                        ...filterObj,
                        shopId: value
                    })
                }}
                showSearch
                optionFilterProp="label"
                labelInValue
                getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
                options={shopList}

            />
        },
        {
            name: '标签',
            value: 'tagList',
            content: <TreeSelect {...tProps} />
        },
        {
            name: '添加时间',
            value: 'addTime',
            content: (
                <React.Fragment>
                    <RangePicker
                        key="ilter-type-range-picker"
                        locale={locale}
                        className="filter-type-range-picker"
                        allowEmpty
                        placement="rightBottom"
                        value={filterObj.addTime}
                        getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
                        onChange={(dates, dateStrings) => {
                            setFilterObj({
                                ...filterObj,
                                addTime: [...dates]
                            })

                        }}
                    />
                </React.Fragment>
            )
        }
    ];

    const handleClickFilterType = (currKey) => {
        const activeFilterType = filterType !== currKey ? currKey : "";
        setFilterType(activeFilterType)
        const key = `${currKey}Open`;
        const currOpenState = filterObjOpen[key];
        setFilterObjOpen({
            templateNameOpen: false,
            templateIdOpen: false,
            shopOpen: false,
            lableOpen: false,
            addTimeOpen: false,
            [key]: !currOpenState
        })
    };

    const handleCancelFilterType = (e, type) => {

        e.stopPropagation();
        setFilterType('');

        setFilterObj({
            ...filterObj,
            [type]: ["tagList", "addTime"].includes(type) ? [] : ""
        })

        onChangeSearchParam({
            ...searchParam,
            [type]: ["tagList", "addTime"].includes(type) ? [] : ""
        })
    };

    return (
        <div className="filter-option-wrap">
            <div className='template-type-wrap'>
                {/* {
                    templateTypeOptions.map((item) => {
                        const cls = item.value === activeType ? 'template-type-item-active template-type-item' : 'template-type-item';
                        return <div className={cls} key={item.value} onClick={() => setActiveType(item.value)}>{item.name}</div>
                    })
                } */}
            </div>
            <div className='filter-type-wrap'>
                {
                    filterTypeOptions.map((item, index) => {
                        let cls = item.value === filterType ? 'filter-type-item-active filter-type-item' : 'filter-type-item';
                        const hasValue = item.value === "addTime" ? searchParam[item.value]?.length === 2 : searchParam[item.value]?.toString()
                        cls = hasValue ? `${cls} filter-type-item-has-value` : cls;

                        let valueStr = item.name;

                        if (hasValue) {
                            switch (item.value) {
                                case "shopId":
                                    valueStr = `${valueStr}: ${searchParam[item.value]?.label ?? ""}`;
                                    break;
                                case "tagList":
                                    valueStr = `${valueStr}: ${searchParam[item.value]?.map(item => item.label)?.join(" , ") ?? ""}`;
                                    // valueStr = `${valueStr}: ${searchParam[item.value]?.join(" , ") ?? ""}`;
                                    break;
                                case "addTime":
                                    const timeStr = searchParam[item.value]?.map(item => item?.format? item?.format("YYYYMMDD") : item)?.join(" - ");
                                    valueStr = `${valueStr}: ${timeStr}`;
                                    break;

                                default: valueStr = `${valueStr}: ${searchParam[item.value]}`;
                                    break;
                            }
                        }

                        return (
                            <Popconfirm
                                key={item.value}
                                open={filterObjOpen[`${item.value}Open`]}
                                placement="bottomRight"
                                title={item.content}
                                icon={null}
                                showCancel={false}

                                onOpenChange={(open) => {
                                    setFilterObjOpen({
                                        ...filterObjOpen,
                                        [`${item.value}Open`]: open
                                    })


                                    !open && setFilterType("")
                                }}
                                onConfirm={() => {
                                    onChangeSearchParam({
                                        ...searchParam,
                                        [item.value]: filterObj[item.value]
                                    })

                                    setFilterObjOpen({
                                        ...filterObjOpen,
                                        [`${item.value}Open`]: false
                                    })
                                }}
                                getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
                            >
                                <div className={cls} onClick={(target) => { handleClickFilterType(item.value) }}
                                >
                                    <span title={valueStr}>{hasValue ? valueStr : item.name}</span>
                                    <DownOutlined style={{ fontSize: '12px' }} />
                                    <CloseCircleFilled className="filter-type-item-close" style={{ color: "rgba(0, 0, 0, 0.4)" }} onClick={(e) => { handleCancelFilterType(e, item.value) }} />
                                </div>
                            </Popconfirm>

                        )
                    })
                }
            </div>
        </div>
    )
}
