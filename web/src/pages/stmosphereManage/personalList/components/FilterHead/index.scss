
.filter-option-wrap {
  margin: 8px 0 24px 0;
  display: flex;
  justify-content: space-between;

  .template-type-wrap {
    display: flex;

    .template-type-item {
      margin-right: 24px;
      cursor: pointer;
      font-size: 14px;
    }

    .template-type-item-active {
      color: #333;
      font-weight: 600;
    }
  }

  .filter-type-wrap {
    display: flex;

    .filter-type-item {
      margin-right: 16px;
      cursor: pointer;
      font-size: 14px;
      padding: 4px 10px;
      border-radius: 8px;
      position: relative;
      box-sizing: border-box;
      display: flex;
      align-items: center;

      span {
        max-width: 200px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }

      > :not(:last-child) {
        margin-right: 8px;
      }

      &:hover {
        background: rgba(0, 0, 0, 0.04);
      }
    }

    .filter-type-item-close {
      display: none;
      position: absolute;
      right: 0;
      top: 0;
      transform: translate(40%, -40%);
      z-index: 1024;
      font-size: 14px;
    }

    .filter-type-item-active {
      background: rgba(0, 0, 0, 0.06) !important;
      position: relative;
    }

    .filter-type-item-has-value {
      background: rgba(0, 0, 0, 0.1) !important;
      position: relative;

      &:hover {
        .filter-type-item-close {
          display: block;
        }
      }
    }
  }
}


.filter-type-templateId {
    min-width: 200px;
}

.filter-type-shop-select {
    min-width: 200px;
}

.filter-type-popover-wrap {
  //   padding-top: 4px;
  //   border: 1px solid red;
  //   position: relative;
}

.filter-type-range-picker {
  width: 250px;
  //   visibility: hidden;
  //   height: calc(50% - 4px);
  //   padding: 0;
  //   width: 0;
  //   right: 0;
  //   position: absolute;

  .ant-picker-dropdown {
    // right: 0;
  }
}

.filter-type-tree-select {
  min-width: 250px;
  margin: 0 24px;
}
