/*
 * @Description: 团队空间
 * @Author: hzweixin <EMAIL>
 * @Date: 2023-05-22 17:03:08
 */
import React, { useEffect, useRef, useState, useMemo } from 'react'
import { Button, Modal, Select, message } from 'antd'
import { Event } from '@sharkr/utils'
import { SharkRForm } from '@sharkr/form'
import { PlusOutlined } from '@ant-design/icons'
import { UmcAuth } from '@eagler/authorizion'
import { addTeamMember, getTeam } from '../../../../services/teamServices'
import { myTemplateTransferToTeamSpace } from '../../../../services/personalServices'
/**
 * @description: 
 * @return {*}
 */
interface Iprops {
  /**
   * @description: 团队空间
   * @return {*}
   */
  setTeam?: (e: number) => void
  teamSpacemoDalFlag: boolean,
  teamSpacemoDalForm: { templateId: null },
  teamSpaceChange: Function
}

export const TeamSpace: React.FC<Iprops> = (props) => {
  const localTeam = JSON.parse(localStorage.getItem('select') || 'null')
  const [options, setOptions] = useState<any>([])
  const [value, setValue] = useState<any>(null)
  const { setTeam, teamSpacemoDalFlag, teamSpacemoDalForm } = props
  const [showModal, setShowModal] = useState<boolean>(false)
  const templateSchema: any[] = [
    // {
    //   key: 'email',
    //   type: 'Input',
    //   label: '用户邮箱',
    //   ui: { required: true },
    //   rules: [
    //     {
    //       required: true,
    //       message: '请填写用户邮箱'
    //     }
    //   ]
    // },
    {
      key: 'teamId',
      type: 'Select',
      label: '关联团队空间',
      ui: { required: true },
      rules: [
        {
          required: true,
          message: '请选择关联团队空间'
        }
      ],
      options: [
        {
          value: 1,
          label: '公共空间',
          selected: true
        },
        {
          value: 2,
          label: '淘系'
        },
        {
          value: 3,
          label: '京东及综合'
        }
      ]
    }
  ]
  const formRef = useRef<any>(null)

  /**
   * @description: 选择团队空间
   * @param {any} e 选项
   * @return {*}
   */
  // const handleSelect = (e: any) => {
  //   setValue(e)
  //   setTeam(e)
  //   localStorage.setItem('select', e)
  //   Event.dispatch('select', e)
  // }

  /**
   * @description: 添加团队成员
   * @return {*}
   */
  const handleOk = async () => {
    const data = await formRef.current.submit()
    if (!data) return
    const { teamId } = data
    const res = await myTemplateTransferToTeamSpace({ templateId: teamSpacemoDalForm?.templateId, teamId })
    if (res?.code === 200) {
      // message.success('添加成功')
      const obj = templateSchema[0]?.options?.filter((item) => {
        return item.value == teamId
      })
      message.success(`已转入${obj[0]?.label}`)
    } else {
      message.error(res?.msg)
    }
    setShowModal(false)
    props.teamSpaceChange(false)
    // window.location.reload()
  }

  /**
   * @description: 取消添加团队成员
   * @return {*}
   */
  const handelCancel = () => {
    setShowModal(false)
    props.teamSpaceChange(false)
  }

  /**
   * @description: 获取团队空间列表
   * @return {*}
   */
  // const getTeamList = async () => {
  //   const res = await getTeam()
  //   if (res?.code === 200) {
  //     // eslint-disable-next-line array-callback-return
  //     res?.data.map(item => {
  //       item.value = item?.id
  //       item.label = item?.name
  //     })
  //     if (localTeam && res?.data?.find(item => item.id === localTeam)) {
  //       setValue(localTeam)
  //       setTeam(localTeam)
  //     } else {
  //       setValue(res.data[0]?.value)
  //       setTeam(res.data[0]?.value)
  //     }
  //     setOptions(res?.data)
  //   }
  // }

  // useEffect(() => {
  //   getTeamList()
  // }, [])

  useEffect(() => {
    if (teamSpacemoDalFlag) {
      setShowModal(true)
    }
  }, [teamSpacemoDalFlag])

  return (
    <>
      {/* {options && (
        <div style={{ marginLeft: '20px' }}>
          <span>团队空间：</span>
          <Select
            bordered={false}
            options={options}
            style={{ width: 120 }}
            value={value}
            onSelect={e => {
              handleSelect(e)
            }}
          />
          <UmcAuth condition="1636131000026">
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setShowModal(true)}>
              添加用户
            </Button>
          </UmcAuth>
        </div>
      )} */}
      <Modal
        title="转到团队空间"
        destroyOnClose
        visible={showModal}
        onOk={handleOk}
        onCancel={handelCancel}>
        <SharkRForm
          ref={formRef}
          schema={templateSchema}></SharkRForm>
      </Modal>
    </>
  )
}
