/* eslint-disable no-useless-concat */
/* eslint-disable prettier/prettier */
/* eslint-disable max-lines-per-function */
/* eslint-disable array-callback-return */
import React, { useMemo, useEffect, useRef, useState, useImperativeHandle, forwardRef } from 'react'
import { Button, message, Modal, Upload } from 'antd'
import Postmate from 'postmate'
import { useHistory } from 'react-router-dom'
import { SharkRForm } from '@sharkr/form'
import { SharkRLoading, SharkRPreview } from '@sharkr/components'
import { BACK_IMAGE_URL, imgTypeOptions, taskTypeOptions } from '../../../../consts/schemaForm'
import { batchAddTask, getTemplateDetail } from '../../../../services/stmosphereService'
import { extendTemplateData, loadPicSize } from '../../../../utils'
import { PESONAL_TEMPLATE_KEY } from '../../../../utils/constant'

const AddDrawingTask: React.FC<any> = (props: any, ref) => {
	const { teamId, savePageInfo, template } = props
	const editUrl =
		`${`${location.protocol}` + '//'}${window.location.host}/distribution-operation/oly-miriam-editor#/micro/save`
	const history = useHistory()
	const host = `${location.protocol}//${window.location.host}`
	const [taskModal, setTaskModal] = useState(false)
	const [activityName, setActivityName] = useState(null)
	const [record, setRecord] = useState<any>(null)
	const [isIframeModalVisible, setIsIframeModalVisible] = useState(false)
	const formRef = useRef<any>(null)
	const container = useRef<any>()
	const iframe = useRef<Postmate.ParentAPI>()
	const [iframeShow, setIframeShow] = useState<any>(false)
	const [isSpin, setIsSpin] = useState(true)

	const disabled = !!template;
	const options = disabled ? [{
		name: template.templateName,
		value: template.templateId,
	}] : {
		action: '/doc/sharkr/form/xhr/getOptions.json?type=${templateId.value}',
		method: 'post',
		path: 'data',
		nameProperty: 'name',
		valueProperty: 'value',
		watch: ['templateId']
	};

	const schema = useMemo(() => {
		const data = [
			{
				key: 'templateId',
				type: 'Select',
				label: '模板',
				props: {
					showSearch: true,
					allowClear: true,
					placeholder: '请输入模板ID/名称',
					disabled
				},
				value: disabled ? template.templateId : undefined,
				options,
				ui: {
					required: true,
				},
				rules: [
					{
						required: true,
						message: '请输入模板ID/名称',
					},
				],
			},
			{
				key: 'activityName',
				type: 'Input',
				label: '活动名称',
				props: {
					placeholder: '请输入活动名称',
				},
				ui: {
					required: true,
				},
				rules: [
					{
						required: true,
						message: '请输入活动名称',
					},
				],

			},
			{
				key: 'taskType',
				type: 'Radio',
				label: '任务类型',
				ui: {
					required: true,
				},
				options: taskTypeOptions,
			},
			{
				key: 'outputFormat',
				type: 'Radio',
				label: '图片导出类型',
				options: imgTypeOptions,
				ui: {
					required: true,
					// message: '请选择图片导出类型',
				},
				rules: [
					{
						required: true,
						message: '请选择图片导出类型',
					},
				],
			},
			{
				key: 'picName',
				type: 'Input',
				label: '图片名称',
				ui: {
					required: true,
				},
				rules: [
					{
						required: true,
						message: '请输入图片名称',
					},
				],
				listeners: [
					{
						watch: ['taskType'],
						condition: true,
						set: (valid, field, form) => {
							const type = form.getValue('taskType')
							const picName = form.getValue('picName')
							field.set({
								status: type === 1 ? 'edit' : 'hidden',
								value: type === 1 ? picName : null,
							})
						},
					},
				],
				props: {
					placeholder: '请输入图片名称',
				},

			},
		]
		return data
	}, [template])

	useEffect(() => {
		iframeShow && init()
	}, [iframeShow])
	useImperativeHandle(ref, () => {
		return {
			showTaskModal,
		}
	})
	const getTemDetail = async () => {
		if (props.template) {
			const res = await getTemplateDetail({ templateId: props.template.templateId })
			if (res.code === 200 && res.data) {
				return res.data
			} else {
				message.error('获取详情失败')
			}
		}
	}
	// 初始化iframe
	const init = async () => {
		const values = await getTemDetail()
		const extendData = await extendTemplateData(props.template)
		if (values) {
			setIsSpin(false)
			iframe.current = await new Postmate({
				container: container.current, // iframe的外置容器ref
				url: `${host}/oly-miriam-editor#/iframe/preview`, // iframe链接地址
				name: 'oly-editor', // iframe name attribuite
				classListArray: ['g-iframe'], // 自定义iframe classNameList
			})
			const params = { template: { ...props.template, ...extendData }, schema: { ...values } }
			iframe.current?.call('sendDataToEditor', params)
		}
	}
	// 展示新建合图弹窗
	const showTaskModal = () => {
		setTaskModal(true)
	}
	// 下载excel模板
	const downLoadExcel = () => {
		if (props.template) {
			window.open(props.template.downloadUrl)
		}
	}
	// 转化为 formdata格式
	const formdataify = (params: object) => {
		const formData = new FormData()
		Object.keys(params).forEach((key) => {
			if (typeof params[key] === 'string') {
				formData.append(key, params[key])
			} else {
				formData.append(key, params[key])
			}
		})
		return formData
	}
	// 展示模板错误详情
	const error = (value: string) => {
		Modal.error({
			title: '错误提示',
			content: value,
		})
	}
	const batchAdd = async (e: any) => {
		const values = activityName
		if (values) {
			const formData = new FormData()
			formData.append('file', e.file)
			const params = { templateId: props.template.templateId, file: e.file, activityName: values, outputFormat: record?.outputFormat, teamId: teamId }
			const res = await batchAddTask(formdataify(params))
			if (res.code === 200) {
				message.success('操作成功')
				setIsIframeModalVisible(false)
				setIframeShow(false)
				history.push('/atmosphere/list')
			} else {
				error(res?.message)
			}
		}
	}
	const handleOk = () => {
		formRef &&
			formRef.current.submit().then(async (data) => {
				const { taskType } = data
				if (taskType === 2) {
					setActivityName((formRef && formRef.current && formRef.current.getValue('activityName')) || '活动名称')
					setRecord(data)
					setIframeShow(true)
					setTaskModal(false)
					setIsIframeModalVisible(true)
				} else {
					const values = await getTemDetail()
					const extendData = await extendTemplateData(props.template)

					const microStorageMessage = {
						message: {
							template: { ...props.template, ...extendData },
							schema: values,
						},
						redirectUrl: window.location.href,
						status: 'pending', // 状态 pending:等待编辑结果 done：已编辑完成 draft: 草稿
						type: 'pic',
						picName: data.picName,
						activityName: data.activityName,
						outputFormat: data?.outputFormat,
						teamId
					}
					localStorage.setItem(PESONAL_TEMPLATE_KEY, JSON.stringify(microStorageMessage))
					savePageInfo?.();
					setTimeout(() => {
						window.location.href = editUrl + `?storageKey=${PESONAL_TEMPLATE_KEY}`
					}, 100)
				}
			})
	}
	const handleCancel = () => {
		setIsIframeModalVisible(false)
		setIframeShow(false)
	}
	return (
		<>
			<Modal
				destroyOnClose
				className="taskModal"
				key="taskModal"
				title="创建合图任务"
				visible={taskModal}
				width={600}
				onCancel={() => setTaskModal(false)}
				onOk={() => handleOk()}>
				<SharkRForm ref={formRef} schema={schema} />
			</Modal>
			<Modal
				destroyOnClose
				className="excelModal"
				footer={[
					<Button key="downLoadExcel" type="link" onClick={downLoadExcel}>
						下载模板
					</Button>,
					<Upload accept=".xlsx" customRequest={batchAdd} key="combin" listType="picture" maxCount={1}>
						<Button style={{ marginLeft: '8px' }} type="primary">
							导入
						</Button>
					</Upload>,
				]}
				key="iframeModal"
				title="批量合图"
				visible={isIframeModalVisible}
				width={1280}
				onCancel={handleCancel}>
				<div>
					{isSpin && <SharkRLoading />}
					<div className="iframeBox" ref={container} />
				</div>
			</Modal>
		</>
	)
}
export default forwardRef(AddDrawingTask)
