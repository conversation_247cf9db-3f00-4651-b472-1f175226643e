import React, { useEffect, useRef, useState, useMemo } from 'react'
import { Col, Row, Select } from 'antd'
import { SharkRForm } from '@sharkr/form'
import { getTemClass } from '../../../../services/templateClassService'
import { getQuery } from '../../../../utils'
import { useHistory } from 'react-router-dom'

interface Iprops {
  setTemClass?: any
  searchParam?: any
  data?: any,
  classFormRef?: any
  teamId?: any
}

export const TemplateClass: React.FC<Iprops> = (props: Iprops) => {
  // const classFormRef = useRef<any>(null)
  // const [data, setData] = useState<any>([])
  const history = useHistory()
  const { setTemClass, data, classFormRef } = props

  const changeSenceData = (value: any[]) => {
    if (value.length <= 0) return []
    const dataValues = [{ name: '全部', value: 0 }]
    value?.forEach((item: any) => {
      item.value = item?.id
      dataValues.push({ ...item })
    })
    return dataValues
  }

  const changePurData = (data: any[], senceId: number) => {
    let dataValues: any = []
    const senceValue = data.find((item) => item.id === senceId)
    if (senceValue?.subNode && senceValue?.subNode?.length) {
      dataValues = [...changeSenceData(senceValue?.subNode)]
    }
    return dataValues
  }

  const schema = useMemo(() => {
    if (data.length <= 0) return null
    const value: any = [
      {
        key: 'scene',
        type: 'Radio',
        label: '场景',
        ui: { labelAlign: 'left' },
        value: 0,
        options: data && changeSenceData(data)
      },
      {
        key: 'purpose',
        type: 'Radio',
        label: '用途',
        ui: { labelAlign: 'left' },
        status: 'hidden',
        listeners: [
          {
            watch: ['scene'],
            set: (valid, field, form) => {
              const scene = form.getValue('scene')
              const purOptions = changePurData(data, scene)
              field.set({
                status: purOptions.length <= 0 ? 'hidden' : 'edit',
                value: purOptions.length <= 0 || !scene ? null : 0,
                options: purOptions
              });
            }
          }
        ]
      },
      {
        key: 'topic',
        type: 'Radio',
        label: '话题',
        ui: { labelAlign: 'left' },
        status: 'hidden',
        listeners: [
          {
            watch: ['purpose', 'scene'],
            condition: (field, form) =>
              form.getValue('purpose') && form.getValue('scene'),
            set: (valid, field, form) => {
              const purpose = form.getValue('purpose')
              const scene = form.getValue('scene')
              const purposeOptions = changePurData(data, scene)
              const topicOptions = changePurData(purposeOptions, purpose)
              field.set({
                status: valid && topicOptions.length > 0 ? 'edit' : 'hidden',
                value: topicOptions.length <= 0 || !purpose ? null : 0,
                options: JSON.parse(JSON.stringify(topicOptions))
              });
            }
          }
        ]
      },
    ]
    return value
  }, [data])

  // const getTemplateClass = async () => {
  //   const res = await getTemClass({ type: 1 })
  //   if (res?.code === 200) {
  //     setData(res?.data)
  //   }
  // }


  const onValuesChange = (changedValues, allValues) => {
    setTemClass({ ...allValues })
  }

  useEffect(() => {
    // getTemplateClass()
    // console.log('🐻', JSON.parse(localStorage.getItem('templateListPageInfo')))
    // const templateListPageInfo = localStorage.getItem('templateListPageInfo')
    // if (templateListPageInfo !== null) {
    //   const pageInfo = JSON.parse(templateListPageInfo)
    //   classFormRef.current.setValue({ scene: pageInfo.scene, purpose: pageInfo.purpose, topic: pageInfo.topic })
    // }
    const args = getQuery(history.location.search);
    if (args.scene) {
      // console.log('🐱', args)
      classFormRef.current.setValue({
        scene: args.scene === 'null' ? null : Number(args.scene),
        purpose: args.purpose === 'null' ? null : Number(args.purpose),
        topic: args.topic === 'null' ? null : Number(args.topic)
      })
    }
    // classFormRef.current.setValue({ scene: 1 })
  }, [])
  return (
    <>
      <Row>
        <Col span={24}>
          <SharkRForm
            className=" searchForm"
            ref={classFormRef}
            schema={schema}
            onValuesChange={onValuesChange}
            {...{
              labelCol: { span: 1 },
              wrapperCol: { span: 20 }
            }}
          />
        </Col>
      </Row>
    </>
  )
}
