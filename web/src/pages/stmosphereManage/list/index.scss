.ecsHeader{
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 10px;
  .task{
   margin-right: 20px;
 }
}
.pagination-area {
  margin-top: 20px;
  text-align: right;
}
.Operation{
  display: flex;
  flex-wrap: wrap;
  align-items: center;

}


.runStatus{
  margin-top: 20px;
  display: flex;
  justify-content:space-between;
  flex-wrap: wrap;

.box{
  width: 49%;
  margin-bottom: 10px;
  .header{
   display: flex;
   justify-content: space-between;
}
  .showStatus{
  font-size: 30px;
  font-weight: 500;
  }
}
}
.radioButton{
  margin-right: 20px;
}
.detailLable{
  text-align: right;
}
.ecsInfo{
 color:rgba(0, 0, 0, 0.65)
}
.sharkr-form-react.ant-form-horizontal .ant-form-item{
        margin-bottom: 0 !important;
        min-height: 56px !important;
}
.radioStyle{
  margin-left: -24px;
  margin-top: 8px;
}
#container{
  width: 100%;
  height: 400px;
}
.excelModal{
.ant-upload-list-item, .ant-upload-list{
 display: none !important;
}
}
.iframeBox{
  height: 600px;
  iframe{
   width: 100%;
   height: 600px;
   border: 1px solid #ddd
}
}
.tableBottoms {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 10px;
    .bottom{
      margin-right: 10px;
    }
  }
.header-flex{
  display: flex;
  align-items: center;
}
