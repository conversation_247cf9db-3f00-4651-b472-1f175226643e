/* eslint-disable max-lines */
/* eslint-disable max-lines-per-function */
/* eslint-disable array-callback-return */
import React, { useMemo, useEffect, useRef, useState } from 'react'
import { Button, Row, Col, Table, Modal, Pagination, Input, Upload, message, Space, Breadcrumb, Form } from 'antd'
import Postmate from 'postmate'
import './index.scss'
import { PlainObject } from '@shark/core'
import { registerComponent, SharkRForm } from '@sharkr/form'
import { Link } from 'react-router-dom'
import moment from 'moment'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import { SharkRLoading, getUserInfo } from '@sharkr/components'
import { getImgList, getTemplateList, getTemplateDetail, batchAddTask, addTask, deleteTask, sharedEditingPermissionUser } from '../../../services/stmosphereService'
import RadioConfig from '../components/radioConfig'
import { choiceStatus, giftOptions, serviceTypeOptions, sizeOptions, taskTypeOptions, templateFrameOptions } from '../../../consts/schemaForm'
import AddDrawingTask from '../templateList/components/addDrawingTask'
import { IcacUser, MultiUserSelect } from '@eagler/umc-select-web'
import CreateCompositeTask from '../templateList/components/CreateCompositeTask'


export interface IPaginationProps {
  page: number
  pageSize: number
  total: number
  totalPage: number
}
export interface IAntDPaginationProps {
  current: number
  pageSize: number
  total?: number
}

interface IECSQueryProps {
  pageNo: number
  pageSize: number
  keyword?: string
}
registerComponent('RadioConfig', RadioConfig);

export const StmosphereList: React.FC<{}> = (props: {}) => {
  // const { Search } = Input
  // const container = useRef<any>()
  // const shareModalRef = useRef<any>()
  // const host = `${location.protocol}//` + window.location.host
  // const iframe = useRef<Postmate.ParentAPI>()
  const [form] = Form.useForm();

  const [searchParam, setSearchParam] = useState<IECSQueryProps>({
    keyword: undefined,
    pageNo: 1,
    pageSize: 10
  })
  const [dateSource, setDateSource] = useState<any[]>([])
  const [pagination, setPagination] = useState<IAntDPaginationProps | {}>({})
  const formRef = useRef<any>(null)
  const searchRef = useRef<any>(null)
  const taskModalRef = useRef<any>()

  const [shareVisible, setShareVisible] = useState(false)
  const [currTask, setCurrTask] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const userEmail = getUserInfo()?.email;

  // 移除从编辑页未关闭的modal蒙层
  // useEffect(() => {
  //   console.log("22222222222222222222")
  //   const modalRef = document.getElementsByClassName('ant-modal-root')
  //   if (modalRef) {
  //     Array.from(modalRef).map((item) => {
  //       console.log("item", item)
  //       item?.remove()
  //     })
  //   }
  // }, [])
  useEffect(() => {
    const storageKey = JSON.parse(localStorage.getItem('storageKey') || "null")
    if (storageKey && storageKey.status === 'done') {
      if (storageKey.type === 'pic') {
        coverImg(storageKey)
      }
    }
  }, [])
  // 更新图片
  const coverImg = async (storageKey) => {
    console.info(storageKey, 'storageKey')
    const params = {
      activityName: storageKey.activityName,
      schemaDTO: storageKey.message.schema,
      outputFormat: storageKey?.outputFormat,
      // picId: template.picId,
      picName: storageKey.picName,
      taskId: 0,
      templateId: storageKey.message.template.templateId
    }
    const res = await addTask(params)
    if (res && res.code === 200) {
      localStorage.removeItem('storageKey')
      message.success('配置成功')
      getList()
    } else {
      localStorage.removeItem('storageKey')
      message.error(res.message)
    }
  }
  // 下载模板
  const download = (url: string) => {
    url && window.open(url)
  }
  // 搜索
  const onSearch = () => {
    searchRef && searchRef.current && searchRef.current.submit().then(data => {
      const params = {
        ...data,
        pageNo: 1,
        pageSize: 10
      }
      setSearchParam(params)
    })
  }
  // 删除合图任务
  const handleDeleteTask = async (taskId: number) => {
    const res = await deleteTask({ taskId })
    if (res && res.code === 200) {
      message.success("删除成功")
      getList()
    } else {
      message.error(res.message)
    }
  }

  const confirm = (taskId: number) => {
    Modal.confirm({
      title: '删除合图任务',
      icon: <ExclamationCircleOutlined translate={undefined} />,
      content: '是否确认删除该条合图任务',
      okText: '确认',
      cancelText: '取消',
      onOk: () => handleDeleteTask(taskId)
    });
  };

  const share = (task: any) => {
    setCurrTask(task);
    setShareVisible(true);
  };

  const columns: any = [
    {
      title: '活动名称',
      dataIndex: 'activityName',
      key: 'activityName'
    },
    {
      title: '时间',
      dataIndex: 'createTime',
      key: 'createTime',
      render: (createTime: any) => {
        return <span>{moment(createTime).format('YYYY-MM-DD HH:mm:ss')}</span>
      }
    },
    {
      title: '模板',
      dataIndex: 'templateName',
      key: 'templateName'
    },
    {
      title: '操作者',
      dataIndex: 'updateUser',
      key: 'updateUser'
    },
    {
      title: '当期状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: number, record: any) => {
        return <span>{choiceStatus(status, record.progressRate)}</span>
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 300,
      render: (text: any, record: PlainObject) => (
        <div className="sharkr-table-actions Operation">
          <Button className="action" disabled={record.status === 3 ? true : false} type="link">
            <Link to={`/gallery/detail?id=${record.id}`}>详情</Link>
          </Button>
          <Button className="action" disabled={record.status !== 2 ? true : false} type="link" onClick={() => download(record.downloadUrl)}>
            下载
          </Button>
          <Button className="action" type="link" onClick={() => confirm(record.id)}>
            删除
          </Button>
          {record?.editFlag ?
            <Button className="action" type="link" onClick={() => share(record)}>
              分享编辑权限
            </Button>
            :
            null
          }
        </div>
      )
    }
  ]

  const searchSchema: any = [
    {
      key: 'keyword',
      type: 'Input',
      label: '活动名称',
      props: {
        placeholder: '请输入活动名称进行搜索',
        allowClear: true
      }
    }
  ]

  // 获取列表
  const getList = async () => {
    const res = await getImgList(searchParam && searchParam)
    if (res && res.code === 200) {
      const { result, paginationVO } = res.data || { result: [], paginationVO: {} }
      const antdPagination: IAntDPaginationProps = {
        current: paginationVO.page,
        pageSize: paginationVO.size,
        total: paginationVO.total
      }
      setPagination(antdPagination)

      result?.map(item => {
        //判断是否显示分享编辑权限，有的话则给true，没有则给false
        item.editFlag = userEmail === item.createUser || item.authorityUser?.includes(userEmail);
      })
      setDateSource(result)
    } else {
      message.error('获取合图任务记录失败')
    }
  }
  // 改变页码
  const handlePaginationChange = (page: number, pageSize = 10) => {
    setSearchParam({
      ...searchParam,
      pageNo: page,
      pageSize
    })
  }

  const handleNewMergeTask = () => {
    taskModalRef?.current?.showTaskModal?.()
  }

  const handleShareOk = () => {
    form.submit()
  };

  const onFinish = async (values: any) => {

    setLoading(true)
    const res = await sharedEditingPermissionUser({
      taskId: currTask.id,
      authorityUser: values.authorityUser.join(",")
    })
    setLoading(false)
    if (res.code === 200) {
      message.success("分享编辑权限成功")
      setShareVisible(false)
    } else {
      message.error("分享编辑权限失败")
    }
  };

  useEffect(() => {
    getList()
  }, [searchParam])
  return (
    <>
      <Breadcrumb separator=">" style={{ marginBottom: 10 }}>
        <Breadcrumb.Item><Link to={`/templatesList/list`}>模板中心</Link></Breadcrumb.Item>
        <Breadcrumb.Item>合图任务记录</Breadcrumb.Item>
      </Breadcrumb>
      <section className="sharkr-section">
        <div className="sharkr-section-header">
          <span className="sharkr-section-header-title">合图任务记录</span>
        </div>
        <div className="sharkr-section-content">
          <SharkRForm
            className="sharkr-form-inline searchForm"
            ref={searchRef}
            schema={searchSchema}
            {...{
              labelCol: { span: 5 },
              wrapperCol: { span: 19 }
            }}
          />
          <Row>
            <Col span={8} xl={8} xxl={6}>
              <Row>
                <Col offset={5}>
                  <Space>
                    <Button type="primary" onClick={onSearch}>
                      搜索
                    </Button>
                  </Space>
                </Col>
              </Row>
            </Col>
          </Row>
          <div className="sharkr-section-global-header tableBottoms">
            <div className="sharkr-tools">
              <Space>
                <Button type="primary" onClick={handleNewMergeTask}>
                  新建合图任务
                </Button>
              </Space>
            </div>
          </div>
          <Table
            className="sharkr-table"
            columns={columns}
            dataSource={dateSource}
            key="listTable"
            pagination={false}
            rowKey="id"
          />
          {dateSource.length ? (
            <div className={'pagination-area'}>
              <Pagination {...pagination} defaultCurrent={1} onChange={handlePaginationChange} />
            </div>
          ) : null}
        </div>
        <AddDrawingTask ref={taskModalRef} savePageInfo={undefined} callback={getList} />
        {/* <CreateCompositeTask ref={taskModalRef} savePageInfo={undefined} callback={getList} /> */}

        {/*分享编辑权限弹窗  */}
        <Modal
          destroyOnClose
          title="分享编辑权限"
          visible={shareVisible}
          onOk={handleShareOk}
          confirmLoading={loading}
          onCancel={() => {
            setShareVisible(false)
          }}>
          <Form
            form={form}
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 16 }}
            style={{ maxWidth: 600 }}
            onFinish={onFinish}
          >
            <Form.Item
              label="添加编辑人"
              name="authorityUser"
              initialValue={currTask?.authorityUser ? currTask?.authorityUser?.split(',') : undefined}
            >
              <MultiUserSelect
                style={{ width: '100%' }}
              />
            </Form.Item>
          </Form>
        </Modal>
      </section>
    </>
  )
}
