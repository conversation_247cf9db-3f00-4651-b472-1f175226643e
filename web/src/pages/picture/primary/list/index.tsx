import React, { useEffect, useState, useCallback, useRef } from 'react'
import {
  Table,
  Button,
  message,
  Upload,
  Space,
  Modal,
  Row,
  Col} from 'antd'
import { PlusOutlined } from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import type { UploadFile } from 'antd/es/upload/interface'
import moment from 'moment'
import { useHistory } from 'react-router-dom'
import { SharkRForm } from '@sharkr/form'
import { openUrl } from '@sharkr/utils'
import { AppConfig } from '@shark/core'
import {
  PrimaryPicQueryVO,
  PrimaryPictureListBO,
  PrimaryPictureBO,
  TEAM_ID,
  YxSpuInfoBO} from '../../../../interfaces'
import { deletePrimarySpuPicture, getPrimaryPictureList, uploadBaseSpuPicture } from '../../../../services/pictureService'
import { PictureCard } from '../../components/pictureCard'
import { getPicTypeTag } from '../../utils'
import { usePictureOperate } from '../../hooks'
import { PictureUploadModal } from '../../components/pictureUploadModal'
import { uploadFile } from '../../../../services/uploadService'

// 图片尺寸配置
const IMAGE_SIZE_CONFIG = {
  '750*1000': { width: 750, height: 1000 },
  '800*800': { width: 800, height: 800 },
  '800*1200': { width: 800, height: 1200 }
}

// 表格行数据结构
interface TableRowData {
  key: string
  channelProductId: string
  yxSpuInfoList: YxSpuInfoBO[]
  // teamId: TEAM_ID
  picType: number
  pictures: {
    '750*1000'?: PrimaryPictureBO
    '800*800'?: PrimaryPictureBO
    '800*1200'?: PrimaryPictureBO
  }
  updateTime: number
  updateUser: string
  rowSpan: number
  teamId: TEAM_ID
}

export const PicturePrimaryList: React.FC = () => {
  const searchRef = useRef<any>()
  const history = useHistory()
  const [loading, setLoading] = useState(false)
  const [dataSource, setDataSource] = useState<TableRowData[]>([])
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  })
  const [uploadModalVisible, setUploadModalVisible] = useState(false)
  const {
    handleDownloadPicture
  } = usePictureOperate()

  // 搜索表单配置
  const searchSchema: any = [
    {
      key: 'spuId',
      type: 'Input',
      label: '严选商品ID',
      props: {
        placeholder: '请输入严选商品ID',
        allowClear: true
      }
    },
    {
      key: 'itemName',
      type: 'Input',
      label: '严选商品名称',
      props: {
        placeholder: '请输入商品名称',
        allowClear: true
      }
    },
    {
      key: 'secondBuId',
      type: 'Select',
      label: '归属BU',
      props: {
          placeholder: '请输入二级BU ID',
          allowClear: true
      }
    },
    {
      key: 'channelProductId',
      type: 'Input',
      label: '渠道商品ID',
      props: {
        placeholder: '请输入渠道商品ID',
        allowClear: true
      }
    },
    {
      key: 'operator',
      type: 'IcacSingleUserSelect',
      label: '操作人'
    },
    {
      key: 'timeRange',
      type: 'RangePicker',
      label: '操作时间',
      props: {
        placeholder: ['开始时间', '结束时间'],
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
        style: { width: '350px' }
      }
    }
  ]

  // 获取列表数据
  const fetchData = useCallback(async (params?: PrimaryPicQueryVO, pageNum?: number, pageSize?: number) => {
    setLoading(true)
    try {
      const queryParams = {
        ...params,
        pageNum: pageNum || pagination.current,
        pageSize: pageSize || pagination.pageSize
      }

      const response = await getPrimaryPictureList(queryParams)

      if (response && response.code === 200) {
        const { result, paginationVO } = response.data
        const tableData = transformDataToTableRows(result)
        setDataSource(tableData)
        setPagination({
          current: paginationVO.page,
          pageSize: paginationVO.size,
          total: paginationVO.total
        })
      } else {
        message.error('获取数据失败')
      }
    } catch (error) {
      console.error('获取商品主图列表失败:', error)
      message.error('获取数据失败')
    } finally {
      setLoading(false)
    }
  }, [pagination.current, pagination.pageSize])

  // 转换数据为表格行格式
  const transformDataToTableRows = (list: PrimaryPictureListBO[]): TableRowData[] => {
    const rows: TableRowData[] = []

    list.forEach(item => {
      // 按图片类型分组处理
      item.primaryPictures.forEach(tabData => {
        const pictureMap: TableRowData['pictures'] = {}
        let updateTime = 0
        let updateUser

        // 按尺寸分组图片
        tabData.primaryPictureList.forEach(pic => {
          const sizeKey = `${pic.picSize}` as keyof typeof pictureMap
          if (sizeKey in IMAGE_SIZE_CONFIG) {
            pictureMap[sizeKey] = pic
            if (pic.updateTime > updateTime) {
              updateTime = pic.updateTime
              updateUser = pic.updateUser
            }
          }
        })

        // 为每个图片类型创建一行
        rows.push({
          key: `${item.channelProductId}-${tabData.picType}`,
          channelProductId: item.channelProductId,
          yxSpuInfoList: item.yxSpuInfoList,
          picType: tabData.picType,
          pictures: pictureMap,
          updateTime,
          updateUser,
          rowSpan: 1,
          teamId: item.primaryPictures[0].primaryPictureList[0].teamId
        })
      })
    })

    // 计算合并单元格
    return calculateRowSpan(rows)
  }

  // 计算行合并
  const calculateRowSpan = (rows: TableRowData[]): TableRowData[] => {
    const result = [...rows]
    let i = 0
    
    while (i < result.length) {
      let spanCount = 1
      const currentChannelProductId = result[i].channelProductId
      
      // 计算相同channelProductId的行数
      for (let j = i + 1; j < result.length; j++) {
        if (result[j].channelProductId === currentChannelProductId) {
          spanCount++
        } else {
          break
        }
      }
      
      // 设置第一行的rowSpan，其他行设置为0
      result[i].rowSpan = spanCount
      for (let k = i + 1; k < i + spanCount; k++) {
        result[k].rowSpan = 0
      }
      
      i += spanCount
    }
    
    return result
  }

  // 搜索
  const handleSearch = async () => {
    const formValues = await searchRef.current?.submit()
    const params: PrimaryPicQueryVO = {
      spuId: formValues.spuId ? Number(formValues.spuId) : undefined,
      channelProductId: formValues.channelProductId,
      itemName: formValues.itemName,
      operator: formValues.operator,
      // showPictureType: formValues.showPictureType,
      secondBuIds: [],
      startTime: formValues.timeRange?.[0]?.valueOf(),
      endTime: formValues.timeRange?.[1]?.valueOf()
    }

    setPagination(prev => ({ ...prev, current: 1 }))
    fetchData(params)
  }

  // 重置
  const handleReset = () => {
    searchRef.current?.reset()
    setPagination(prev => ({ ...prev, current: 1 }))
    fetchData()
  }

  // 处理分页变化
  const handleTableChange = (page: number, pageSize: number) => {
    setPagination(prev => ({ ...prev, current: page, pageSize }))
    // 获取新页面的数据
    fetchData(undefined, page, pageSize)
  }

  // 上传图片前的校验
  const beforeUpload = (file: File, expectedSize: keyof typeof IMAGE_SIZE_CONFIG) => {
    return new Promise<boolean>((resolve) => {
      const img = new Image()
      img.onload = () => {
        const { width: expectedWidth, height: expectedHeight } = IMAGE_SIZE_CONFIG[expectedSize]
        if (img.width !== expectedWidth || img.height !== expectedHeight) {
          message.error(`图片尺寸必须为 ${expectedSize}`)
          resolve(false)
        } else {
          resolve(true)
        }
      }
      img.onerror = () => {
        message.error('图片格式不正确')
        resolve(false)
      }
      img.src = URL.createObjectURL(file)
    })
  }

  // 处理图片上传
  const handleUpload = async (file: File, sizeType: keyof typeof IMAGE_SIZE_CONFIG, rowData: TableRowData) => {
    const isValid = await beforeUpload(file as any, sizeType)
    if (!isValid) return

    const response = await uploadFile(file)
    if (response.data.code === 200) {
      const picUrl = response.data.data
      const params = rowData.yxSpuInfoList.map(item => ({
        channelProductId: rowData.channelProductId,
        spuId: item.spuId,
        picSize: sizeType,
        picUrl,
        picType: rowData.picType,
        teamId: rowData.teamId,
        picName: file.name
      }))
      const res = await uploadBaseSpuPicture(params)
      if (res[0].success) {
        message.success('操作成功')
        fetchData()
      } else {
        message.error(res[0].error)
      }
    } else {
      message.error('图片上传失败')
    }
  }

  // 跳转主图详情
  const handleViewDetail = (spuId: number) => {
    // history.push(`/picture/base/detail/${picture.spuId}`)
    openUrl(`${AppConfig.contextPath}/#/picture/base/detail/${spuId}`)
  }

  // 删除
  const handleDeletePrimaryPicture = (picture: PrimaryPictureBO) => {
    Modal.confirm({
      title: '操作提示',
      content: '删除之后不可恢复，是否确认删除？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        const res = await deletePrimarySpuPicture({
          primaryId: picture.primaryId,
          spuId: picture.spuId
        })
        if (res && res.code === 200) {
          message.success('操作成功')
          fetchData()
        } else {
          message.error(res.message)
        }
      }
    })
  }


  // 获取表格列配置
  function getTableColumns(): ColumnsType<TableRowData> {
    return [
      {
        title: '渠道商品ID',
        dataIndex: 'channelProductId',
        key: 'channelProductId',
        width: 120,
        fixed: 'left',
        render: (text: string, record: TableRowData) => ({
          children: <>{text}</>,
          props: {
            rowSpan: record.rowSpan
          }
        })
      },
      {
        title: '严选商品',
        dataIndex: 'yxSpuInfoList',
        key: 'yxSpu',
        width: 200,
        render: (list: YxSpuInfoBO[], record: TableRowData) => {
          const children = (
            <Space direction="vertical">
              {list.map((item, index) => (
                <Button
                  size="small"
                  type="link"
                  key={item.spuId}
                  onClick={() => handleViewDetail(item.spuId)}>
                    {`${item.spuId} ${item.name}`}
                </Button>
              ))}
            </Space>
          )
          return {
            children,
            props: {
              rowSpan: record.rowSpan
            }
          }
        }
      },
      {
        title: '归属BU',
        dataIndex: 'yxSpuInfoList',
        key: 'buName',
        width: 150,
        render: (list: YxSpuInfoBO[], record: TableRowData) => {
          const children = (
            <Space direction="vertical">
              {list.map((item, index) => (
                <div key={item.secondBuId}>{`${item.buName}-${item.secondBuName}`}</div>
              ))}
            </Space>
          )
          return {
            children,
            props: {
              rowSpan: record.rowSpan
            }
          }
        }
      },
      {
        title: '标签',
        dataIndex: 'picType',
        key: 'picType',
        width: 80,
        render: (picType: number) => getPicTypeTag(picType)?.text
      },
      {
        title: '750*1000主图',
        dataIndex: 'pictures',
        key: '750*1000',
        width: 180,
        render: (pictures: TableRowData['pictures'], record: TableRowData) =>
          renderPictureCell(pictures['750*1000'], '750*1000', record)
      },
      {
        title: '800*800主图',
        dataIndex: 'pictures',
        key: '800*800',
        width: 180,
        render: (pictures: TableRowData['pictures'], record: TableRowData) =>
          renderPictureCell(pictures['800*800'], '800*800', record)
      },
      {
        title: '800*1200主图',
        dataIndex: 'pictures',
        key: '800*1200',
        width: 180,
        render: (pictures: TableRowData['pictures'], record: TableRowData) =>
          renderPictureCell(pictures['800*1200'], '800*1200', record)
      },
      {
        title: '操作人',
        dataIndex: 'updateUser',
        key: 'updateUser',
        width: 100
      },
      {
        title: '最近更新时间',
        dataIndex: 'updateTime',
        key: 'updateTime',
        width: 160,
        render: (time: number) => moment(time).format('YYYY-MM-DD HH:mm:ss')
      }
    ]
  }

  // 渲染图片单元格
  function renderPictureCell(picture: PrimaryPictureBO | undefined, sizeType: string, rowData: TableRowData) {
    if (picture) {
      // 有图片时显示PictureCard
      return (
        <PictureCard
          // width={160}
          picUrl={picture.picUrl}
          menus={[
            {
              key: 'replace',
              label: (
                <Upload
                  showUploadList={false}
                  beforeUpload={(file) => {
                    handleUpload(file, sizeType as keyof typeof IMAGE_SIZE_CONFIG, rowData)
                    return false
                  }}
                >
                  <div>上传图片</div>
                </Upload>
              ),
              style: {
                width: '100%'
              }
            },
            {
              key: 'select',
              label: '从主图库选择替换图片',
              onClick: () => handleViewDetail(picture.spuId)
            },
            {
              key: 'download',
              label: '下载图片',
              onClick: () => handleDownloadPicture(picture.picUrl, `${picture.channelProductId}-${sizeType}`)
            },
            {
              key: 'delete',
              label: '删除图片',
              onClick: () => handleDeletePrimaryPicture(picture)
            }
          ]}
        />
      )
    } else {
      // 无图片时显示上传按钮
      return (
        <div className="upload-placeholder">
          <Upload
            listType="picture-card"
            showUploadList={false}
            beforeUpload={(file) => {
              handleUpload(file, sizeType as keyof typeof IMAGE_SIZE_CONFIG, rowData)
              return false
            }}
          >
            <div>
              <div><PlusOutlined /></div>
              <div style={{ marginTop: 8 }}>上传图片</div>
            </div>
          </Upload>
        </div>
      )
    }
  }

  useEffect(() => {
    fetchData()
  }, [])

  return (
    <>
      {/* <Breadcrumb separator=">" style={{ marginBottom: 10 }}>
          <Breadcrumb.Item>图片管理</Breadcrumb.Item>
          <Breadcrumb.Item>底图列表</Breadcrumb.Item>
      </Breadcrumb> */}
      <section className="sharkr-section picture-base-list">
        <div className="sharkr-section-header with-tools">
            <span className="sharkr-section-header-title">渠道商品主图</span>
            <span className="sharkr-section-header-sub-title">{`（共${dataSource.length}条）`}</span>
            <div className="sharkr-tools">
                <Button type="primary" onClick={() => setUploadModalVisible(true)}>上传图片</Button>
            </div>
        </div>
        <div className="sharkr-section-content">
          <SharkRForm
            className="sharkr-form-inline searchForm"
            ref={searchRef}
            schema={searchSchema}
            {...{
              labelCol: { span: 6 },
              wrapperCol: { span: 18 }
            }}
          />
          <Row>
            <Col offset={2}>
              <Space>
                <Button type="primary" onClick={handleSearch}>
                  搜索
                </Button>
                <Button onClick={handleReset}>
                  重置
                </Button>
              </Space>
            </Col>
          </Row>
          <Table
            className="sharkr-table margin-t-4x"
            loading={loading}
            dataSource={dataSource}
            columns={getTableColumns()}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              showSizeChanger: true,
              showQuickJumper: true,
              onChange: handleTableChange,
              onShowSizeChange: handleTableChange
            }}
            scroll={{ x: 1200 }}
          />
        </div>
      </section>
      <PictureUploadModal
        isOpen={uploadModalVisible}
        onCancel={() => setUploadModalVisible(false)}
        onOk={() => {
            setUploadModalVisible(false);
            fetchData();
        }}
      />
    </>
  )
}
