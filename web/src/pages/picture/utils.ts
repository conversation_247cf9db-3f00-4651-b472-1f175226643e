import { PIC_TYPE, TEAM_ID } from '../../interfaces';

/**
  * @description: 根据图片类型获取标签配置
  * @param {PIC_TYPE} picType 图片类型
  * @returns {object | undefined} 标签配置对象
  */
export const getPicTypeTag = (picType: PIC_TYPE): { color: string; text: string } | undefined => {
  let tag;
  switch (picType) {
    case PIC_TYPE.DAILY:
      tag = {
        color: 'blue',
        text: '日常'
      };
      break;
    case PIC_TYPE.PROMOTION:
      tag = {
        color: 'orange',
        text: '活动'
      };
      break;
    // no default
  }

  return tag
}

/**
  * @description: 根据团队空间获取标签配置
  * @param {TEAM_ID} teamId 团队空间
  * @returns {object | undefined} 标签配置对象
  */
export const getTeamTag = (teamId: number): { color: string; text: string } | undefined => {
  let tag
  switch (teamId) {
    case TEAM_ID.TB:
      tag = {
        color: 'blue',
        text: '淘系专属'
      }
      break
    case TEAM_ID.JD:
      tag = {
        color: 'orange',
        text: '京东专属'
      }
      break
    // no default
  }

  return tag;
}