.picture-upload-modal {
  .ant-modal-body {
    max-height: 70vh;
    overflow-y: auto;
  }

  .upload-section {
    margin-bottom: 16px;
  }

  .upload-result-section {
    margin-top: 16px;
    
    h4 {
      margin-bottom: 12px;
      font-weight: 600;
    }

    .ant-table {
      .ant-table-tbody > tr > td {
        padding: 8px 12px;
      }
    }
  }

  .form-item-help {
    font-size: 12px;
    color: #666;
    margin-top: 4px;
  }

  .upload-tips {
    background: #f6f8fa;
    border: 1px solid #e1e4e8;
    border-radius: 4px;
    padding: 12px;
    margin-top: 8px;
    font-size: 12px;
    color: #586069;

    .tip-title {
      font-weight: 600;
      margin-bottom: 8px;
    }

    .tip-item {
      margin-bottom: 4px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .status-tag {
    &.success {
      background-color: #f6ffed;
      border-color: #b7eb8f;
      color: #52c41a;
    }

    &.error {
      background-color: #fff2f0;
      border-color: #ffccc7;
      color: #ff4d4f;
    }
  }

  .error-message {
    color: #ff4d4f;
    font-size: 12px;
  }
}

// 全局样式覆盖
.ant-upload-list-item {
  .ant-upload-list-item-name {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
