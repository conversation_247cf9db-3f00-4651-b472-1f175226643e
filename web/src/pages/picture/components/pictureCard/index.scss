.picture-card-wrapper {
  .picture-card {
    // width: 200px;
    border-radius: 8px;
    overflow: hidden;
    // transition: all 0.3s ease;
    // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    // border: 1px solid #f4f4f4;

    &:hover {
      // box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      // transform: translateY(-2px);
    }

    .ant-card-body {
      padding: 0 10px 10px !important;
    }

    .picture-cover {
      position: relative;
      padding-top: 10px;
      padding: 10px 10px 0;
      text-align: center;
      // height: 230px;
      // height: 200px;
      overflow: hidden;
      background: #f5f5f5;

      .picture-image {
        width: 100%;
        height: 100%;
        object-fit: contain;
        cursor: pointer;
        transition: transform 0.3s ease;
        border-radius: 8px;

        &:hover {
          // transform: scale(1.05);
        }
      }

      .ant-image-mask {
        border-radius: 8px;
      }

      .picture-sub {
        line-height: 30px;
        color: #666;
      }

      .picture-actions {
        position: absolute;
        bottom: 8px;
        right: 8px;
        opacity: 0;
        transition: opacity 0.3s ease;

        .more-actions-btn {
          width: 32px;
          height: 32px;
          // background: rgba(255, 255, 255, 0.6);
          background: #fff;
          opacity: 0.8;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          color: #333;
          font-size: 16px;
          transition: all 0.3s ease;

          &:hover {
            // background: rgba(255, 255, 255, 0.8);
            opacity: 1;
            transform: scale(1.1);
          }
        }
      }

      &:hover .picture-actions {
        opacity: 1;
      }
    }

    .picture-info {
      display: flex;
      align-items: center;
      justify-content: center;
      padding-top: 10px;
      .info-name {
        // width: 124px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        // font-size: 12px;
        color: #333;
      }
    }
  }
}

// 操作菜单样式
.ant-dropdown-menu {
  .ant-dropdown-menu-item {
    &.ant-dropdown-menu-item-danger {
      color: #ff4d4f;

      &:hover {
        background-color: #fff2f0;
      }
    }
  }
}

// 加载状态样式
.picture-card-loading {
  .picture-cover {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;

    .ant-spin {
      color: #1890ff;
    }
  }
}