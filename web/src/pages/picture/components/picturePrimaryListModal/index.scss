.picture-primary-list-modal {
  .ant-modal-body {
    padding: 20px;
    max-height: 80vh;
    overflow-y: auto;
  }

  .picture-list-content {
    min-height: 200px;

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 60px 20px;
      color: #666;

      .ant-spin {
        margin-bottom: 16px;
      }

      p {
        margin: 0;
        font-size: 14px;
      }
    }

    .ant-empty {
      padding: 60px 20px;
    }
  }

  .picture-list-body {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    align-items: center;
    // justify-content: center;
  }
}

