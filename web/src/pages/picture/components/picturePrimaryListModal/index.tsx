import React, { useEffect, useState } from 'react'
import { Modal, Spin, message, Empty } from 'antd'
import { BasePictureBO, SpuPictureListQueryParams, TEAM_ID, TEAM_NAME_RECORD } from '../../../../interfaces'
import { getSpuPictureList } from '../../../../services/pictureService'
import { PictureCard } from '../pictureCard'
import './index.scss'
import { PictureOperateModal } from '../pictureOperateModal'
import { getPicTypeTag } from '../../utils'
import { usePictureOperate } from '../../hooks'

export interface PicturePrimaryListModalProps {
  /** 弹窗是否可见 */
  isOpen: boolean
  /** 关闭弹窗回调 */
  onCancel: () => void
  /** SPU ID */
  spuId?: number
  /** 团队 ID */
  teamId?: TEAM_ID
  /** SPU 名称 */
  spuName?: string
}

export const PicturePrimaryListModal: React.FC<PicturePrimaryListModalProps> = ({
  isOpen,
  onCancel,
  spuId,
  teamId,
  spuName
}) => {
  const [loading, setLoading] = useState(false)
  const [pictureList, setPictureList] = useState<BasePictureBO[]>([])
  const {
    optModalParams,
    setOptModalParams,
    handleDeleteSpuPicture,
    handleCopySpuPicture,
    handleEditSpuPicture,
    handleDownloadPicture
  } = usePictureOperate()

  // 获取图片列表
  const fetchPictureList = async () => {
    if (!spuId || !teamId) {
      return
    }

    setLoading(true)
    try {
      const params: SpuPictureListQueryParams = {
        spuId,
        teamId
      }
      
      const response = await getSpuPictureList(params)
      
      if (response && response.code === 200) {
        setPictureList(response.data.pictureList || [])
      } else {
        message.error('获取图片列表失败')
        setPictureList([])
      }
    } catch (error) {
      console.error('获取图片列表失败:', error)
      message.error('获取图片列表失败')
      setPictureList([])
    } finally {
      setLoading(false)
    }
  }

  // 当弹窗打开时获取数据
  useEffect(() => {
    if (isOpen) {
      fetchPictureList()
    }
  }, [isOpen, spuId, teamId])

  // 关闭弹窗
  const handleCancel = () => {
    setPictureList([])
    onCancel()
  }

  return (
    <Modal
      centered
      title={`【${teamId ? TEAM_NAME_RECORD[teamId] : ''}】${spuId} ${spuName}`}
      open={isOpen}
      onCancel={handleCancel}
      footer={null}
      width={900}
      className="picture-primary-list-modal"
      destroyOnClose
    >
      <div className="picture-list-content">
        {loading ? (
          <div className="loading-container">
            <Spin size="large" />
            <p>加载中...</p>
          </div>
        ) : pictureList.length > 0 ? (
          <div className="picture-list-body">
            {pictureList.map((picture) => (
              <PictureCard
                key={picture.baseId}
                title={picture.channelProductId}
                subTitle={picture.picSize}
                picUrl={picture.picUrl}
                tag={getPicTypeTag(picture.picType)}
                menus={[
                  {
                    key: 'copy',
                    label: '复制图片',
                    onClick: () => handleCopySpuPicture({
                      picUrl: picture.picUrl,
                      picSize: picture.picSize,
                      picName: picture.picName,
                      spuId: picture.spuId
                    })
                  },
                  {
                    key: 'download',
                    label: '下载图片',
                    onClick: () => handleDownloadPicture(picture.picUrl, picture.channelProductId)
                  },
                  {
                    key: 'edit',
                    label: '编辑图片信息',
                    onClick: () => handleEditSpuPicture(picture)
                  },
                  {
                    key: 'delete',
                    label: '删除',
                    onClick: () => handleDeleteSpuPicture(picture)
                  }
                ]}
              />
            ))}
          </div>
        ) : (
          <Empty
            description="暂无图片数据"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        )}
      </div>
      {
        optModalParams && (
          <PictureOperateModal
            isOpen
            {...optModalParams}
            onCancel={() => setOptModalParams(undefined)}
          />
        )
      }
    </Modal>
  )
}
