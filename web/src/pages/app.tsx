import React from 'react'
import { message } from 'antd'
import { HashRouter as Router } from 'react-router-dom'
import { AppConfig } from '@shark/core'
import {
  SharkRMenuProps,
  SharkRMenuItemClickEvent,
  SharkRMenuItem,
  SharkRIcon
} from '@sharkr/components'
import { Layout } from './layouts'
import './app.scss'
import { AppRoute } from './appRoute'

export const App: React.FC = () => {
  const siderMenu: SharkRMenuProps = {
    data: [
      {
        name: '首页',
        icon: 'home',
        authCondition: 100,
        link: {
          href: '#/home'

        }
      },
      {
        name: '合图工具',
        icon: 'sharkr-xitongguanli',
        children: [
          {
            name: '商品图库',
            icon: 'item',
            activeRouters: ['#/gallery/list'],
            link: {
              href: '#/gallery/list'
            }
          },
          {
            name: '模板中心',
            icon: 'item',
            activeRouters: ['#/templatesList/list'],
            link: {
              href: '#/templatesList/list'
            },
          },
          {
            name: '个人中心',
            icon: 'item',
            activeRouters: ['#/personalList/list'],
            link: {
              href: '#/personalList/list'
            }
          }
        ]
      },
      {
        name: '营销工具',
        icon: 'sharkr-xitongguanli',
        children: [
          {
            name: '模板管理',
            icon: 'item',
            activeRouters: ['#/activity/template/list'],
            link: {
              href: '#/activity/template/list'
            }
          }
        ]
      },
      {
        name: '商品管理',
        icon: 'sharkr-xitongguanli',
        children: [
          {
            name: '快速铺货',
            icon: 'item',
            activeRouters: ['#/channel/putGoods'],
            link: {
              href: '#/channel/putGoods'
            }
          }
        ]
      }
    ]
  }
  // AppConfig.configure({ contextPath: '/doc/eagler/authorizion' });
  return (
    <Router>
      <Layout siderMenu={siderMenu}>
        <AppRoute />
      </Layout>
    </Router>
  )
}
