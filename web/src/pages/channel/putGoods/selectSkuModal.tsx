import React from 'react'
import { Button, Form, Input, Select, Row, Col } from 'antd'
import { ProductSelectModal } from '@eagler/commodity-selector-new'

import requestList from '../../../consts/requestList'

const { Option } = Select

export const SelectSkuModal: React.FC<{
  channelId: number
  visible: boolean
  handleOk: Function
  handleCancel: Function
}> = props => {
  const customSearchForm = (
    <>
      <Form.Item label="商品名" name="itemName">
        <Input placeholder="请输入商品名" />
      </Form.Item>
      <Row>
        <Col span={6}>
          <span className="float-right margin-h-2x">:</span>
          <Form.Item noStyle name="type">
            <Select defaultValue={0} className="float-right">
              <Option value={1}>商品ID</Option>
              <Option value={0}>SKUID</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item noStyle name="value">
            <Input placeholder="请输入" />
          </Form.Item>
        </Col>
      </Row>
      <Col offset={6} className="margin-t-4x">
        <Form.Item>
          <Button type="primary" htmlType="submit">
            搜索
          </Button>
        </Form.Item>
      </Col>
    </>
  )
  const filterSelectSkus = (itemList: any[]) => {
    let allSkuList = []
    itemList.forEach((item: any) => {
      const { itemId, itemName, phyCategoryPathList } = item
      let phyCategory = ''
      phyCategory = phyCategoryPathList
        ? phyCategoryPathList.map(cate => cate.name).join('>')
        : null
      const selectList = item.skuList.filter((sku: any) => !sku.disabled)
      const rowSpan = selectList?.length || 0
      const skuList = selectList.map((sku, index) => {
        return {
          skuId: sku.id,
          displayString: sku.displayString,
          itemId,
          itemName,
          phyCategory,
          rowSpan: !index ? rowSpan : 0
        }
      })
      allSkuList = allSkuList.concat(skuList)
    })
    props.handleOk(allSkuList)
  }

  return (
    <>
      {props.visible && (
        <ProductSelectModal
          key={'selectSku'}
          model={'custom'}
          visible={props.visible}
          onOk={filterSelectSkus}
          onCancel={props.handleCancel}
          ifNeedCate={false}
          queryProductListUrl={requestList.channel.queryChannelItem}
          beforeQuery={() => {
            const params = {
              channelId: props.channelId,
              page: 1,
              size: 10000
            }
            return params
          }}
          afterQuery={(e: any) => {
            const itemList = e.result || []
            itemList.forEach((item: any) => {
              item.itemId = item.id
              item.itemName = item.name
              // item.disabled = item.skuList.some((sku:any)=> !sku.state);

              item.skuList.forEach((sku: any) => {
                sku.skuId = sku.id
                // sku.disabled = !sku.state
              })
            })
            return itemList
          }}>
          {customSearchForm}
        </ProductSelectModal>
      )}
    </>
  )
}
