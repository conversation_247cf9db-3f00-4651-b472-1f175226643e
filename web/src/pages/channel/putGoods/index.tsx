/*
 * @Description:
 * @Author: chenxiaopan <EMAIL>
 * @Date: 2022-11-28 20:40:48
 */

import React, { useEffect, useState, useRef, useMemo } from 'react'
import { Button, message, Space, Spin, Table, Form, Modal, Select, TableColumnProps } from 'antd'
import { SharkRForm, ISchema, SharkRFormGlobal, useSharkRFormGlobalStore } from '@sharkr/form'

import { IChannelInfo, ISkuInfo, IChannelTypeInfo } from '../../../interfaces'
import { channelService } from '../../../services'
import { ColWithDel, ColWithCategory } from '../../../components'
import RequestDecorator from '../../../services/requestDecorator'

import { ColWithAttribution } from '../../../components/colWithAttribution'
import { PutGoodRes, DefaultChannelSkuId, ChannelType, ColKey } from '../../../consts'
import { getItemMap, getAddProductParams, getFieldList } from '../../../utils/channel'

import { SelectSkuModal } from './selectSkuModal'

import './index.scss'

const { Option } = Select
export const PutGoods: React.FC = () => {
  return (
    <SharkRFormGlobal>
      <Inner />
    </SharkRFormGlobal>
  )
}
const Inner: React.FC = () => {
  const formRefs = useRef(null)
  const [channelList, setChannelList] = useState<any[]>([])
  // 渠道选择
  const schema: ISchema = [
    {
      key: 'channelId',
      type: 'Select',
      label: '渠道名',
      ui: {
        size: 'lg',
        required: true
      },
      props: {
        showSearch: true,
        placeholder: '请选择'
      },
      options: {
        action: async (file, form) => {
          return new Promise((resolve, reject) => {
            try {
              if (!channelList.length) {
                channelService.getChannelList().then(
                  (res: any) => {
                    // const jdChannelList =
                    //   res instanceof Array
                    //     ? res
                    //         .filter((item: IChannelInfo) => item.channelName.includes('京东'))
                    //         .map((item: IChannelInfo) => ({
                    //           name: item.channelName,
                    //           value: item.channelId
                    //         }))
                    //     : []
                    // const aliChannelList =
                    //   res instanceof Array
                    //     ? res
                    //         .filter((item: IChannelInfo) => item.channelName.includes('淘'))
                    //         .map((item: IChannelInfo) => ({
                    //           name: item.channelName,
                    //           value: item.channelId
                    //         }))
                    //     : []
                    // const allList = jdChannelList.concat(aliChannelList)
                    const allList = res.map((item: IChannelInfo) => ({
                      name: item.channelName,
                      value: item.channelId
                    }))

                    console.log(allList)

                    setChannelList(allList)
                    resolve(allList)
                  },
                  e => {
                    reject(e)
                  }
                )
              } else {
                resolve(channelList)
              }
            } catch {
              message.error('请稍后重试')
            }
          })
        }
      },
      rules: [
        {
          required: true,
          message: '请选择渠道名'
        }
      ]
    }
  ]
  // 多表单验证
  const store = useSharkRFormGlobalStore()
  const [skuList, setSkuList] = useState<ISkuInfo[]>([])
  const [loading, setLoading] = useState<boolean>(false)
  const [showModal, setShowModal] = useState<boolean>(false)
  const [channelInfo, setChannelInfo] = useState<IChannelTypeInfo>({ channelId: 0, type: 0 })
  const [doneFlag, setDoneFlag] = useState<boolean>(false)
  const [defaultOptions, setDefaultOptions] = useState([])
  const [itemCate, setItemCate] = useState(new Map())
  const [productList, setProductList] = useState<number[] | null>([])

  const columns: TableColumnProps<any & { _hide?: boolean }>[] = [
    {
      title: '商品信息',
      render: (value: any) => {
        const obj = {
          children: (
            <ColWithDel info={value} itemType="item" handleDel={delItem} delDisabled={doneFlag} />
          ),
          props: {
            shouldCellUpdate: (record: any, preRecord: any) =>
              record.skuId !== preRecord.skuId || record.rowSpan !== preRecord.rowSpan,
            rowSpan: value.rowSpan
          }
        }
        return obj
      }
    },
    {
      title: '商品类目',
      dataIndex: 'phyCategory',
      width: 200
    },
    {
      title: 'SKU信息',
      render: (value: any) => (
        <ColWithDel info={value} itemType="sku" handleDel={delSku} delDisabled={doneFlag} />
      )
    },
    {
      title: '渠道类目',
      render: (value: any) => {
        const obj = {
          children: (
            <ColWithCategory
              info={value}
              channelId={channelInfo.channelId}
              handleChange={changeCategory}
              disabled={doneFlag}
              defaultOptions={defaultOptions}
            />
          ),
          props: {
            shouldCellUpdate: (record: any, preRecord: any) => record.itemId !== preRecord.itemId,
            rowSpan: value.rowSpan
          }
        }
        return obj
      }
    },
    {
      title: '产品关键属性',
      render: (value: any) => {
        const obj = {
          children: (
            <ColWithAttribution
              info={value}
              channelId={channelInfo.channelId}
              disabled={doneFlag}
              cateInfo={itemCate[value.itemId]}
            />
          ),
          props: {
            shouldCellUpdate: (record: any, preRecord: any) =>
              record.itemId !== preRecord.itemId ||
              itemCate[record.itemId].isLeaf ||
              itemCate[record.itemId].changedCate,
            rowSpan: value.rowSpan
          }
        }
        return obj
      }
    },
    {
      title: '渠道商品ID',
      dataIndex: 'channelItemId'
    },
    {
      title: '渠道SKUID',
      dataIndex: 'channelSkuId'
    },
    {
      title: '铺货失败原因',
      dataIndex: 'reason'
    }
  ]
  // 类目选择变化
  const changeCategory = e => {
    const itemId = e.itemId
    const newMap = { ...itemCate }
    const categoryIds = [...e.value]
    const categoryId = categoryIds.pop()
    const oldCategoryId = itemCate[itemId] ? itemCate[itemId].categoryIds.pop() : 0

    newMap[itemId] = {
      categoryIds: e.value,
      isLeaf: e.isLeaf,
      changedCate: categoryId === oldCategoryId
    }
    if (!e.isLeaf) {
      message.warn('请选择继续选择渠道分类至末级')
      setItemCate(newMap)
    } else {
      if (channelInfo.type === ChannelType.Ali) {
        setItemCate(newMap)
      } else {
        channelService.queryPublicProps({ channelId: channelInfo.channelId, categoryId }).then(
          res => {
            newMap[itemId].fieldList = res.map((field: any) => {
              const { id, name, type, rules, fieldList, options, value, values } = field
              return { id, name, type, rules, fieldList, options, value, values }
            })
            setItemCate(newMap)
          },
          () => {
            message.error('关键属性获取失败')
          }
        )
      }
    }
  }
  const putGoodsRequest = () => {
    let flag = true
    const instanceQue = new RequestDecorator({ requestAjax: channelService.submitPutGoods })

    const promiseList: any[] = []
    const itemMap = getItemMap(skuList)
    itemMap.forEach((value, key) => {
      const params = { channelId: channelInfo.channelId, itemId: key, skuIdList: value }
      let cateInfo
      let productInfo
      if (channelInfo.type !== ChannelType.JD) {
        const categoryIds = [...itemCate[key].categoryIds]
        const categoryId = categoryIds.pop()
        if (!itemCate[key].isLeaf) {
          message.error('渠道类目请配置至末级')
          flag = false
          return
        } else {
          cateInfo = { categoryId }
        }
        if (channelInfo.type === ChannelType.TianMao) {
          const promiseLen = promiseList.length
          const productId = productList ? productList[promiseLen] : 0
          productInfo = { productId }
        } else {
          productInfo = null
        }
      } else {
        cateInfo = null
        productInfo = null
      }
      promiseList.push(instanceQue.request({ ...params, ...cateInfo, ...productInfo }))
    })
    // 提交铺货
    try {
      if (flag) {
        Promise.all(promiseList)
          .then(
            resList => {
              let _skuResultList: ISkuInfo[] = []
              resList.forEach(itemRes => {
                let _skuList = skuList.filter((sku: ISkuInfo) => sku.itemId === itemRes.itemId)
                if (itemRes.result === PutGoodRes.fail) {
                  // 铺货失败商品需要在列表中回显失败原因
                  _skuList = _skuList.map((sku: ISkuInfo) => {
                    return { ...sku, reason: itemRes.errMsg }
                  })
                  _skuResultList = _skuResultList.concat(_skuList)
                } else {
                  const channelItemId = itemRes.channelItemId
                  const skuResultList = itemRes.skuResultList
                  _skuList = _skuList.map((sku: ISkuInfo) => {
                    const skuResult = skuResultList.filter((skuR: any) => skuR.skuId === sku.skuId)
                    const channelSkuId = skuResult.length
                      ? skuResult[0].channelSkuId
                      : DefaultChannelSkuId
                    return { ...sku, channelItemId, channelSkuId }
                  })
                  _skuResultList = _skuResultList.concat(_skuList)
                }
              })
              console.log('_skuResultList', _skuResultList)
              setLoading(false)
              setDoneFlag(true)
              setSkuList(_skuResultList)
            },
            error => {
              setLoading(false)
            }
          )
          .catch(err => {
            setLoading(false)
          })
      } else {
        setLoading(false)
      }
    } catch {
      setLoading(false)
    }
  }
  // 发布商品请求
  const addProductRequest = (values: any[]) => {
    const instanceQue = new RequestDecorator({ requestAjax: channelService.addProduct })

    const promiseList: any[] = []
    const itemCateWithField = getFieldList(values, itemCate)

    const itemMap = getItemMap(skuList)
    itemMap.forEach((_, key) => {
      const params = getAddProductParams(key, itemCateWithField[key])
      promiseList.push(instanceQue.request({ ...params, channelId: channelInfo.channelId }))
    })
    //  发布商品
    try {
      Promise.all(promiseList)
        .then(
          (resList: number[]) => {
            setProductList(resList)
          },
          error => {
            setLoading(false)
          }
        )
        .catch(() => {
          setLoading(false)
        })
    } catch {
      setLoading(false)
    }
  }
  useEffect(() => {
    putGoodsRequest()
  }, [productList?.length])

  // 提交铺货
  const handleSubmit = async () => {
    if (channelInfo.type === ChannelType.TianMao) {
      store.validate().then(async () => {
        setLoading(true)
        const values = await store.submit()
        Modal.confirm({
          title: '确认产品发布信息',
          content: '产品发布成功后进行铺货，是否确认商品配置并进行产品发布？',
          okText: '确认',
          cancelText: '取消',
          onOk: () => addProductRequest(values),
          onCancel: () => setLoading(false)
        })
      })
    } else {
      setLoading(true)
      putGoodsRequest()
    }
  }

  // 删除商品
  const delItem = (item: any) => {
    const itemId = item.itemId || 0
    const _list = skuList.filter((sku: any) => sku.itemId === itemId)
    const ids = _list.map((sku: any) => {
      return sku.skuId
    })
    batchDeleteSku(ids)
  }

  // 删除sku
  const delSku = (sku: any) => {
    const skuId = sku.skuId || 0
    const itemId = sku.itemId || 0
    const rowSpan = sku.rowSpan || 0
    if (rowSpan === 1) {
      // 删除仅为一个sku的商品 || 删除非首个sku
      batchDeleteSku([skuId])
    } else {
      // 多个sku，删除第一个sku，其余进行rowSpan重新计算；
      const filterFn = (v: any) => skuId !== v.skuId
      const _skuList = skuList.filter(filterFn)
      const _index = _skuList.findIndex(v => v.itemId === itemId)
      const _rowSpan = rowSpan > 0 ? rowSpan : _skuList[_index].rowSpan
      _skuList[_index].rowSpan = _rowSpan - 1
      _skuList[_index].selectedCate = itemCate[itemId] ? itemCate[itemId].categoryIds : null

      setSkuList(_skuList)
    }
  }
  // 批量删除sku
  const batchDeleteSku = skuIds => {
    const filterFn = (v: any) => !skuIds.includes(v.skuId)
    setSkuList(skuList.filter(filterFn))
  }
  // 页面返回按钮
  const handleBack = () => {
    // 铺货完成状态下返回，直接刷新页面
    if (doneFlag) {
      reload()
    } else {
      Modal.confirm({
        title: '操作确认',
        content: '页面即将刷新，是否确认？',
        onOk: close => {
          reload()
          close()
        }
      })
    }
  }
  // 刷新页面
  const reload = () => {
    setLoading(true)
    window.location.reload()
  }
  // 选择商品
  const handleSelectItem = () => {
    setShowModal(true)
  }
  // 商品选择确认
  const getSelectedSku = (e: any) => {
    const newSkuMap = new Map()
    e.forEach((sku: any) => {
      newSkuMap.set(sku.skuId, 1)
    })
    const _oldList = skuList.filter((sku: any) => !newSkuMap.get(sku.skuId))
    setSkuList(_oldList.concat(e))
    closeModal()
  }

  const closeModal = () => {
    setShowModal(false)
  }
  // 渠道发生变化
  const formChange = (e: any) => {
    const channelId = e.channelId
    setSkuList([])
    setDoneFlag(false)
    setLoading(true)
    try {
      channelService.queryChannelType({ channelId }).then(
        res => {
          const channelType = res
          setChannelInfo({ channelId, type: channelType })
          if (channelType && channelType !== ChannelType.JD) {
            channelService.queryCategory({ channelId, parentId: 0 }).then(
              cateRes => {
                const channelCateRes = cateRes
                let list = channelCateRes || []
                list = list.map((cate: any) => {
                  const { cid, name, parent } = cate
                  return { value: cid, label: name, isLeaf: !parent }
                })
                setDefaultOptions(list)
                setLoading(false)
              },
              () => {
                setLoading(false)
                message.error('获取渠道一级类目失败')
              }
            )
          } else {
            getColumns()
            setLoading(false)
          }
        },
        () => {
          setLoading(false)
          setSkuList([])
          message.error('获取渠道类型失败')
        }
      )
    } catch (e) {
      setLoading(false)
    }
  }
  const searchSchema = useMemo(
    () => (
      <SharkRForm
        className="shark-form-inline"
        labelCol={{ span: 2 }}
        ref={formRefs}
        schema={schema}
        labelWrap
        wrapperCol={{ flex: 1 }}
        onValuesChange={formChange}
      />
    ),
    []
  )
  const getColumns = () => {
    let columnsList: any[] = []
    if (channelInfo.type === ChannelType.JD) {
      columnsList = columns.filter(
        (col: any) => ![ColKey.channelCate, ColKey.keyAttr].includes(col.title)
      )
    } else if (channelInfo.type === ChannelType.Ali) {
      columnsList = columns.filter((col: any) => col.title !== ColKey.keyAttr)
    } else {
      columnsList = columns
    }
    return columnsList
  }

  return (
    <>
      <div className="sharkr-section-header">
        <span className="sharkr-section-header-sub-title">商品管理&gt;</span>
        <span className="sharkr-section-header-sub-title">快速铺货</span>
      </div>
      <section className="sharkr-section">
        <div className="sharkr-section-header">
          <span className="sharkr-section-header-title">快速铺货</span>
        </div>
        <div className="sharkr-section-content">
          <Spin spinning={loading}>
            <div className="position-relation">
              {searchSchema}
              <Space className="position-absolute form-btn">
                <Button
                  type="primary"
                  disabled={!skuList.length || doneFlag}
                  onClick={handleSubmit}>
                  提交铺货
                </Button>
                <Button type="default" disabled={!channelInfo.channelId} onClick={handleBack}>
                  返回
                </Button>
              </Space>
            </div>
            {/*  TODO
             */}
            <Button
              type="primary"
              disabled={!channelInfo.channelId || doneFlag}
              onClick={handleSelectItem}>
              选择商品
            </Button>
            <Table
              className="sharkr-table margin-t-4x"
              columns={getColumns()}
              dataSource={skuList}
              rowKey="skuId"
              // loading={loading}
              pagination={false}
            />
            <SelectSkuModal
              channelId={channelInfo.channelId}
              visible={showModal}
              handleCancel={closeModal}
              handleOk={getSelectedSku}
            />
          </Spin>
        </div>
      </section>
    </>
  )
}
