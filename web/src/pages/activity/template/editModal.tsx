import React, { useRef, useEffect, useState } from 'react';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { Modal, Typography, Descriptions, Button, message } from 'antd';
// import { SharkRForm } from '@sharkr/form';

import { IEditlModal } from '../../../interfaces'
import { FormatTools } from '../../../utils'


import { TemplateForm } from './templateForm'

// 使用shark通用ListHook
export const EditModal: React.FC<IEditlModal> = (props: IEditlModal) => {



  /**
  * 模板表单浮层
* @param record 
* @returns 
*/
  const createTemplateEditModal = () => {
    const { onCancel, onOk, handleChangePerson, visible, loading, formRef, templateInfo } = props

    if (!visible) {
      return null
    }

    const okBtn = (<Button onClick={onOk} type="primary" loading={loading} key="ok">确认</Button>)
    const cancelBtn = (<Button onClick={onCancel} key="cancel">取消</Button>)

    const btnDOM = [cancelBtn, okBtn]

    return (
      <Modal
        title={`模板信息-${templateInfo?.id ? '编辑' : '新建'}`}
        style={{
          top: '10px'
        }}
        width={600}
        visible={visible}
        onCancel={onCancel}

        footer={btnDOM}
      >
        <TemplateForm
          formRef={formRef}
          formValue={templateInfo}
          onChangePerson={handleChangePerson}
        />
      </Modal>
    )
  }





  return (
    <>
      {createTemplateEditModal()}
    </>
  )

}; 
