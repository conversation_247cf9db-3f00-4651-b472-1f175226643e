import React, { useRef, useEffect, useState } from 'react';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { Form, Button, Tabs, Tooltip, Table, Tag, Divider, message, Modal, Image, Space, Typography, Descriptions } from 'antd';
import { Pagination, PlainObject } from '@shark/core';
import { UmcAuth } from '@eagler/authorizion';
import moment from 'moment';
import { activeService, getUserInfo } from '../../../services'
import { FormatTools } from '../../../utils'

import { DetailModal } from './detailModal'
import { EditModal } from './editModal'


const { TabPane } = Tabs;

const { Text } = Typography;


// 使用shark通用ListHook
export const TemplateList: React.FC<{}> = (props: {}) => {
  const formRef = useRef<any>(null);


  /**
   * 当前模板列表
   */
  const [list, setList] = useState<any[]>([])
  const [userInfo] = useState<any>(getUserInfo)

  /**
   * 当前页码
   */
  const [pagination, setPagination] = useState<any>({
    pageIndex: 1,
    pageSize: 10,
  })

  /**
   * 模板编辑浮层信息
   */
  const [templateInfo, setTemplateInfo] = useState<any>({})

  const [templateEditVisible, setEditVisible] = useState<any>(false)
  const [templateInfoVisible, setInfoVisible] = useState<any>(false)

  const [templateModalLoading, setTemplateModalLoading] = useState<any>(false)


  useEffect(() => {
    init()
  }, [])

  const init = () => {
    refreshList() // 刷新列表
  }

  /**
   *  获取列表
   */
  const getList = async (param: any) => {
    const res: any = await activeService.getTemplateList(param)
    const { results, pagination: _pagination } = res.data;
    setList(results)
    setPagination({
      ...pagination,
      ..._pagination,
    })

    // setTemplateInfo(results[0])

    // setInfoVisible(true)
  }


  /**
   * 更新模板状态
   * @param param 
   */
  const updateTemplateStatus = async (param: any) => {
    try {
      const res: any = await activeService.updateStatus(param)
      refreshList()
    } catch (error) {
    }
  }

  const refreshList = async () => {
    const { pageIndex, pageSize } = pagination
    getList({ pageIndex, pageSize })
  }

  const handleChangeTable = (current: number) => {
    const { pageSize } = pagination
    getList({ pageIndex: current, pageSize })

  }

  const getFormValues = async () => {

    try {
      const values = await formRef.current.validateFields()

      const result = {
        ...templateInfo,
        ...values,
        thumb: values?.thumb[0]?.url || values?.thumb,
        // responseUid: values.response.uid,
        // responseName: values.response.userName,
        // productUid: values.product.uid,
        // productName: values.product.userName,
      }

      delete result.response
      delete result.product

      console.log(result)
      return result
    } catch (error) {
      const _error: any = error
      const msg = _error?.errorFields[0]?.errors[0] || ''
      if (msg) {
        message.error(msg)
      }
    }
  }

  /**
    * 确认表单内容
    */
  const handleFormSubmit = async () => {

    try {

      const result: any = await getFormValues()
      if (result) {
        setTemplateModalLoading(true)
        if (result.id) {

          const res: any = await activeService.saveTemplateInfo(result);
          message.success('模板信息更新成功')
        } else {
          const res: any = await activeService.createTemplateInfo(result);
          message.success('模板创建更新成功')
        }

        setTemplateModalLoading(false)
        setEditVisible(false)
        refreshList()
      }

    } catch (error) {
      setTemplateModalLoading(false)
    }
  }


  /**
   * 变更对应人员信息
   * @param param 
   */
  const handleChangePerson = async (param: any) => {
    const { data, name } = param;

    const _templateInfo = {
      ...templateInfo,
      ...{ [`${name}Uid`]: data.uid },
      ...{ [`${name}Name`]: data.userName },
    }
    setTemplateInfo(_templateInfo)

  }

  const createTable = () => {
    const columns: any = [
      {
        title: '模板ID',
        dataIndex: 'id',
        key: 'id',
        width: 90,
        fixed: 'left',
      },
      {
        title: '模板名称',
        dataIndex: 'name',
        key: 'name',
        width: 200,
        fixed: 'left',
      },
      {
        title: '预览图',
        dataIndex: 'thumb',
        key: 'thumb',
        fixed: 'left',

        render: (v: string) => {
          return (
            <Image
              width={100}
              src={`${v}?imageView&quality=80&thumbnail=265x265`}
              preview={{
                src: v
              }}
            />
          )
        }
      },
      {
        title: '标签',
        dataIndex: 'tags',
        key: 'tags',
        width: 200,
        render: (v: string[]) => {
          return FormatTools.Tags(v)
        }
      },
      {
        title: '状态',
        key: 'status',
        dataIndex: 'status',
        render: (status: number, record: any) => {
          if (status === 0) {
            // 未开始
            return <Tag color="red">下架</Tag>
          } else if (status === 1) {
            // 生效中
            return <Tag color="#108ee9">上架中</Tag>
          } else {
            return '-'
          }
        }
      },
      {
        title: (
          <Space>
            <span>时间</span>
            <Tooltip
              title={'更新时间/创建时间'}
            >
              <QuestionCircleOutlined translate={null} />
            </Tooltip>
          </Space>
        ),
        key: 'time',
        dataIndex: 'time',
        width: 250,
        render: (_: number, record: any) => {
          return (
            <Space direction="vertical">
              <Text>{FormatTools.Time(record.updateTime)}</Text>
              <Text type="secondary">{FormatTools.Time(record.createTime)}</Text>
            </Space>

          )


          // <>
          //   <div>
          //     {`更新: ${moment(record.updateTime).format('YYYY-MM-DD HH:mm:ss').valueOf()}`}
          //   </div>
          //   <div>
          //     {`创建: ${moment(record.createTime).format('YYYY-MM-DD HH:mm:ss').valueOf()}`}
          //   </div>
          // </>

          // <>

          // </>
        }
      },
      {
        title: (
          <Space>
            <span>操作人</span>
            <Tooltip
              title={'更新人/创建人'}
            >
              <QuestionCircleOutlined translate={null} />
            </Tooltip>
          </Space>
        ),
        // key:'person',
        dataIndex: 'person',
        width: 300,
        render: (_: any, record: any) => {
          return (
            <Space direction="horizontal">
              {/* <Text>{`开发:  ${record?.responseName ?? '-'} `} <Divider type="vertical" /> {` 产品:  ${record?.productName ?? '-'}`}</Text> */}
              <Text>{record?.updateName ?? '-'} </Text> /<Text type="secondary"> {record?.createName ?? '-'}</Text>
            </Space>
          )
        }
      },
      {
        title: '操作',
        key: 'action',
        fixed: 'right',
        render: (text: any, record: PlainObject) => (
          <span className="sharkr-table-actions">
            {createActionButton(record)}
          </span>
        )
      }
    ];

    return (
      <Table
        className="sharkr-table"
        rowKey="id"
        columns={columns}
        // scroll={{x:2000}}
        pagination={{
          current: pagination?.pageIndex,
          pageSize: pagination?.pageSize,
          total: pagination?.totalCount || '',
          onChange: handleChangeTable
        }}
        dataSource={list}
      />
    )
  }

  /**
   * 列表操作按钮区
   * @param record 
   * @returns 
   */
  const createActionButton = (record: any) => {
    const { email } = userInfo;

    const rightList = [
      record.responseUid,
      record.productUid,
      record.createUid,
      record.updateUid
    ]

    let turnOffBtn = <Button type='link' key='turnOff' onClick={() => { updateTemplateStatus({ id: record.id, status: 0 }) }} danger>下架</Button>
    let turnOnBtn = <Button type='link' key='turnOff' onClick={() => { updateTemplateStatus({ id: record.id, status: 1 }) }}>上架</Button>

    const editBtn = (
      <Button
        key='edit'
        type='link'
        onClick={() => {
          setTemplateInfo(record)
          setEditVisible(true)
        }}>编辑</Button>
    )
    const detailBtn = <Button type='link' key={'detail'} onClick={() => { handleTemplateDetail(record) }}>详情</Button>
    const DOM = [editBtn, detailBtn];

    if (rightList.includes(email)) {
      // 当前操作人为对应负责人
    } else {
      // 当前操作人不是对应负责人 
      turnOffBtn = <UmcAuth condition="1636131000015" key='umc-turnOff'>{turnOffBtn}</UmcAuth>
      turnOnBtn = <UmcAuth condition="1636131000015" key='umc-turnOn'>{turnOnBtn}</UmcAuth>
    }


    if (record.status === 1) {
      DOM.push(turnOffBtn)
    } else {
      DOM.push(turnOnBtn)
    }


    return DOM


  }






  /**
   * 查看详情
   * @param record 
   */
  const handleTemplateDetail = (record: any) => {
    setTemplateInfo(record);
    setInfoVisible(true)
  }


  return (
    <>
      <section className="sharkr-section">
        <div className="sharkr-section-header">
          <span className="sharkr-section-header-sub-title">营销工具&gt;</span>
          <span className="sharkr-section-header-sub-title">营销活动管理</span>
        </div>
        <div className="sharkr-section-content">


          <Tabs
            defaultActiveKey="1"
            tabBarExtraContent={(
              <Button
                // ghost
                type="primary"
                onClick={() => {
                  setTemplateInfo({})
                  setEditVisible(true)

                }}
              >新建模板</Button>)}
          >
            <TabPane tab="所有模板" key="1">
              {createTable()}
            </TabPane>
          </Tabs>


          <EditModal
            visible={templateEditVisible}
            loading={templateModalLoading}
            templateInfo={templateInfo}
            onCancel={() => { setEditVisible(false) }}
            onOk={handleFormSubmit}
            handleChangePerson={handleChangePerson}
            formRef={formRef}
          />


          <DetailModal
            visible={templateInfoVisible}
            templateInfo={templateInfo}
            onCancel={() => { setInfoVisible(false); setTemplateInfo({}) }}
          />

        </div>

      </section>
    </>
  );
};
