import React, { useRef, useEffect, useState } from 'react';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { Form, Button, Input, Select, DatePicker, Row, Col, Table, Tag, Tooltip, message, Modal } from 'antd';
// import { SharkRForm } from '@sharkr/form';

import { createFormItem } from '../../../utils'
import { ITemplateForm } from '../../../interfaces'

const { confirm } = Modal;



// 使用shark通用ListHook
export const TemplateForm: React.FC<ITemplateForm> = (props: ITemplateForm) => {

  const { formRef, formValue:defaultValue , onChangePerson} = props;

  console.log(defaultValue)
  const [formValue, setFormValue] = useState<any>({});



  useEffect(() => {
    init()
  }, [])

  const init = () => {

  }

  const onValuesChange = (changedValue, allValues) => {
    console.log(changedValue, allValues);
  };


  /**
   * 模板名称
   * @returns 
   */
  const createName = () => {
    const DOM = createFormItem.Input({
      name: 'name',
      label: '模板名称',
      opations: {
        initialValue: defaultValue.name,
        rules: [{
          required: false,
          whitespace: true,
          message: '请填模板名称'
        }]
      }
    })
    return DOM;
  }

  /**
  * 模板名称
  * @returns 
  */
  const createCode = () => {
    const DOM = createFormItem.Input({
      name: 'code',
      label: '模板code',
      opations: {
        initialValue: defaultValue.code,
        rules: [{
          required: false,
          whitespace: true,
          message: '请填模板code',
        }]
      }
    })
    return DOM;
  }

  /**
   * 模板封面
   * @returns 
   */
  const createImg = () => {
    console.log( defaultValue.thumb)
    const DOM = createFormItem.Image({
      name: 'thumb',
      label: '封面',
      opations: {
        initialValue: defaultValue.thumb,
        rules: [{
          required: false,
          // whitespace: true,
          message: '请上传封面',
        }]
      }
    })
    return DOM;
  }

  /**
   * 模板描述
   */
  const createDesc = () => {
    const DOM = createFormItem.TextArea({
      name: 'description',
      label: '模板描述',
      opations: {
        initialValue: defaultValue.description,
        rules: [{
          required: false,
          whitespace: true,
          message: '请填写模板描述',
        }]
      }
    })
    return DOM
  }

  /**
   * 模板描述
   */
  const createGuide = () => {
    const DOM = createFormItem.TextArea({
      name: 'guide',
      label: '使用说明',
      opations: {
        initialValue: defaultValue.guide,
      }
    })
    return DOM
  }

  /**
   * 版本
   * @returns 
   */
  const createVersion = () => {
    const DOM = createFormItem.Input({
      name: 'version',
      label: '版本',
      opations: {
        initialValue: defaultValue.version,
      }
    })
    return DOM
  }


  /**
   * git仓库
   * @returns 
   */
  const createGit = () => {
    const DOM = createFormItem.Input({
      name: 'git',
      label: 'git',
      opations: {
        initialValue: defaultValue.git,

        rules: [{
          required: true,
          whitespace: true,
          message: '请填写git仓库地址',
        }]
      }
    })
    return DOM
  }


  /**
   * 模块标签
   * @returns 
   */
  const createtags = () => {
    const DOM = createFormItem.Select({
      name: 'tags',
      label: '标签',
      mode: 'tags',
      opations: {
        initialValue: defaultValue.tags,
      },
      optionList: [],
    })
    return DOM
  }


  /**
   * 人员选择器
   * @param key 
   * @returns 
   */
  const createPerson = (param) => {
    const { key, label } = param;
    const DOM = createFormItem.Person({
      name: key,
      label,
      // multiple: true,
      opations: {
        initialValue: defaultValue[`${key}Uid`],
        rules: [{
          required: true, 
          message: '请填责任人',
        }]
      },
      onChange:onChangePerson
    })
    return DOM
  }


  /**
   * 创建form表单
   */
  const createForm = () => {

    return (
      <Form
        ref={formRef}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
      >
        {createName()}
        {createCode()}
        {createImg()}
        {createGit()}
        {createtags()}
        {/* {createVersion()}  */}



        {createPerson({ key: 'response', label: '开发负责人' })}
        {createPerson({ key: 'product', label: '产品负责人' })}

        {createDesc()}
        {createGuide()}
      </Form>
    )


  }


  return (
    <>
      {createForm()}
    </>
  );
};
