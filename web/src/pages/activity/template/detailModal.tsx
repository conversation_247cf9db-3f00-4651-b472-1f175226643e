import React, { useRef, useEffect, useState } from 'react';
import { QuestionCircleOutlined, GitlabOutlined } from '@ant-design/icons';
import { Modal, Typography, Descriptions, Image, Tag, Space, Tooltip } from 'antd';
// import { SharkRForm } from '@sharkr/form';

import { IDetailModal } from '../../../interfaces'
import { FormatTools } from '../../../utils'
import './detailModal.scss'

const { confirm } = Modal;

const { Text, Link } = Typography;

// 使用shark通用ListHook
export const DetailModal: React.FC<IDetailModal> = (props: IDetailModal) => {




  const createTemplateDetailModal = () => {

    const { visible, templateInfo, onCancel } = props;
    if (!visible && templateInfo.id) {
      return null
    }



    const tags = <Tag color={templateInfo.status ? '#108ee9' : 'red'}>{templateInfo.status ? '上架中' : '已下架'}</Tag>

    return (
      <Modal
        title={(
          <Space>
            <Text>{`模板信息`}</Text>
            {tags}
          </Space>
        )}
        style={{
          top: '10px'
        }}
        width={900}
        onCancel={onCancel}
        // onCancel={()=>{onCancel()}}
        visible={visible}
      >
        <Descriptions
          size={'small'}
          bordered
          column={3}
        >
          <Descriptions.Item label="模板名称">{templateInfo.name}</Descriptions.Item>
          <Descriptions.Item label="ID">{templateInfo.id}</Descriptions.Item>
          <Descriptions.Item label="GIT">
            <Tooltip title={templateInfo.git}>
              <Space> 
                <GitlabOutlined translate={undefined} className={'gitlab'} />
                <a href={templateInfo.git} target="_blank"> 
                  <Text>{templateInfo?.git?.split('/')?.pop() || templateInfo?.git || '-'}</Text>
                </a>
              </Space>
            </Tooltip>
          </Descriptions.Item>
          {/* <Descriptions.Item label="版本">{templateInfo.version}</Descriptions.Item> */}
          {/* <Descriptions.Item label="Amount">$80.00</Descriptions.Item> */}

          <Descriptions.Item label="缩略图">
            <Image
              width={100}
              src={`${templateInfo.thumb}?imageView&quality=80&thumbnail=265x265`}
              preview={{
                src: templateInfo.thumb
              }}
            />
          </Descriptions.Item>
          <Descriptions.Item label="CODE"> <Text> {templateInfo.code}</Text> </Descriptions.Item>

          <Descriptions.Item label="操作信息">
            <div> <Text>{'[更新] '} {templateInfo.updateName ?? '-'}{FormatTools.Time({ value: templateInfo.updateTime })}</Text></div>
            <div> <Text type="secondary">{'[创建] '} {templateInfo.createName ?? '-'}{FormatTools.Time({ value: templateInfo.createTime })}</Text></div>
          </Descriptions.Item>
          
          <Descriptions.Item label="产品负责人" >{templateInfo.productName ?? '-'}</Descriptions.Item>
          <Descriptions.Item label="开发负责人" >{templateInfo.responseName ?? '-'}</Descriptions.Item>
          <Descriptions.Item label="标签">{FormatTools.Tags(templateInfo.tags)}</Descriptions.Item>
          <Descriptions.Item label="模板描述" span={3}>{templateInfo.description}</Descriptions.Item>
          <Descriptions.Item label="使用说明" span={3}>{templateInfo.guide}</Descriptions.Item>

        </Descriptions>
      </Modal>
    )


  }


  return (
    <>
      {createTemplateDetailModal()}
    </>
  );
};
