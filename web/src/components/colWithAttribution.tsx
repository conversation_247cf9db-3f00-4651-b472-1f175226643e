/*
 * @Description:
 * @Author: chenxiaopan <EMAIL>
 * @Date: 2022-11-25 11:29:21
 * 产品关键属性配置
 */
import React, { useState, useEffect, useRef } from 'react'
import { SharkRForm, ISchema } from '@sharkr/form'
import { InfoCircleOutlined } from '@ant-design/icons'
import { FormInstance } from 'antd/lib/form'

import { IColAttriProps } from '../interfaces'
import { FileRuleType, FileRuleValue, SymbolType, OperatorType } from '../consts'

export const ColWithAttribution: React.FC<IColAttriProps> = props => {
  const { disabled, cateInfo, info } = props
  const [schema, setSchema] = useState<any[]>([])
  const formRef = useRef<any>()
  useEffect(() => {
    if (cateInfo && cateInfo.isLeaf) {
      const list: any = cateInfo.fieldList ? getPublicToSchema(cateInfo?.fieldList) : []
      setSchema(list)
    }
  }, [cateInfo?.isLeaf])

  const getPublicToSchema = (list: any[]) => {
    const initPublic: any = [
      {
        key: 'itemId',
        type: 'Input',
        label: '商品ID',
        status: 'hidden',
        value: String(info.itemId)
      }
    ]
    list.forEach((item: any) => {
      if (item.name !== '产品图片') {
        const formateItem = {
          key: item.id,
          type: getSchemaType(item.type),
          label: item.name,
          ...getOtherType(item)
        }
        initPublic.push(formateItem)
      }
    })
    return initPublic
  }
  const getSchemaType = (type: string) => {
    switch (type) {
      case 'INPUT':
        return 'Input'
      case 'SINGLECHECK':
      case 'MULTICHECK':
        return 'Select'
      case 'MULTIINPUT':
        return 'Array'
      case 'MULTICOMPLEX':
        return 'Array'
      default:
        return 'Input'
    }
  }
  const getRuleInfo = (ruleList: any[]) => {
    let ruleInfos
    let tooltip
    let maxLength
    if (ruleList.length) {
      const requiredRule = ruleList.filter(
        rules => rules.name === FileRuleType.required && rules.value === FileRuleValue.true
      )
      const statusRule = ruleList.filter(
        rules => rules.name === FileRuleType.hidden && rules.value === FileRuleValue.true
      )
      const tipRule = ruleList.filter(rules => rules.name === FileRuleType.tip)
      const maxNumRule = ruleList.filter(rules => rules.name === FileRuleType.maxNum)

      let rules
      let listenInfo
      let ui
      if (requiredRule.length) {
        rules = [{ required: true, message: '请配置' }]
        ui = { required: true }
      } else {
        rules = []
        ui = null
      }
      if (statusRule.length) {
        const useRule = statusRule[0]
        const dependExpressList = useRule.dependGroup.dependExpressList
        const operator = useRule.dependGroup.operator
        const listenerMap = getListenerMap(dependExpressList, formRef)

        if (dependExpressList.length > 1) {
          const watchIds = Object.keys(listenerMap)

          listenInfo = {
            status: 'edit',
            listeners: [
              {
                watch: [...watchIds],
                condition: (file, form) => {
                  const newMap = getListenerMap(dependExpressList, form)
                  const flagList = Object.values(newMap)
                  let listenFlag = false
                  if (operator === OperatorType.and) {
                    listenFlag = flagList.some((flag: any) => {
                      return !flag.some(value => !value)
                    })
                  } else {
                    listenFlag = flagList.some((flag: any) => {
                      return flag.some(value => value)
                    })
                  }
                  return listenFlag
                },
                set: {
                  status: 'hidden'
                }
              }
            ]
          }
        } else {
          listenInfo = {
            status: 'edit',
            listeners: dependExpressList.map(rule => {
              const watchId = rule.fieldId
              return {
                watch: [watchId],
                condition: (field, form) => {
                  const flag = getSymbolResult(form.getValue(watchId), rule)
                  return flag
                },
                set: {
                  status: 'hidden'
                }
              }
            })
          }
        }
      } else {
        listenInfo = { status: 'edit' }
      }
      if (tipRule.length) {
        tooltip = { title: tipRule[0].value, icon: <InfoCircleOutlined /> }
      } else {
        tooltip = null
      }
      if (maxNumRule.length) {
        maxLength = maxNumRule[0].value
      } else {
        maxLength = null
      }
      if (!ui) {
        ui = {
          tooltip
        }
      } else {
        ui.tooltip = tooltip
      }
      ruleInfos = { rules, ui, maxLength, ...listenInfo }
      return ruleInfos
    } else {
      return
    }
  }

  const getListenerMap = (dependExpressList: any[], form: any) => {
    const watchMap = {}
    dependExpressList.forEach(rule => {
      const id = rule.fieldId
      const standValue = form.current ? form.current.getValue(id) : form.getValue(id)
      const flag = getSymbolResult(standValue, rule)
      const values = watchMap[id]

      if (values) {
        watchMap[id] = values.concat([flag])
      } else {
        watchMap[id] = [flag]
      }
    })

    return watchMap
  }
  const getSymbolResult = (standValue: any, rule: any) => {
    const value = rule.value
    if (
      [
        SymbolType.bigger,
        SymbolType.less,
        SymbolType.biggerAndEqual,
        SymbolType.lessAndEqual
      ].includes(rule.symbol)
    ) {
      return eval(`${standValue}${rule.symbol}${value}`)
    } else if ([SymbolType.equal, SymbolType.unEqual].includes(rule.symbol)) {
      if (rule.symbol === SymbolType.equal) {
        return standValue === value
      } else {
        return standValue !== value
      }
    } else if ([SymbolType.include, SymbolType.unInclude].includes(rule.symbol)) {
      if (rule.symbol === SymbolType.include) {
        return Boolean(standValue && standValue.includes(rule.value))
      } else {
        return standValue ? !standValue.includes(rule.value) : true
      }
    } else if (SymbolType.isNull) {
      return standValue === null
    } else if (SymbolType.isEmpty) {
      return standValue && !standValue.length
    } else {
      console.log('getSymbol未兼容清空', rule.symbol)
      return true
    }
  }
  const getOtherType = (item: any) => {
    let infos
    let baseRule
    const ruleInfos = getRuleInfo(item.rules)
    const maxLength = ruleInfos && ruleInfos.maxLength ? ruleInfos.maxLength : null
    if (ruleInfos) {
      baseRule = { ...ruleInfos, value: item.value?.value || null }
      delete baseRule.maxLength
    }

    if (item.type === 'MULTIINPUT') {
      infos = {
        props: {
          type: 'List',
          maxLength
        },
        children: [
          {
            key: '_code',
            type: 'Input',
            ui: {
              labelCol: { span: 0 }
            }
          }
        ],
        ...baseRule
      }
    } else if (item.type === 'MULTICHECK') {
      infos = {
        props: {
          mode: 'multiple'
        },
        options: item.options.map(info => {
          return { label: info.displayName, value: info.value }
        }),
        ...baseRule
      }
    } else if (item.type === 'SINGLECHECK') {
      infos = {
        options: item.options.map(info => {
          return { label: info.displayName, value: info.value }
        }),
        ...baseRule
      }
    } else if (item.type === 'MULTICOMPLEX') {
      infos = {
        props: {
          type: 'List',
          maxLength
        },
        children: getPublicToSchema(item.fieldList),
        ...baseRule
      }
    } else {
      infos = {
        ...baseRule
      }
    }
    console.log('infos', infos)

    return infos
  }
  return (
    <>
      {cateInfo?.isLeaf && (
        <SharkRForm
          ref={formRef}
          schema={schema}
          labelWrap
          status={disabled ? 'disabled' : 'edit'}
        />
      )}
    </>
  )
}
