/* eslint-disable prettier/prettier */
import React, { forwardRef, memo, useCallback, useEffect, useState } from 'react'
import { Upload as AntUpload, Button, Modal, message } from 'antd'
import { SharkrUpload } from '@eagler/business-components';
import { PlusOutlined } from '@ant-design/icons'
import { FileItem } from './interface'
import './uploadImg.scss'
import { uploadFile } from '../../services/uploadService'

export interface UploadImgProps {
  value?: FileItem[]
  imgSize?: number // 图片大小
  imgWh?: {
    width?: number
    height?: number
  },
  field: any,
  onChange?: (s: any) => void
  formatImg?: string // 图片格式
  name?: Boolean
  disabled?: Boolean
}

export interface AntdImgFileProps {
  fileUrl: string
  fileName: string
}
let completeList: any[] = []
let upQueue: any[] = []
export const UploadImg: React.FC<UploadImgProps> = memo(
  forwardRef((props: UploadImgProps, ref) => {
    const [files, setFileList] = useState<any>([])
    const [uploadingList, setUploadingList] = useState<any>([])
    const { value } = props
    console.info(props, 'props')
    const [previewVisible, setPreviewVisible] = useState(false)
    const [previewTitle, setPreviewTitle] = useState('')
    const [previewImage, setPreviewImage] = useState('')

    // 添加自定义校验逻辑
    const onValidate = useCallback(async (trigger, value) => {
      let valid = true;
      let message = '';
      if ((trigger === 'submit' || 'blur') && !value) {
        valid = false;
        message = '请上传图片';
      }
      const status = valid ? 'normal' : 'error';
      return { valid, message, status };
    }, []);

    useEffect(() => {
      // 注册自定义校验方法
      props?.field?.onValidate(onValidate);
      upQueue = []
      completeList = []
    }, [])
    useEffect(() => {
      if (value && value.length) {
        const list = value.map((v: AntdImgFileProps) => ({
          uid: v.fileUrl,
          name: v.fileName,
          status: 'down',
          url: v.fileUrl
        }))
        setFileList(list)
        completeList = list
      }
    }, [value])
    const handleChange = (e: any) => {
      const {
        file: { status, uid }
      } = e
      const { onChange } = props
      let newList = files
      if (status === 'removed') {
        newList = newList.filter((f: any) => f.uid !== uid)
        completeList = newList
        setFileList(newList)
        onChange && onChange(newList.map((f: any) => ({ fileUrl: f.url, fileName: f.name })))
      }
    }
    const handleUpload = (e: any) => {
      // const newLoadingList = uploadingList.concat([e.file])
      // setUploadingList(newLoadingList);
      if (!upQueue.length) {
        upQueue = upQueue.concat([e])
        upload(0)
      } else {
        upQueue = upQueue.concat([e])
      }
    }
    const upload = (index: number) => {
      const e = upQueue[index]
      uploadFile(e.file)
        .then(res => {
          const { code, data } = res.data
          if (code === 200 && data) {
            const fileUrl = data
            const newList = completeList.concat([
              {
                uid: fileUrl,
                name: e.file.name || '',
                status: 'down',
                url: fileUrl
              }
            ])
            completeList = newList
            // setUploadingList(upQueue.slice(index + 1).map(v => v.file));
            const { onChange } = props
            onChange && onChange(newList.map((f: any) => ({ fileUrl: f.url, fileName: f.name })))
            nextUp(index + 1)
          } else {
            nextUp(index + 1)
            // setUploadingList(upQueue.slice(index + 1).map(v => v.file));
          }
        })
        .catch(() => {
          // setUploadingList(upQueue.slice(index + 1).map(v => v.file));
          nextUp(index + 1)
        })
    }
    const nextUp = (index: number) => {
      if (upQueue[index]) {
        upload(index)
      } else {
        upQueue = []
      }
    }
    const beforeUpload = (file: any, fileList: any) => {
      upQueue = []
      completeList = []
      const isSize = props.imgSize && props.imgSize > file.size / 1024
      if (props.imgSize && !isSize) {
        message.error(`${file.name}图片大小超出限制`);
        return false
      }
      if (props.imgWh) {
        const isWh = checkImageWH(file)
        return isWh
      }
      return true
      // return isSize && isWh;
    }

    const checkImageWH = (file: any) => {
      return new Promise((resolve, reject) => {
        const width = props.imgWh && props.imgWh.width
        const height = props.imgWh && props.imgWh.height
        const _URL = window.URL || window.webkitURL
        const img = new Image()
        img.onload = () => {
          let valid = false
          if (props.imgWh?.width && props.imgWh?.height) {
            valid = img.width === width && img.height === height
          } else if (props.imgWh?.width) {
            valid = img.width === width
          } else {
            valid = img.height === height
          }
          // eslint-disable-next-line prefer-promise-reject-errors
          valid ? resolve(img) : reject()
        }
        img.src = _URL.createObjectURL(file)
      }).then(
        () => {
          return file
        },
        () => {
          message.error(`${file.name}图片尺寸不符合要求，请修改后从新上传!`)
          // eslint-disable-next-line prefer-promise-reject-errors
          return Promise.reject()
        }
      )
    }

    const handlePreview = async (file: any) => {
      setPreviewTitle(file.name || file.url.substring(file.url.lastIndexOf('/') + 1))
      setPreviewImage(file.url)
      setPreviewVisible(true)
    }
    const upLoadBtn = (
      <div>
        <PlusOutlined translate={undefined} />
        <div className="ant-upload-text">{props.name ? '上传' : 'Upload'}</div>
      </div>
    )
    return (
      <div className="upload-wrap">
        <AntUpload
          accept={props.formatImg ? props.formatImg : '.jpg, .jpeg, .png'}
          beforeUpload={beforeUpload}
          customRequest={handleUpload}
          disabled={props.disabled ? true : false}
          fileList={files}
          listType="picture-card"
          // max={1}
          // serviceCode="'dsp-web'"
          onChange={handleChange}
          onPreview={handlePreview}>
          {files.length > 0 ? null : upLoadBtn}
        </AntUpload>
        <Modal
          footer={null}
          title={previewTitle}
          visible={previewVisible}
          onCancel={() => setPreviewVisible(false)}>
          <img alt="example" src={previewImage} style={{ width: '100%' }} />
        </Modal>
        {/* {uploadingList.map((v: any) => (
                    <div key={v.uid} className="up-loading-btn">
                        <Button loading>{v.name}</Button>
                    </div>
                ))} */}
      </div>
    )
  })
)
