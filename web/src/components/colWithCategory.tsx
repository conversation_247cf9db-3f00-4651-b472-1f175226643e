/*
 * @Author: chenxiaopan <EMAIL>
 * @Date: 2022-11-16 20:48:18
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-12-12 14:22:45
 * @FilePath: /distribution-operation-web/web/src/components/colWithCategory.tsx
 * @Description: 类目级联选择
 */
import React, { useState, useEffect } from 'react'
import { Button, Cascader } from 'antd'

import { IColProps } from '../interfaces'
import { channelService } from '../services'
import './index.scss'

export const ColWithCategory: React.FC<IColProps> = (props: IColProps) => {
  const { handleChange, disabled, channelId, defaultOptions, info } = props
  console.log('ColWithCategory', info)

  const [options, setOptions] = useState(defaultOptions)

  const onChange = (value: string[], selectedOptions: any[]) => {
    console.log(value, selectedOptions)
    const selectedLength = selectedOptions?.length || 0
    const isLeaf = selectedLength > 0 ? selectedOptions[selectedLength - 1]?.isLeaf : false
    handleChange({ itemId: info.itemId, value: value, isLeaf: isLeaf })
    !isLeaf && loadData(selectedOptions)
  }
  const filter = (inputValue: string, path: any[]) =>
    path.some(option => (option.label as string).indexOf(inputValue) > -1)
  const loadData = (selectedOptions: any[]) => {
    const targetOption = selectedOptions[selectedOptions.length - 1]
    console.log('selectedOptions', targetOption)
    targetOption.loading = true
    channelService.queryCategory({ channelId, parentId: targetOption.value }).then(res => {
      console.log('queryCategory', res)
      let list = res || []
      list = list.map((cate: any) => {
        return { value: cate.cid, label: cate.name, isLeaf: !cate.parent }
      })
      targetOption.children = list
      targetOption.loading = false
      setOptions([...options])
    })
  }
  const dropdownRender = (menus: React.ReactNode) => <div className="popupClassName">{menus}</div>

  return (
    <>
      <Cascader
        changeOnSelect
        defaultValue={info.selectedCate}
        disabled={disabled}
        dropdownRender={dropdownRender}
        loadData={loadData}
        options={options}
        showSearch={{ filter }}
        onChange={onChange}
        // onSearch={onChange}
      />
    </>
  )
}
