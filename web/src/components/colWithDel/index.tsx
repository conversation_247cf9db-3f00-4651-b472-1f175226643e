/*
 * @Author: chen<PERSON>opan <EMAIL>
 * @Date: 2022-09-29 14:14:14
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-11-30 10:32:52
 * @FilePath: /distribution-operation-web/web/src/components/colWithDel/index.tsx
 * @Description: 带删除功能的列
 */
import React from 'react'
import { Button } from 'antd'
import { IDelColProps } from '../../interfaces'
import { ItemType } from '../../consts'

export const ColWithDel: React.FC<IDelColProps> = (props: IDelColProps) => {
  const { info, itemType, handleDel, delDisabled } = props
  const name = itemType === ItemType.item ? info.itemName : info.displayString
  const id = itemType === ItemType.item ? info.itemId : info.skuId

  const btnText = itemType === ItemType.item ? '删除商品' : '删除SKU'
  return (
    <>
      {name}
      <br />
      {id}
      <Button
        type="link"
        className="ant-btn-dangerous padding-h-0"
        disabled={delDisabled}
        onClick={() => {
          handleDel(info)
        }}>
        {btnText}
      </Button>
    </>
  )
}
