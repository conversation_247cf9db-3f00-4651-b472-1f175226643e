/* eslint-disable */
import React, { forwardRef, memo, useEffect, useState } from 'react';
import { Upload as AntUpload, Button, Modal, message } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { uploadFile, downloadFile } from '../../services/uploadService';
import { FileItem } from './interface';
import './upload.scss';

export interface UploadProps {
    defaultText?: string;
    value?: FileItem[];
    imgSize?: number; // 图片大小
    imgWh?: {
        width?: number,
        height?: number
    }
    onChange?: (s: any) => void;
    formatImg?: string // 图片格式
    maxFiles?: number
}

export interface AntdFileProps {
    fileUrl: string;
    fileName: string;
}
let completeList: any[] = [];
let upQueue: any[] = [];
export const UploadTmp: React.FC<UploadProps> = memo(
    forwardRef((props: UploadProps, ref) => {
        const [files, setFileList] = useState<any>([]);
        const { value } = props;
        useEffect(() => {
            upQueue = [];
            completeList = [];
        }, []);
        useEffect(() => {
            if (value && value.length) {
                const list = value.map((v: AntdFileProps) => ({
                    uid: v.fileUrl,
                    name: v.fileName,
                    status: 'down',
                    url: v.fileUrl,
                }));
                setFileList(list);
                completeList = list;
            }
        }, [value]);
        const handleChange = (e: any) => {
            const {
                file: { status, uid },
            } = e;
            const { onChange } = props;
            let newList = files;
            if (props.maxFiles) {
                if (status === 'uploading') {
                    onChange &&
                        onChange(newList.map((f: any) => ({ fileUrl: f.url, fileName: f.name })));
                    return
                }
                if (status === 'removed') {
                    newList = newList.filter((f: any) => f.uid !== uid);
                    completeList = newList;
                    setFileList(newList);
                    onChange &&
                        onChange(newList.map((f: any) => ({ fileUrl: f.url, fileName: f.name })));
                }
            } else {
                if (status === 'removed') {
                    newList = newList.filter((f: any) => f.uid !== uid);
                    completeList = newList;
                    setFileList(newList);
                    onChange &&
                        onChange(newList.map((f: any) => ({ fileUrl: f.url, fileName: f.name })));
                }
            }

        };
        const handleUpload = (e: any) => {
            if (!upQueue.length) {
                upQueue = upQueue.concat([e])
                upload(0)
            } else {
                upQueue = upQueue.concat([e])
            }
        };
        const upload = (index: number) => {
            const e = upQueue[index]
            uploadFile(e.file)
                .then(res => {
                    const { code, data } = res.data;
                    if (code === 200 && data) {
                        const fileUrl = data;
                        const newList = completeList.concat([
                            {
                                uid: fileUrl,
                                name: e.file.name || '',
                                status: 'down',
                                url: fileUrl + `?t=${new Date().getTime()}`
                            },
                        ]);
                        completeList = newList;
                        const { onChange } = props;
                        onChange &&
                            onChange(
                                newList.map((f: any) => ({ fileUrl: f.url, fileName: f.name }))
                            );
                        nextUp(index + 1)
                    } else {
                        nextUp(index + 1)
                    }
                })
                .catch(() => {
                    nextUp(index + 1)
                });
        }
        const nextUp = (index: number) => {
            if (upQueue[index]) {
                upload(index)
            } else {
                upQueue = []
            }
        }
        const beforeUpload = (file: any, fileList: any) => {
            upQueue = [];
            // completeList = [];
            const isSize = props.imgSize && props.imgSize > (file.size / 1024)
            if (props.imgSize && !isSize) {
                message.error(`${file.name}图片大小超出限制`);
                return false;
            }
            if (props.imgWh) {
                const isWh = checkImageWH(file)
                return isWh
            }
            return true
        }


        const checkImageWH = (file: any) => {
            return new Promise((resolve, reject) => {
                const width = props.imgWh && props.imgWh.width;
                const height = props.imgWh && props.imgWh.height;
                const _URL = window.URL || window.webkitURL;
                const img = new Image();
                img.onload = () => {
                    let valid = false
                    if (props.imgWh?.width && props.imgWh?.height) {
                        valid = img.width === width && img.height === height;
                    } else if (props.imgWh?.width) {
                        valid = img.width === width
                    } else {
                        valid = img.height === height
                    }
                    valid ? resolve(img) : reject();
                }
                img.src = _URL.createObjectURL(file);
            }).then(
                () => {
                    return file;
                },
                () => {
                    message.error(`${file.name} + "图片尺寸不符合要求，请修改后从新上传!`);
                    return Promise.reject();
                }
            );
        }

        return (
            <div className="upload-wrap">
                <AntUpload
                    multiple={true}
                    accept={props.formatImg ? props.formatImg : '.jpg, .jpeg, .png'}
                    listType="text"
                    fileList={files}
                    beforeUpload={beforeUpload}
                    onChange={handleChange}
                    customRequest={handleUpload}
                >
                    <Button type='primary'>上传图片</Button>
                    <div style={{ marginTop: 10 }}>单批次最多上传100张  单张图片大小不可超过3M</div>
                </AntUpload>

                {/* {uploadingList.map((v: any) => (
                    <div key={v.uid} className="up-loading-btn">
                        <Button loading>{v.name}</Button>
                    </div>
                ))} */}
            </div>
        );
    })
);