
## 概述
包含：静态资源管理，商品管理，商品主图美化，营销工具等模块


## 运行指南

### 1. 开发环境配置

#### 1.1 Host 配置

需要配置如下host：
127.0.0.1 local.yx.mail.netease.com
127.0.0.1 remote.yx.mail.netease.com


建议使用Host管理工具来进行操作（如：SwitchHosts）

- 本地mock开发 访问local.yx.mail.netease.com
- 联调模式 访问remote.yx.mail.netease.com
```

#### 1.2 VSCode

我们要求使用VSCode作为开发环境。请在VSCode中至少安装以下插件:

- ESLint

#### 1.3 Node 和 NPM

电脑需要正确安装node和npm，node版本为v8或以上，npm版本为v6或以上。

#### 1.4 使用ppnpm

[使用ppnpm](http://yxpdc.mail.netease.com/friday/training-doc/tutorial/page/base.html#%E4%BD%BF%E7%94%A8ppnpm)

### 2. 开发过程

#### 2.1 安装依赖

目前工程依赖了严选 C 端组件库和基础库，而这些npm包发布在npm私服，需要使用ppnpm进行安装

```bash
npm install 

```

#### 2.2 运行
```bash
npm run dev

```
执行上述命令后，浏览器会自动打开本地调试链接: `http://local.yx.mail.netease.com:9000/discalpel/index.html`

#### 2.3 编译打包
```bash
npm run build  # 本地环境打包
npm run build:test  # 测试环境打包
npm run build:online  # 线上环境打包

```

### 3. 部署

目前，工程已支持测试环境自动部署，仅需要把开发分支合并到对应的测试分支即可，测试分支与染色环境的对应关系如下：

| 分支 | 染色环境 |
| --- | --- |
| dev | feature1 |
| longTest-1.0 | feature2 |
| longTest-2.0 | feature3 |
| longTest-3.0 | feature4 |

### 4. 提测

目前页面开发统一走[天枢](http://yx.mail.netease.com/dubhe#/versions/list)流程进行提测，因此需要在天枢中把对应的jira任务关联相应的git工程。

![提测](https://yanxuan.nosdn.127.net/static-union/16632959932c4ba4.png)

![关联分支](https://yanxuan.nosdn.127.net/static-union/166329610958cdc8.png)

输入服务名 `yanxuan-ianus-static`，对应的工程名`yanxuan-mall-wap`，选择对应的分支关联（需要git工程创建分支）。完成后点击右上角提测。

![提测信息](https://yanxuan.nosdn.127.net/static-union/1663296418b9e61f.png)

填写对应的提测信息，点击提交，这样完整的一个提测流程就完成了。

## 开发指南

本工程底层基于Fregin研发框架生成，如需了解工程的目录结构、工程配置、配套能力使用等更细粒度的开发指引，请查看
[Fregin研发框架开发指南](http://yxpdc.mail.netease.com/friday/rd-framework/fregin/intro/about.html#%E7%89%B9%E6%80%A7)。

## 业务介绍

## CHANGELOG

## FAQ
