#!/usr/bin/env bash
###
 # @Author: chen<PERSON><PERSON>an <EMAIL>
 # @Date: 2022-11-23 17:13:44
 # @LastEditors: chenxiaopan <EMAIL>
 # @LastEditTime: 2022-11-23 17:13:48
 # @FilePath: /distribution-operation-web/web/changelog.sh
 # @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
### 

print() {
  echo "npm run changelog: $1"
}

conventional-changelog -p angular -i CHANGELOG.md -s -r 0
if [[ -n $(git status --porcelain) ]]; then
    git add CHANGELOG.md && git commit --no-verify -m 'doc: update CHANGELOG.md'
else
    print "Working directory is clean"
fi