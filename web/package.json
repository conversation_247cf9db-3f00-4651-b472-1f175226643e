{"name": "distribution-operation-web", "version": "0.0.1", "private": true, "dependencies": {"@ant-design/icons": "^4.7.0", "@eagler/authorizion": "^2.0.0", "@eagler/commodity-selector-new": "^1.4.3", "@eagler/umc-select-web": "^2.0.0", "@shark/core": "^1.1.3", "@sharkr/components": "^2.6.9", "@sharkr/css": "^2.0.0", "@sharkr/form": "^0.0.27-beta.4", "@sharkr/request": "^1.1.0", "antd": "4.24.10", "postmate": "^1.5.2", "react": "^16.14.0", "react-css-modules": "^4.7.11", "react-dom": "16.14.0", "react-router": "5.3.4", "react-router-dom": "5.3.4"}, "scripts": {"dev": "cross-env MUSE_ENV=dev muses-scripts dev", "build": "cross-env MUSE_ENV=test muses-scripts build", "build:test": "cross-env MUSE_ENV=test muses-scripts build", "build:regression": "cross-env MUSE_ENV=regression muses-scripts build", "build:betayun": "cross-env MUSE_ENV=betayun muses-scripts build", "build:online": "cross-env MUSE_ENV=online muses-scripts build", "lint": "eslint --ext=jsx,ts,tsx src", "lint-fix": "eslint --ext=jsx,ts,tsx src --fix", "eslint": "eslint --ext .js,.jsx,.ts,.tsx ./src > eslint-analyze.log || exit 0", "eslint-stat": "eslint-stat", "readmelint": "readmelint", "changelog": "bash changelog.sh"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@fe-sdk/eslint-plugin-comments": "^1.1.0", "@fe-sdk/eslint-statistics": "latest", "@fe-sdk/readmelint": "latest", "@musesscripts/yanxuan-b": "latest", "@types/jest": "24.0.13", "@types/node": "14.14.31", "@types/react": "^16.14.41", "@types/react-dom": "^16.9.19", "@types/react-router": "5.0.2", "@types/react-router-dom": "^5.3.3", "@vscode-snippets/test-snippets": "0.0.6", "browserslist": "4.6.2", "conventional-changelog-cli": "^2.2.2", "cross-env": "^7.0.3", "http-proxy-middleware": "^2.0.1", "lint-staged": "^10.1.2", "typescript": "^4.4.3"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix"]}}