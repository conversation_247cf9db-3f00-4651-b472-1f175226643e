{"code": 200, "data": {"paginationVO": {"page": 1, "size": 10, "total": 97, "totalPage": 82}, "result": [{"buId": 71, "buName": "服配", "name": "羊毛围巾", "pictureStatisticsList": [{"pictureList": [{"baseId": 83, "channelProductId": "渠道商品 sku id", "createTime": 1750337625293, "createUser": "", "picName": "consectetur irure proident non", "picSize": 84, "picType": 0, "picUrl": "ut qui", "skuId": 90, "spuId": 99, "teamId": 1, "updateTime": 1753177866905, "updateUser": "Ut elit"}, {"baseId": 16, "channelProductId": "渠道商品 sku id", "createTime": 1749964864137, "createUser": "", "picName": "ullamco do sunt sint velit", "picSize": 10, "picType": 0, "picUrl": "in deserunt do", "skuId": 52, "spuId": 86, "teamId": 2, "updateTime": 1749073051385, "updateUser": "consequat nostrud incididunt anim aliquip"}], "teamId": 3, "teamName": "京东", "totalCount": 15}, {"pictureList": [{"baseId": 45, "channelProductId": "渠道商品 sku id", "createTime": 1749323783408, "createUser": "", "picName": "minim", "picSize": 64, "picType": 0, "picUrl": "dolor tempor esse", "skuId": 50, "spuId": 53, "teamId": 13, "updateTime": 1749662099629, "updateUser": "laboris"}], "teamId": 2, "teamName": "淘宝", "totalCount": 17}], "secondBuId": 75, "secondBuName": "服装配件部", "skuPicCount": 3, "spuId": 12345678, "updateTime": 1752853433469, "updator": "hhh"}, {"buId": 62, "buName": "sit ul<PERSON>co", "name": "dolore commodo aute laborum", "pictureStatisticsList": [{"pictureList": [{"baseId": 29, "channelProductId": "渠道商品 sku id", "createTime": 1749914486594, "createUser": "", "picName": "non incididunt in magna", "picSize": 40, "picType": 0, "picUrl": "sed qui eiusmod", "skuId": 36, "spuId": 82, "teamId": 47, "updateTime": 1753786026828, "updateUser": "laboris sunt"}, {"baseId": 98, "channelProductId": "渠道商品 sku id", "createTime": 1750478040583, "createUser": "", "picName": "consequat e<PERSON><PERSON>d", "picSize": 73, "picType": 0, "picUrl": "nostrud labore ex", "skuId": 23, "spuId": 44, "teamId": 4, "updateTime": 1748742155959, "updateUser": "sit"}, {"baseId": 37, "channelProductId": "渠道商品 sku id", "createTime": 1753753990265, "createUser": "", "picName": "quis labore", "picSize": 1, "picType": 0, "picUrl": "amet nulla nostrud ul<PERSON>co", "skuId": 57, "spuId": 72, "teamId": 93, "updateTime": 1749409528397, "updateUser": "qui occaecat velit eiusmod"}, {"baseId": 2, "channelProductId": "渠道商品 sku id", "createTime": 1751157417929, "createUser": "", "picName": "adipisicing do", "picSize": 91, "picType": 0, "picUrl": "incididunt et enim eu Lorem", "skuId": 75, "spuId": 48, "teamId": 67, "updateTime": 1753799398596, "updateUser": "amet pariatur nulla est"}], "teamId": 71, "teamName": "nisi elit amet Ut velit", "totalCount": 44}, {"pictureList": [{"baseId": 42, "channelProductId": "渠道商品 sku id", "createTime": 1753580902450, "createUser": "", "picName": "occaecat sed culpa", "picSize": 38, "picType": 0, "picUrl": "incididunt sunt sint aute", "skuId": 63, "spuId": 85, "teamId": 77, "updateTime": 1752882525991, "updateUser": "consectetur ut aliquip"}, {"baseId": 21, "channelProductId": "渠道商品 sku id", "createTime": 1749576534273, "createUser": "", "picName": "sint fugiat", "picSize": 77, "picType": 0, "picUrl": "in qui", "skuId": 52, "spuId": 41, "teamId": 14, "updateTime": 1750656100265, "updateUser": "non"}, {"baseId": 32, "channelProductId": "渠道商品 sku id", "createTime": 1749925994187, "createUser": "", "picName": "labore culpa ea", "picSize": 21, "picType": 0, "picUrl": "ea occaecat laborum Ut id", "skuId": 15, "spuId": 91, "teamId": 22, "updateTime": 1752028652824, "updateUser": "esse ipsum aliquip dolore sunt"}], "teamId": 36, "teamName": "ex labore", "totalCount": 31}, {"pictureList": [{"baseId": 58, "channelProductId": "渠道商品 sku id", "createTime": 1750580573025, "createUser": "", "picName": "ad aute ex reprehenderit Ut", "picSize": 35, "picType": 0, "picUrl": "Duis pariatur veniam", "skuId": 27, "spuId": 12, "teamId": 76, "updateTime": 1750909969639, "updateUser": "minim"}, {"baseId": 91, "channelProductId": "渠道商品 sku id", "createTime": 1749224619637, "createUser": "", "picName": "Duis mollit qui", "picSize": 10, "picType": 0, "picUrl": "fugiat", "skuId": 76, "spuId": 35, "teamId": 82, "updateTime": 1752446644480, "updateUser": "reprehenderit commodo anim quis Ut"}, {"baseId": 95, "channelProductId": "渠道商品 sku id", "createTime": 1749310085410, "createUser": "", "picName": "nostrud anim ad Lorem", "picSize": 92, "picType": 0, "picUrl": "aute", "skuId": 21, "spuId": 2, "teamId": 94, "updateTime": 1752382457926, "updateUser": "velit"}, {"baseId": 15, "channelProductId": "渠道商品 sku id", "createTime": 1752263741371, "createUser": "", "picName": "in", "picSize": 85, "picType": 0, "picUrl": "Lorem et cillum", "skuId": 14, "spuId": 66, "teamId": 59, "updateTime": 1750704969685, "updateUser": "tempor fugiat"}], "teamId": 18, "teamName": "in qui", "totalCount": 30}], "secondBuId": 94, "secondBuName": "sint do deserunt sed quis", "skuPicCount": 11, "spuId": 10, "updateTime": 1753478768288, "updator": ""}, {"buId": 92, "buName": "quis", "name": "deserunt", "pictureStatisticsList": [{"pictureList": [{"baseId": 99, "channelProductId": "渠道商品 sku id", "createTime": 1753173196617, "createUser": "", "picName": "in", "picSize": 76, "picType": 0, "picUrl": "sunt sint velit ipsum", "skuId": 29, "spuId": 22, "teamId": 93, "updateTime": 1751934888433, "updateUser": "reprehenderit consectetur in"}, {"baseId": 68, "channelProductId": "渠道商品 sku id", "createTime": 1753358505696, "createUser": "", "picName": "consectetur sint veniam", "picSize": 71, "picType": 0, "picUrl": "in", "skuId": 26, "spuId": 22, "teamId": 52, "updateTime": 1753177268647, "updateUser": "proident veniam in anim nulla"}], "teamId": 27, "teamName": "labore amet ad dolore nostrud", "totalCount": 63}, {"pictureList": [{"baseId": 61, "channelProductId": "渠道商品 sku id", "createTime": 1749459434481, "createUser": "", "picName": "reprehenderit nisi mollit", "picSize": 46, "picType": 0, "picUrl": "officia minim ea", "skuId": 54, "spuId": 98, "teamId": 20, "updateTime": 1752298187709, "updateUser": "<PERSON><PERSON>"}, {"baseId": 88, "channelProductId": "渠道商品 sku id", "createTime": 1752183263248, "createUser": "", "picName": "pariatur sint adipisicing", "picSize": 28, "picType": 0, "picUrl": "<PERSON><PERSON>", "skuId": 61, "spuId": 2, "teamId": 19, "updateTime": 1749783850934, "updateUser": "nulla quis"}], "teamId": 64, "teamName": "cillum", "totalCount": 2}, {"pictureList": [{"baseId": 62, "channelProductId": "渠道商品 sku id", "createTime": 1749678263762, "createUser": "", "picName": "dolore", "picSize": 80, "picType": 0, "picUrl": "nostrud exercitation", "skuId": 97, "spuId": 89, "teamId": 45, "updateTime": 1751631793215, "updateUser": "dolor nulla cillum"}], "teamId": 24, "teamName": "et consequat velit", "totalCount": 78}, {"pictureList": [{"baseId": 67, "channelProductId": "渠道商品 sku id", "createTime": 1753110954570, "createUser": "", "picName": "laboris consequat", "picSize": 93, "picType": 0, "picUrl": "consectetur Ut", "skuId": 5, "spuId": 32, "teamId": 53, "updateTime": 1750376563541, "updateUser": "in nisi commodo ea amet"}, {"baseId": 84, "channelProductId": "渠道商品 sku id", "createTime": 1753167135155, "createUser": "", "picName": "nulla do consectetur velit culpa", "picSize": 59, "picType": 0, "picUrl": "ex ea occaecat incididunt", "skuId": 51, "spuId": 78, "teamId": 24, "updateTime": 1753642992462, "updateUser": "deserunt"}, {"baseId": 77, "channelProductId": "渠道商品 sku id", "createTime": 1751503538007, "createUser": "", "picName": "occa<PERSON>t", "picSize": 83, "picType": 0, "picUrl": "eius<PERSON>d qui", "skuId": 20, "spuId": 48, "teamId": 35, "updateTime": 1752120543567, "updateUser": "commodo consectetur ullamco"}, {"baseId": 70, "channelProductId": "渠道商品 sku id", "createTime": 1752412998171, "createUser": "", "picName": "incididunt ullam<PERSON> in", "picSize": 93, "picType": 0, "picUrl": "Ut", "skuId": 88, "spuId": 48, "teamId": 62, "updateTime": 1751911810569, "updateUser": "veniam elit"}, {"baseId": 70, "channelProductId": "渠道商品 sku id", "createTime": 1751673647126, "createUser": "", "picName": "consectetur dolore", "picSize": 93, "picType": 0, "picUrl": "deserunt Lorem Excepteur in enim", "skuId": 68, "spuId": 29, "teamId": 76, "updateTime": 1751985326622, "updateUser": "mollit aliqua"}], "teamId": 24, "teamName": "sit", "totalCount": 72}, {"pictureList": [{"baseId": 30, "channelProductId": "渠道商品 sku id", "createTime": 1752984481037, "createUser": "", "picName": "labore sit", "picSize": 7, "picType": 0, "picUrl": "ut eiusmod", "skuId": 0, "spuId": 36, "teamId": 74, "updateTime": 1750518801913, "updateUser": "incididunt Lorem amet nostrud"}, {"baseId": 77, "channelProductId": "渠道商品 sku id", "createTime": 1753617974876, "createUser": "", "picName": "Excepteur ut veniam ullamco", "picSize": 38, "picType": 0, "picUrl": "proident", "skuId": 71, "spuId": 74, "teamId": 22, "updateTime": 1751020776867, "updateUser": "do reprehenderit amet dolor ullamco"}, {"baseId": 29, "channelProductId": "渠道商品 sku id", "createTime": 1750922520920, "createUser": "", "picName": "sunt laborum aliqua minim nulla", "picSize": 16, "picType": 0, "picUrl": "id cillum dolor nisi sint", "skuId": 82, "spuId": 78, "teamId": 89, "updateTime": 1750397849290, "updateUser": "mollit irure"}, {"baseId": 33, "channelProductId": "渠道商品 sku id", "createTime": 1748696232845, "createUser": "", "picName": "cupidatat sunt voluptate commodo laboris", "picSize": 46, "picType": 0, "picUrl": "pariatur fugiat", "skuId": 10, "spuId": 35, "teamId": 17, "updateTime": 1751974265783, "updateUser": "ut sunt incididunt nostrud"}, {"baseId": 45, "channelProductId": "渠道商品 sku id", "createTime": 1752727339656, "createUser": "", "picName": "ut ex tempor et dolore", "picSize": 21, "picType": 0, "picUrl": "nisi in velit laboris culpa", "skuId": 10, "spuId": 47, "teamId": 56, "updateTime": 1750417889342, "updateUser": "officia"}], "teamId": 67, "teamName": "dolore amet fugiat", "totalCount": 70}], "secondBuId": 58, "secondBuName": "dolor", "skuPicCount": 70, "spuId": 70, "updateTime": 1751780880876, "updator": ""}]}, "error": {"description": "", "error": {"description": "The circular reference: `MissaResponseErrorEntity`"}, "errorCode": 36}, "message": "Msg消息，字符串格式，非必须  可供调用方直接对终端用户输出和使用的业务报错信息 业务异常必须，其它非必须，不允许在此存放堆栈信息等无意义信息"}