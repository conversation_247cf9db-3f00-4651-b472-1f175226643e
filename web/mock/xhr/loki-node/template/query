{"code": 200, "data": {"results": [{"name": "测试模板", "id": 2, "code": "test", "description": "333", "thumb": "http://mailshark-test.nos-jd.163yun.com/document/static/753A427B790B7A8F4C68F3880B7FEF40.jpeg", "createTime": 1660119365792, "updateTime": 1660119365792, "git": "https://git.yx.netease.com/", "version": "", "guide": "222", "status": 0, "tags": ["123", "333"], "responseName": "陈鹏威", "responseUid": "<EMAIL>", "productName": "陈鹏威", "productUid": "<EMAIL>", "createName": "陈鹏威", "createUid": "<EMAIL>", "updateName": "陈鹏威", "updateUid": "<EMAIL>"}, {"name": "惊喜开盲盒", "id": 1, "code": "blindbox", "description": "点击开盲盒即可抽奖", "thumb": "https://yanxuan.nosdn.127.net/static-union/1658818830d0db71.png", "createTime": 1657679241017, "updateTime": 1660118142362, "git": "", "version": "", "guide": "", "status": 1, "tags": ["333", "33334", "123", "aaa"], "responseName": "徐杨", "responseUid": "<EMAIL>", "productName": "徐杨", "productUid": "<EMAIL>", "createUid": "", "updateName": "陈鹏威", "updateUid": "<EMAIL>"}], "pagination": {"total": 2, "page": 1, "pageSize": "10"}}, "message": "ok"}