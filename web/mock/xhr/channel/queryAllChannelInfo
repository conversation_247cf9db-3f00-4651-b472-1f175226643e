{"code": "200", "errorCode": null, "data": [{"channelId": 6801505, "channelName": "发票信息为空的渠道", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801518, "channelName": "员工福利-*********", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "operatorPrincipalName": null, "marketPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6801517, "channelName": "zqrPOP", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801516, "channelName": "河南国寿", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801515, "channelName": "结算平台地推出库供货价结算全品统一折扣", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801512, "channelName": "安新网络", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801521, "channelName": "其他-互联网测试-POP", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "operatorPrincipalName": null, "marketPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 3897377, "channelName": "ZBtest渠道", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801532, "channelName": "爱福客", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801529, "channelName": "互联网测试-地推出库", "accessType": 1, "settleType": 0, "reconciliationWay": 0, "accountType": 2, "operatorPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "operatorPrincipalName": null, "marketPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801528, "channelName": "互联网测试-平台自营", "accessType": 1, "settleType": 3, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "operatorPrincipalName": null, "marketPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801479, "channelName": "渠道化回归测试********", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801478, "channelName": "北京人寿", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801475, "channelName": "巴西Americanas_Mundo", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801487, "channelName": "结算平台POP佣金结算无佣金", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 2, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801486, "channelName": "结算平台第三方代销供货价结算全品统一折扣", "accessType": 1, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801483, "channelName": "结算平台第三方代销佣金结算全品统一佣金", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801482, "channelName": "结算平台第三方代销佣金结算无佣金", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801480, "channelName": "重庆搜购", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801495, "channelName": "结算平台地推出库佣金结算按商品分级计算佣金", "accessType": 1, "settleType": 1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801494, "channelName": "结算平台平台自营供货价结算按严选品类计算折扣", "accessType": 0, "settleType": 3, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801493, "channelName": "结算平台平台自营供货价结算无折扣", "accessType": 0, "settleType": 3, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801492, "channelName": "结算平台平台自营佣金结算按严选品类计算佣金", "accessType": 0, "settleType": 3, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3893263, "channelName": "二条1", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 1, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 3, "verifyStatus": 0}, {"channelId": 6801491, "channelName": "特斯拉-市场营销-zqr第三方代销", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6801490, "channelName": "结算平台POP佣金结算按商品分级计算佣金", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 2, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801489, "channelName": "结算平台POP供货价结算按严选品类计算折扣", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3934218, "channelName": "钱报", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801488, "channelName": "结算平台POP佣金结算全品统一佣金", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801499, "channelName": "互联网测试", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "operatorPrincipalName": null, "marketPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801497, "channelName": "结算平台地推出库供货价结算按商品分级计算折扣", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801496, "channelName": "<PERSON><PERSON>", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801447, "channelName": "ZBtest1563413716090", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801446, "channelName": "ZBtest1563413540038", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6907944, "channelName": "llj账户080", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6801444, "channelName": "ZBtest1563357429395", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 3, "verifyStatus": 2}, {"channelId": 6801443, "channelName": "七彩人生", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801442, "channelName": "好易购", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801441, "channelName": "ZBtest1563343131441", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 4, "verifyStatus": 0}, {"channelId": 6801440, "channelName": "ZBtest1562744774816", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801455, "channelName": "青岛汇商", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801454, "channelName": "其他-渠道化回归测试新建渠道1", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801448, "channelName": "vivo", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801457, "channelName": "ZBtest1563961909235", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801456, "channelName": "ZBtest1563961825085", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 3, "verifyStatus": 2}, {"channelId": 6907953, "channelName": "llj账户082", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 4032609, "channelName": "浙江物产", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 4069472, "channelName": "绿森", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 4044893, "channelName": "陈宇昕", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 4063327, "channelName": "1钱包", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801411, "channelName": "鼎麓", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801410, "channelName": "全量回归mq迁移测试渠道", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801409, "channelName": "mq老渠道", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801421, "channelName": "太极网", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801419, "channelName": "文漫考拉图书店", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801418, "channelName": "API测试渠道（考拉预占，勿动）", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801417, "channelName": "酷屏", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801416, "channelName": "招商到家汇", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801429, "channelName": "ZBtest20190708", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801428, "channelName": "Missu2", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6907930, "channelName": "llj账户075", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6801427, "channelName": "智恒", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801425, "channelName": "彩韵", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801424, "channelName": "广垦佳鲜农庄", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801439, "channelName": "ZBtest1562744694337", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801438, "channelName": "ZBtest1562742051159", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801437, "channelName": "ZBtest1562741110077", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801436, "channelName": "ZBtest1562741069543", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3919936, "channelName": "嘉岩供应", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801433, "channelName": "ZBtest1562574589878", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 3, "verifyStatus": 0}, {"channelId": 6801639, "channelName": "多点", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 3, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801638, "channelName": "影客福利", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801637, "channelName": "安徽联谊通信科技有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801636, "channelName": "法迪奥积分商城", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801635, "channelName": "测试流程", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801634, "channelName": "人马互动科技", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801633, "channelName": "北京逸北知道商贸有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801632, "channelName": "招行礼品卡", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801647, "channelName": "车智互联（北京）科技有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801646, "channelName": "长城汽车股份有限公司（欧拉）", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 3, "verifyStatus": 0}, {"channelId": 6801645, "channelName": "山西潞安石圪节智华生物科技有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801644, "channelName": "海峡人力", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801643, "channelName": "亿平方", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801642, "channelName": "欣欣旅游网", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801641, "channelName": "同福汇", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801640, "channelName": "心福利-新", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801655, "channelName": "北京锐思时代科技有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801654, "channelName": "杭州圣斗士科技有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801653, "channelName": "北京智优惠2", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801652, "channelName": "长城汽车股份有限公司（欧拉）2", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801651, "channelName": "华夏", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801650, "channelName": "上汽赛可通", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801649, "channelName": "goapi测试渠道", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801648, "channelName": "易茂", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801663, "channelName": "快手测试", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "operatorPrincipalName": null, "marketPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801661, "channelName": "杭州启健", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801660, "channelName": "吉利福利商城", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801659, "channelName": "快手", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "operatorPrincipalName": null, "marketPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801658, "channelName": "绿城", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801657, "channelName": "礼擎2", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801656, "channelName": "航天万源实业有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3920027, "channelName": "共享库存测试渠道", "accessType": 1, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 3, "verifyStatus": 0}, {"channelId": 6801604, "channelName": "联通电子商城", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801615, "channelName": "北京宅鲜配", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801614, "channelName": "禄品", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801613, "channelName": "OMS测试渠道", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "wb.zhou<PERSON>@mesg.corp.netease.com", "operatorPrincipalName": null, "marketPrincipal": "wb.zhou<PERSON>@mesg.corp.netease.com", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 3917973, "channelName": "瞬为", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801609, "channelName": "其他-Goapi测试专用（勿动）", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6801623, "channelName": "zqrtest06", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801621, "channelName": "北京智优惠", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 3, "verifyStatus": 0}, {"channelId": 6801620, "channelName": "深圳国寿", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801619, "channelName": "享乐吧", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801618, "channelName": "上海源慧 -巧克力", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801616, "channelName": "zqr测试渠道", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801631, "channelName": "招行M站", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801629, "channelName": "小红书POP对接", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6801628, "channelName": "中影云（北京）科技有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801627, "channelName": "云产品", "accessType": 0, "settleType": 3, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801626, "channelName": "上海聂品网络科技有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801625, "channelName": "盒美-中软国际", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801624, "channelName": "江西电影票", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801575, "channelName": "美国独立站_金山仓", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801574, "channelName": "美国独立站_余杭仓", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801573, "channelName": "美国独立站_美国仓", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801577, "channelName": "联通集成", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "operatorPrincipalName": null, "marketPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801588, "channelName": "京东厂直", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "operatorPrincipalName": null, "marketPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6801542, "channelName": "回归测试老数据", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801540, "channelName": "太平", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801536, "channelName": "测试老数据111", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 4008150, "channelName": "ZBregtest", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 3928256, "channelName": "ZBtest103", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 3, "verifyStatus": 1}, {"channelId": 6801564, "channelName": "美国独立站", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908052, "channelName": "llj账户121-续约", "accessType": 0, "settleType": 3, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "wb.daiji<PERSON><PERSON>@mesg.corp.netease.com", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 4063424, "channelName": "上海得仕", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908265, "channelName": "测试形式发票", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908264, "channelName": "测试不查询企业002", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908267, "channelName": "测试变更增值税普票002", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6801773, "channelName": "广东优识", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908258, "channelName": "测试变更增值税普票", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6801772, "channelName": "常州象源信息科技有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 4036913, "channelName": "云联美购", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908261, "channelName": "夏天100-员工福利-测试渠道联系人001", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6801771, "channelName": "企业CRM测试渠道2-分销业务部", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 3, "verifyStatus": 0}, {"channelId": 4043056, "channelName": "测试同步的渠道", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 1, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 3, "verifyStatus": 0}, {"channelId": 6801768, "channelName": "其他-CRM测试渠道1-分销业务部", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 3, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801783, "channelName": "Goapi测试-海外渠道", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801780, "channelName": "Goapi测试-抖音小店", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801778, "channelName": "江苏我趣商贸发展有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801777, "channelName": "中汇豪泰2", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908273, "channelName": "测试变更增值税专票001", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908272, "channelName": "测试变更增值税普票004", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 4059431, "channelName": "hzchenzehe_test_3", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908274, "channelName": "测试变更形式发票001", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6801731, "channelName": "北京百乐惠企业管理服务有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801730, "channelName": "宝利德", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801729, "channelName": "安吉县广播电视网络有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801728, "channelName": "三峡e购", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 4018453, "channelName": "互联网家", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3928338, "channelName": "ZBtest104", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 3, "verifyStatus": 1}, {"channelId": 6801741, "channelName": "北京嗖嗖快跑科技有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801740, "channelName": "西安耕辛广告传播有限", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3950860, "channelName": "Fango360", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801750, "channelName": "北京紫程电子商务有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801749, "channelName": "中银积分系统", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801748, "channelName": "其他-测试007", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908250, "channelName": "满天星10-员工福利-xf测试********-2", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6801747, "channelName": "海外渠道3", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801746, "channelName": "海外渠道2", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "operatorPrincipalName": null, "marketPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6801745, "channelName": "海外渠道1", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "operatorPrincipalName": null, "marketPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801744, "channelName": "北京惠海通科技有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801759, "channelName": "湖南物会网络信息技术有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801758, "channelName": "碧盛2", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801757, "channelName": "南粤2", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801703, "channelName": "关爱通商城", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801702, "channelName": "second全球精选", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801700, "channelName": "长城汽车股份有限公司徐水魏派分公司", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801699, "channelName": "工家云", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801698, "channelName": "流水测试渠道", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801696, "channelName": "安徽百兆", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801711, "channelName": "上海蓝寿", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801710, "channelName": "大汉三通电子商务有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801709, "channelName": "e家银商城", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801708, "channelName": "欧拉（积分商城）", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801707, "channelName": "福建上晴", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801706, "channelName": "咪咕动漫2", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 3, "verifyStatus": 0}, {"channelId": 3989873, "channelName": "测试下新建渠道", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801705, "channelName": "北京政采商贸有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801704, "channelName": "中智关爱通商城", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801719, "channelName": "东福", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3950956, "channelName": "中顺易", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801718, "channelName": "网易雷火科技-上海晨光科力普办公用品有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801717, "channelName": "zqr测试渠道1", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801716, "channelName": "福建互联星空网络科技有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801715, "channelName": "紫金农商行-员工福利", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801714, "channelName": "变更回归测试123", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801713, "channelName": "紫金农商行-集采积分", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801712, "channelName": "北京企团科技有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801727, "channelName": "sptest", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 3, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 3928418, "channelName": "ZBtest105", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 3, "verifyStatus": 1}, {"channelId": 6801725, "channelName": "北京北纬三十度网络科技有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801724, "channelName": "金照林1", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801671, "channelName": "亿合尤迅2", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3987804, "channelName": "动态库存主备测试渠道", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801670, "channelName": "OPPO积分商城", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801669, "channelName": "抖音小店", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "operatorPrincipalName": null, "marketPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6801668, "channelName": "福礼商城", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 1}, {"channelId": 6801666, "channelName": "芒果TV", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801665, "channelName": "亿合尤讯", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 3, "verifyStatus": 0}, {"channelId": 6801664, "channelName": "互联网快手", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "operatorPrincipalName": null, "marketPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801678, "channelName": "锐立影业", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801677, "channelName": "福州邮储银行", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801676, "channelName": "吉利福利商城2", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801675, "channelName": "宁波福味元", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801674, "channelName": "拍一下", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801673, "channelName": "北京虹景数字技术有限公司2", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801672, "channelName": "北京虹景数字技术有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801687, "channelName": "长兴传媒", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801686, "channelName": "房东直卖", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801685, "channelName": "鑫福通", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801684, "channelName": "福礼商城2", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801683, "channelName": "哈弗积分商城", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 3, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801682, "channelName": "回归测试", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801681, "channelName": "爱买买的我", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3989835, "channelName": "ZBTEST111", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 3, "verifyStatus": 1}, {"channelId": 6801695, "channelName": "怀谷数字科技（北京）有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801694, "channelName": "咪咕动漫", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801693, "channelName": "福建望岳", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801692, "channelName": "北京果儿科技有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801691, "channelName": "招财权限隔离", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "operatorPrincipalName": null, "marketPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801690, "channelName": "长城汽车（皮卡）", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801689, "channelName": "亿云通", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801688, "channelName": "咪咕音乐", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908395, "channelName": "Chen账户051", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908394, "channelName": "分销-其他-crm新建渠道测试", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908397, "channelName": "分销-员工福利-测试渠道4", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908396, "channelName": "Chen账户052", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908399, "channelName": "分销-市场营销-北京据常优科技有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908385, "channelName": "分销-员工福利-分销-招财3", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908387, "channelName": "分销-市场营销-分销-招财4t", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908386, "channelName": "分销-员工福利-分销-招财4", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 3946928, "channelName": "考拉测试账号DBC", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 1, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908389, "channelName": "分销-员工福利-分销-招财6test2136", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908388, "channelName": "分销-市场营销-分销-招财5", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908390, "channelName": "分销-员工福利-分销-招财testedit", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 2450, "channelName": "代销的execl", "accessType": 1, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 3910061, "channelName": "ZBtest9", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 3, "verifyStatus": 1}, {"channelId": 6908415, "channelName": "分销-员工福利-蜂鸟创新科技", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908414, "channelName": "hhhhh-员工福利-fq账户05", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908401, "channelName": "hhhhh-员工福利-测试004", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908400, "channelName": "分销-其他-crm新建渠道测试0422", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 4034978, "channelName": "京东食品旗舰店", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908363, "channelName": "分销-企业crm-分销-第三方代销", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 2467, "channelName": "破皮api", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 2, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6908367, "channelName": "测试接口数据隔离", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "wb.chen<PERSON><PERSON><PERSON>@mesg.corp.netease.com", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908355, "channelName": "分销-市场营销-企业crm-分销-pop", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908358, "channelName": "分销程相鸣测-员工福利-企业crm-分销-预付款", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908376, "channelName": "分销-员工福利-crm-fx-pop1", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 4041097, "channelName": "翼云", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 4008328, "channelName": "宝龙", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908383, "channelName": "分销-员工福利-分销-招财1", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 3983754, "channelName": "四喜", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908369, "channelName": "Chen账户048", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908368, "channelName": "分销-员工福利-企业crm-分销-非预付款", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908371, "channelName": "Chen账户050", "accessType": 0, "settleType": 3, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908370, "channelName": "<PERSON>账户049", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908374, "channelName": "分销-员工福利-分销测试专用_test1", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 4065787, "channelName": "测试泰迪002", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 1, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3910132, "channelName": "徽酒", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908351, "channelName": "ctest-员工福利-xf测试033001", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 4067813, "channelName": "观荣", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3989985, "channelName": "天猫网易严选旗舰店", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 4010463, "channelName": "至尚", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 4008410, "channelName": "API对接模式", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 4055511, "channelName": "网聚天下", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3920343, "channelName": "好享购物", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3926487, "channelName": "新锐营", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3994066, "channelName": "ZBtest日流水1", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 3985858, "channelName": "中烟", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3928517, "channelName": "考拉API对接testaaa", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 3910201, "channelName": "ZBtesthello", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 1, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 4008501, "channelName": "人工维护模式", "accessType": 1, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3883566, "channelName": "京东b", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 3, "verifyStatus": 0}, {"channelId": 6900351, "channelName": "llj账户044", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908533, "channelName": "分销-员工福利-考拉友品", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 3928605, "channelName": "雷琨测试", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3912194, "channelName": "上汽", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908460, "channelName": "分销-积分兑换-南网积分商城", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908463, "channelName": "lsntest01-员工福利-7/12", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 4026996, "channelName": "ZBtest预付款优化", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 3, "verifyStatus": 0}, {"channelId": 6908451, "channelName": "分销-其他-小码科技", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 3926647, "channelName": "渠道名修改", "accessType": 1, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3928674, "channelName": "三朵小花", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908425, "channelName": "分销-员工福利-zml代销", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 4057692, "channelName": "火星选品", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908427, "channelName": "分销-员工福利-杭州礼想2", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908426, "channelName": "分销-员工福利-杭州惠合信息科技有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908431, "channelName": "分销-员工福利-杭州礼想3", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908419, "channelName": "分销-员工福利-pddcc", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 4065874, "channelName": "慧家优选", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3934802, "channelName": "云中鹤", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 4037196, "channelName": "测试渠道变更", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 3, "verifyStatus": 0}, {"channelId": 4055625, "channelName": "恒之源", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908447, "channelName": "分销-其他-北京紫程电子商务有限公司渠道2", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908446, "channelName": "分销-员工福利-苏州门泊", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908434, "channelName": "yanxuan1072-员工福利-yxtest107201", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908436, "channelName": "分销-员工福利-分销测试1001", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6200030, "channelName": "小笨猪", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200031, "channelName": "折扣更新bug修复渠道", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908648, "channelName": "分销-员工福利-蜂助手礼品卡", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6200028, "channelName": "ZBtest2018413渠道", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200029, "channelName": "测试23213", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 3, "verifyStatus": 0}, {"channelId": 6908650, "channelName": "分销-员工福利-文鱼联登分销", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6200026, "channelName": "ZBtest20184126", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908653, "channelName": "网易（杭州）-积分兑换-渠道归属修改1", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6200027, "channelName": "普迪斯", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908652, "channelName": "分销-员工福利-润泽汇1", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6200024, "channelName": "ZBtest2018412t", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200025, "channelName": "ZBtest2018412t1", "accessType": 1, "settleType": 1, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200022, "channelName": "申合信", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200023, "channelName": "21CN", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200020, "channelName": "华润通", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908643, "channelName": "分销-员工福利-阅动", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6200021, "channelName": "爱关怀", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200018, "channelName": "飞象企服", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200019, "channelName": "走一口田", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200016, "channelName": "礼拍档", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908647, "channelName": "今日头条宠物放心购", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6200017, "channelName": "淘略数据", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908646, "channelName": "分销-员工福利-上海旭弘科技", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6200014, "channelName": "红喇叭", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200015, "channelName": "哆吧商城", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908664, "channelName": "总监测试客户-员工福利-行业总监审核测试--002", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6200012, "channelName": "江西腾成", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908667, "channelName": "客户-部门隔-线下零售-OA审核节点优化1", "accessType": 0, "settleType": 3, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6200013, "channelName": "冠群卓凡", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200010, "channelName": "买买提", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200011, "channelName": "京东母婴店", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6200008, "channelName": "苏宁服饰店", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908671, "channelName": "中国建设银行-员工福利-黄玉琛测试行业总监1", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6200009, "channelName": "苏宁综合店", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6200007, "channelName": "共享库存回归渠道", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908659, "channelName": "分销-员工福利-农行M站x微能3", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6200005, "channelName": "非草稿渠道", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908658, "channelName": "分销-员工福利-分乐科技", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6200002, "channelName": "回归数据库增加渠道测试", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200003, "channelName": "良家生活", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3910309, "channelName": "hzchenzehe_test_1", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 2, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 3, "verifyStatus": 0}, {"channelId": 6200000, "channelName": "数据库自增测试渠道", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908663, "channelName": "分销-员工福利-淘宝特价", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6200001, "channelName": "网易聚玩", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200062, "channelName": "水象分期", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908617, "channelName": "分销-员工福利-顾家积分商城", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6200063, "channelName": "北京石油", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908619, "channelName": "分销-员工福利-江苏华福普惠科技发展有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6200061, "channelName": "奥卡中信", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908618, "channelName": "分销-员工福利-龙卉", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6200058, "channelName": "南粤", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908621, "channelName": "黄玉琛测试1", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 4035224, "channelName": "三维度", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200059, "channelName": "荣信天下", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908620, "channelName": "特斯拉-积分兑换-商机认领阶段变更", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6200056, "channelName": "蓝喜", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908623, "channelName": "分销-员工福利-饿了么", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6200057, "channelName": "国东", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908622, "channelName": "分销-员工福利-深圳兆日科技股份有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6200054, "channelName": "雷钰", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908609, "channelName": "分销-员工福利-小鹅拼拼", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6200055, "channelName": "ZBAPI专用渠道", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200053, "channelName": "国寿", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908610, "channelName": "浙江网新恒天-积分兑换-测试账户续约变更返点变更", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6200050, "channelName": "API接口测试(勿动)", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "wb.zhou<PERSON>@mesg.corp.netease.com", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6908613, "channelName": "分销-员工福利-中银对接2", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6200051, "channelName": "通联", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908612, "channelName": "云音乐京东POP", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 2, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6200048, "channelName": "景真", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200049, "channelName": "琛巍", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200046, "channelName": "供销社", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200047, "channelName": "城市海报", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200044, "channelName": "政采云", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 4008590, "channelName": "测试动态库存", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200045, "channelName": "无锡市民卡", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200042, "channelName": "支付通", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200040, "channelName": "新影通", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908639, "channelName": "黄玉琛测试权-积分兑换-黄玉琛测试权限隔离采购账户1-大客户", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 2, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6200041, "channelName": "5条", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 3, "verifyStatus": 0}, {"channelId": 6200038, "channelName": "回归测试_ZB", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200039, "channelName": "至惠", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908624, "channelName": "分销-员工福利-深圳书城网络科技有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6200036, "channelName": "汉兆", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200037, "channelName": "衡翮", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908629, "channelName": "分销-员工福利-震坤行", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6200035, "channelName": "舒码科技", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908628, "channelName": "谷歌信息技术-市场营销-合同到期续约提醒发送2", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6200032, "channelName": "铱澜", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908631, "channelName": "网易（杭州）-积分兑换-权限隔离-账户立项2", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6200033, "channelName": "帆恒", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908630, "channelName": "网易（杭州）-积分兑换-权限隔离-账户立项", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 4059902, "channelName": "银鲷", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908591, "channelName": "XH福礼商城5-员工福利-test014", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908590, "channelName": "分销-员工福利-上海新大陆翼码信息科技股份有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908577, "channelName": "分销-员工福利-测试006", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908576, "channelName": "分销-员工福利-test03", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908579, "channelName": "分销-员工福利-test04", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908581, "channelName": "分销-员工福利-测试009", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908580, "channelName": "分销-员工福利-测试008", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 4049651, "channelName": "易物网001", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6900390, "channelName": "llj账户059", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908604, "channelName": "山东严选果蔬-积分兑换-测试线下渠道录入-合作详情4", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 2, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908593, "channelName": "分销-员工福利-成都鼎有福", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908592, "channelName": "yxtest1062-市场营销-test015", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 3926759, "channelName": "势远", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908596, "channelName": "分销-员工福利-鼎信直充", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 3920601, "channelName": "嘉佑生活", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 4008671, "channelName": "远行", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 4023006, "channelName": "阳光公采", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 4018905, "channelName": "固守", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908559, "channelName": "分销-员工福利-常州象源信息科技有限公司2", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908558, "channelName": "工行礼品卡", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908544, "channelName": "福礼体验版-市场营销-xf测试**********", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 4047575, "channelName": "渠道测试11", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908547, "channelName": "招财七鱼客服测试", "accessType": 0, "settleType": 3, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908548, "channelName": "分销-员工福利-有赞拓麦", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908551, "channelName": "分销-员工福利-浙江人寿", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908573, "channelName": "分销-员工福利-云音乐-曲库积分兑换商城", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908572, "channelName": "分销-其他-111saas", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 4057802, "channelName": "当当", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908574, "channelName": "分销-员工福利-腾讯惠聚", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908561, "channelName": "分销-员工福利-有赞推手店铺", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908560, "channelName": "分销-员工福利-上海顶诚实业有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908563, "channelName": "分销-员工福利-河南省聚爱数字科技有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908564, "channelName": "分销-员工福利-福礼商城体验版", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908567, "channelName": "zchTest1025001", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908566, "channelName": "分销-员工福利-头号买家渠道（严选昆山生活）", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6200144, "channelName": "瑞卡", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200143, "channelName": "百望富通", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200188, "channelName": "上海莫争", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200187, "channelName": "中和宝", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200184, "channelName": "华夏信财", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200182, "channelName": "京东女装", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200183, "channelName": "厦门万翔", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200162, "channelName": "联满网络", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200094, "channelName": "建行善融", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3924858, "channelName": "一家", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200095, "channelName": "ZB测试渠道", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 3, "verifyStatus": 0}, {"channelId": 6200092, "channelName": "健合", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200093, "channelName": "银讯科技", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200090, "channelName": "恩纵", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 3, "verifyStatus": 0}, {"channelId": 6200091, "channelName": "恩纵网桥", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200088, "channelName": "触点", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200089, "channelName": "严之选", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200086, "channelName": "hzchenzehe_test_4", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200087, "channelName": "小蓦机器人", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200084, "channelName": "京东礼品店", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908707, "channelName": "分销-员工福利-驿宝通车驿宝11", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6200085, "channelName": "嘉兴嘉佑", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200082, "channelName": "这个是一个名字很长很长很长很长很长很长很长很长很长很很的渠道", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3883895, "channelName": "京东d", "accessType": 1, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 3, "verifyStatus": 0}, {"channelId": 6200079, "channelName": "API接口异常测试(勿动)", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6200072, "channelName": "云南中烟", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 1, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200070, "channelName": "南京飞翰", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200071, "channelName": "有哲", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200068, "channelName": "尚品宅配", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200069, "channelName": "微能科技", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200067, "channelName": "众享福利", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200064, "channelName": "盖得", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200065, "channelName": "聚聚", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200124, "channelName": "山东木禾", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908683, "channelName": "分销-员工福利-兴业上海", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 4059998, "channelName": "掌聚", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200122, "channelName": "百礼汇", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200123, "channelName": "京东聚玩", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908684, "channelName": "分销-员工福利-苏州纵缤", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6200120, "channelName": "瑞祥全球购1", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6200121, "channelName": "山西老乡", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908672, "channelName": "中国建设银行-市场营销-黄玉琛测试行业总监2", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 4047703, "channelName": "渠道同步测试1", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200117, "channelName": "智造", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200114, "channelName": "爆米铺", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200115, "channelName": "测试1122", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200112, "channelName": "太平洋", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200113, "channelName": "中电", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200110, "channelName": "中经", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200111, "channelName": "沃银企服", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908696, "channelName": "抖音家居-其他-分销抖音家居店", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6200108, "channelName": "综保购", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200109, "channelName": "江苏万邦", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200106, "channelName": "云音乐商城", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200107, "channelName": "云音乐天猫", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200104, "channelName": "优礼", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200105, "channelName": "畅由联盟", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200102, "channelName": "京东春风点", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200103, "channelName": "纯甄", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908688, "channelName": "分销-员工福利-杭州朗和科技有限公司FX", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 4019015, "channelName": "ZBtest999", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6200100, "channelName": "回归测试新建渠道推送oms", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 3, "verifyStatus": 0}, {"channelId": 6908691, "channelName": "分销-员工福利-惠州市威元科技有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6200101, "channelName": "泰华寿悦", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200098, "channelName": "开发专用渠道四", "accessType": 1, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3994433, "channelName": "移动和包", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 4023104, "channelName": "优投金服", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200099, "channelName": "开发专用五", "accessType": 1, "settleType": 1, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200097, "channelName": "即刻开始", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6800359, "channelName": "as_已禁用编辑信息后待审核_POP_品类", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 4, "verifyStatus": 1}, {"channelId": 6800358, "channelName": "as_已禁用_代销", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6800357, "channelName": "as_已启用提交禁用待审核_其他_分级", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 2, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 3, "verifyStatus": 0}, {"channelId": 6800356, "channelName": "as_启用编辑待审核_POP_品类", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 1}, {"channelId": 6800360, "channelName": "as_禁用提交启用待审核_其他_分级", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 2, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 4, "verifyStatus": 1}, {"channelId": 6800370, "channelName": "京东YESSIGN", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 4047787, "channelName": "中信银行信E购", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3920803, "channelName": "陕西电视", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 2, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6800378, "channelName": "淘宝旗舰", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 4029346, "channelName": "来就送", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6800326, "channelName": "yessing天猫旗舰店", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6800325, "channelName": "武汉严之选-线下门店代销", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6800324, "channelName": "京东自营严选", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6800335, "channelName": "薪起程", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6800334, "channelName": "易诚互动", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6800333, "channelName": "苔米", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6800332, "channelName": "京东家电", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6800331, "channelName": "优加优品", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6800330, "channelName": "泰福利", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6800329, "channelName": "奇遇俱乐部", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6800328, "channelName": "云简", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6800343, "channelName": "杭州双城", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3982221, "channelName": "京东服饰旗舰店", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6800342, "channelName": "河南乐善", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3986317, "channelName": "ZB退款预付款", "accessType": 1, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "operatorPrincipalName": null, "marketPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6800341, "channelName": "厦门小依", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6800340, "channelName": "yessing淘宝自营店", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6800339, "channelName": "杭研区块链", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6800338, "channelName": "淄博天甲", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3916686, "channelName": "ZBtest10", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 3, "verifyStatus": 0}, {"channelId": 6800337, "channelName": "民商智惠", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6800336, "channelName": "静客", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6800351, "channelName": "as_已启用_其他_分级", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 2, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 3, "verifyStatus": 0}, {"channelId": 6800347, "channelName": "华录", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3920774, "channelName": "酷开", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 2, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6800346, "channelName": "睿远", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6800345, "channelName": "凡普金科", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 4047871, "channelName": "渠道同步测试2", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 4056056, "channelName": "至特", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 4037620, "channelName": "渠道变更测试1", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 3, "verifyStatus": 0}, {"channelId": 6200210, "channelName": "花生有信", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200211, "channelName": "Look", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200208, "channelName": "环球礼品", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200209, "channelName": "壳牌", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6200202, "channelName": "暴雪", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908852, "channelName": "分销-员工福利-平安普惠金融", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908801, "channelName": "审核流测试04-审核流测试04", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 1003, "channelName": "考拉", "accessType": 1, "settleType": 0, "reconciliationWay": 1, "accountType": 3, "operatorPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 3924937, "channelName": "天然", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6800287, "channelName": "京东智造家居自营店", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6800284, "channelName": "京东网易智造家电自营旗舰店", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6800283, "channelName": "京东自营春风", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6800281, "channelName": "京东智造数码自营店", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 4060099, "channelName": "博影世纪", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3884090, "channelName": "京东5", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 3, "verifyStatus": 0}, {"channelId": 1025, "channelName": "奥飞娱乐-其他-京东", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909024, "channelName": "分销-员工福利-北京影化科技有限公司聚优福利", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909026, "channelName": "ht-市场营销-周一变更续约01", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 4011059, "channelName": "卓望", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6909049, "channelName": "杭州银行-员工福利-端午福利陈竹铃测试", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909050, "channelName": "网易雷火科技-积分兑换-验收使用2", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909052, "channelName": "分销-员工福利-悠福fx", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909054, "channelName": "网易雷火科技-积分兑换-分销测试", "accessType": 0, "settleType": -1, "reconciliationWay": 1, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 4068388, "channelName": "共享库存新建渠道", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3925031, "channelName": "预付款测试渠道1", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6909044, "channelName": "分销-员工福利-苏州农商银行fx", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909017, "channelName": "ht-积分兑换-变更041702", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909019, "channelName": "ht-线下零售-变更041703", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909020, "channelName": "网易雷火科技-线下零售-xh测试商机02", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909023, "channelName": "ht-积分兑换-变更周一01", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 3986434, "channelName": "逸通", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3996795, "channelName": "石油生活网", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908985, "channelName": "分销-员工福利-e家银2", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 3986538, "channelName": "ZB预付款退货1", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 3992679, "channelName": "礼舍", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 4041824, "channelName": "招财运营使用渠道", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6908980, "channelName": "分销-员工福利-测试新建渠道1", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6908982, "channelName": "分销-员工福利-上海攸吉信息技术有限公司1", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 3168, "channelName": "测试", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 4, "verifyStatus": 0}, {"channelId": 3918926, "channelName": "ZBtest100", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 3, "verifyStatus": 1}, {"channelId": 6909160, "channelName": "分销-员工福利-京东宠物分销", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909152, "channelName": "分销-员工福利-西安远眺网络科技有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909154, "channelName": "分销-员工福利-拼多多-云音乐商城", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909158, "channelName": "分销-员工福利-江苏出国人员物资", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909176, "channelName": "分销-员工福利-网易春风微信官方商城", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909173, "channelName": "广发银行-积分兑换-积分商城(勿动)", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909175, "channelName": "分销-员工福利-北京博奥维科技发展有限公司2", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909129, "channelName": "网易雷火科技-渠道奖励-测试履约服务变更2", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909122, "channelName": "分销-员工福利-长城欧拉商城", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909125, "channelName": "分销-员工福利-今日头条宠物放心购", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909124, "channelName": "分销-员工福利-同期声网络科技扬州有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 3918997, "channelName": "礼擎", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6909127, "channelName": "网易雷火科技-积分兑换-测试履约服务变更1", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909126, "channelName": "网易雷火科技-积分兑换-Goapi测试预付款渠道（勿动）", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 3927179, "channelName": "ZBtest101", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 3, "verifyStatus": 1}, {"channelId": 3921033, "channelName": "pop测试", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 1, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6909147, "channelName": "分销-员工福利-拼多多-春风自营旗舰店", "accessType": 0, "settleType": -1, "reconciliationWay": 1, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909149, "channelName": "分销-员工福利-长城哈弗商城", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909097, "channelName": "中信杭分-员工福利-中信杭分", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909099, "channelName": "分销-员工福利-世奥倍特2", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909103, "channelName": "分销-员工福利-世奥倍特3", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909089, "channelName": "分销-员工福利-拼多多家居生活", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 3933423, "channelName": "拿米", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6909116, "channelName": "ht-市场营销-0519001", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909118, "channelName": "分销-员工福利-玖优创分销", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909106, "channelName": "ht-积分兑换-BBBB", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909108, "channelName": "杭州银行-市场营销-陈竹铃测试0516", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 3982558, "channelName": "还呗", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3925214, "channelName": "美好绿城", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3921106, "channelName": "cmss", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 2, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6909056, "channelName": "xf测试客户-市场营销-xf商机********-1", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909059, "channelName": "网易雷火科技-线下零售-仓配工作台测试", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909058, "channelName": "ht-员工福利-**********", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909061, "channelName": "网易雷火科技-线下零售-履约测试", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909080, "channelName": "杭州数爆-积分兑换-商机认领测试", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909083, "channelName": "分销-员工福利-杭州银行", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 4023497, "channelName": "信通开铭", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 5364, "channelName": "石真收不到邮件", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 1, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 3, "verifyStatus": 0}, {"channelId": 6909084, "channelName": "分销-员工福利-杭州银行客户使用", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909075, "channelName": "网易雷火科技-办公用品采购-变更测试2", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 3988679, "channelName": "网店管家", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6909079, "channelName": "ht-积分兑换-分销0426", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909288, "channelName": "xy测试客户-员工福利-测试zdyys", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909280, "channelName": "网易云音乐-市场营销-yhrtest福利VVVVVV", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909283, "channelName": "深圳富德人寿-员工福利-测试0620 2", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 2884917, "channelName": "入住日期为必填", "accessType": 1, "settleType": 0, "reconciliationWay": 1, "accountType": 1, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 2}, {"channelId": 6909296, "channelName": "黄玉琛测试权-员工福利-啊啊", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909299, "channelName": "奥飞娱乐-渠道奖励-测试回归商机072601", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909300, "channelName": "测试回归账户0726", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 3927332, "channelName": "预付365", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6909302, "channelName": "杭州银行-积分兑换-验收测试0726-1", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6800707, "channelName": "欣欣测试", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 3, "verifyStatus": 0}, {"channelId": 6909261, "channelName": "xy测试客户-市场营销-测试一下", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6800706, "channelName": "czy测试5", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6909253, "channelName": "xy测试客户-积分兑换-验证负责人", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909273, "channelName": "杭州百事可乐-市场营销-测试商机072201", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909277, "channelName": "xy测试客户-渠道奖励-测试变更履约001", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909276, "channelName": "xy测试客户-市场营销-测试变更履约", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909264, "channelName": "chy测试客户2-市场营销-xy测试一下哈", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 2, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909266, "channelName": "可口可乐-线下零售-测试商机072102", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909224, "channelName": "测试-市场营销-测试商机071801", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6800675, "channelName": "as_20180918007", "accessType": 1, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6909229, "channelName": "测试-线下零售-测试商机071802", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909228, "channelName": "分销-员工福利-民生银行", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6800673, "channelName": "as_20180918003", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6909230, "channelName": "可口可乐-积分兑换-测试商机071803", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 4060533, "channelName": "精明购", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6800695, "channelName": "as test", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6800703, "channelName": "as__test_002", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6909235, "channelName": "分销-员工福利-荣数勿改", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909237, "channelName": "chy测试客户2-软装采购-人力资源-福利44", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909239, "channelName": "分销-员工福利-北京好生活勿改", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909238, "channelName": "xy测试客户-市场营销-人力资源-福利01", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909193, "channelName": "分销-员工福利-鲸灵", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 3919192, "channelName": "亚马逊POP", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6909194, "channelName": "测试-线下零售-测试商机070702", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909197, "channelName": "分销-员工福利-渠道测试12", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6800640, "channelName": "czy测试2", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6909189, "channelName": "分销-员工福利-春风自营旗舰店2", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909190, "channelName": "分销-员工福利-网易春风微信官方商城2", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909213, "channelName": "广发银行-员工福利-xzy测试********", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6800671, "channelName": "as_20180918001", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 3, "verifyStatus": 0}, {"channelId": 3886401, "channelName": "开发专用渠道二", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6909205, "channelName": "测试-渠道奖励-测试商机071203", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909207, "channelName": "分销-员工福利-北京指尖无限勿改", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909206, "channelName": "分销-员工福利-优加惠品勿改", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 5507, "channelName": "test1931", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 2, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 3, "verifyStatus": 0}, {"channelId": 3462, "channelName": "梧桐细雨商城", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 1415, "channelName": "代销", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 3919272, "channelName": "欲购", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3880353, "channelName": "开发专用渠道", "accessType": 1, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 3919262, "channelName": "招财测试1", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6909376, "channelName": "分销-员工福利-长江三峡（成都）电子商务有限公司", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 1450, "channelName": "二更excelzzz", "accessType": 1, "settleType": 1, "reconciliationWay": 1, "accountType": 3, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6800843, "channelName": "test_20181011001", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 4, "verifyStatus": 0}, {"channelId": 6800842, "channelName": "test_20181010001", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 4, "verifyStatus": 0}, {"channelId": 4056460, "channelName": "蓝火", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 4066698, "channelName": "测试泰迪", "accessType": 1, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3927438, "channelName": "ZBtest102", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 3, "verifyStatus": 0}, {"channelId": 6909352, "channelName": "xy测试客户-员工福利-权限隔离测试一下", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 1474, "channelName": "破皮", "accessType": 1, "settleType": 0, "reconciliationWay": 1, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6909355, "channelName": "xy测试客户-员工福利-回归测试运营负责人", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 3909119, "channelName": "ZBtest1", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 4050425, "channelName": "lee_test_3", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 1, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6909359, "channelName": "分销-员工福利-瑞祥全球购勿改", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6800814, "channelName": "分销压测渠道_test1", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6800813, "channelName": "其他-test_20180929_002", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 1}, {"channelId": 6909349, "channelName": "xy测试客户-积分兑换-xy测试信用代码", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909369, "channelName": "分销-员工福利-咪咕音乐勿改", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6800822, "channelName": "其他-test_20180929_004", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 3993070, "channelName": "ZBtest日流水", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 2, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909371, "channelName": "xy测试客户-线下零售-0823测试", "accessType": 0, "settleType": -1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909370, "channelName": "xy测试客户-市场营销-测试***********", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 4056553, "channelName": "易陛电子商务", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6800819, "channelName": "test_20180929_003", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6909373, "channelName": "xy测试客户-线下零售-xy测试0823", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909375, "channelName": "分销-员工福利-国信紫晶勿改", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 4007402, "channelName": "上海乐辅", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6909374, "channelName": "chy测试客户-积分兑换-xy测试0823002", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6800831, "channelName": "test_20180929_007", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 3, "verifyStatus": 2}, {"channelId": 6800828, "channelName": "test_20180929_006", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 1}, {"channelId": 6800827, "channelName": "test_1003_001", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6800826, "channelName": "其他-test_20180929_005", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6909364, "channelName": "chy测试客户-员工福利-测试价格结算政策", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 2, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909320, "channelName": "xy测试客户-积分兑换-测试权限隔离", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 3927512, "channelName": "测试*********", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 4068827, "channelName": "共享库存新建渠道1", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 3, "verifyStatus": 0}, {"channelId": 6909324, "channelName": "分销-员工福利-哈弗积分商城勿改", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909317, "channelName": "分销-员工福利-江西电影票勿改", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909319, "channelName": "分销-员工福利-快团团", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909318, "channelName": "奥飞娱乐-线下零售-测试商机072603", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6800789, "channelName": "其他-test_20180928_004", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 4, "verifyStatus": 0}, {"channelId": 6909339, "channelName": "安抚-市场营销-测试工单对接模式", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 4060617, "channelName": "灵讯时空", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6800787, "channelName": "其他-test_20180928_003", "accessType": 1, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 3, "verifyStatus": 2}, {"channelId": 4029892, "channelName": "因特帕克万通", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6909331, "channelName": "xy测试客户-市场营销-分销变更履约", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909335, "channelName": "分销-员工福利-东福", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 2, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6909334, "channelName": "xy测试客户-市场营销-测试筛选项", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6800999, "channelName": "test_201811220001", "accessType": 1, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 4, "verifyStatus": 0}, {"channelId": 4060734, "channelName": "易创天下", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801002, "channelName": "京东宠物店", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3927584, "channelName": "测试**********", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801019, "channelName": "其他-as test1", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6800964, "channelName": "网易拼团", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 2, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 4062747, "channelName": "伯乐天成", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6800975, "channelName": "亚米", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6800973, "channelName": "test_20181102003", "accessType": 1, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 1}, {"channelId": 6800971, "channelName": "test_20181102001", "accessType": 1, "settleType": 1, "reconciliationWay": 1, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 1}, {"channelId": 6800969, "channelName": "test_20181031002", "accessType": 1, "settleType": 1, "reconciliationWay": 1, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 1}, {"channelId": 6800980, "channelName": "小朋", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 6800979, "channelName": "test_20181106003", "accessType": 1, "settleType": 0, "reconciliationWay": 1, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6800978, "channelName": "test_20181106002", "accessType": 1, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6800977, "channelName": "test_20181106001", "accessType": 1, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 4068977, "channelName": "特仑苏", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6800950, "channelName": "拼多多旗舰店", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 3927655, "channelName": "ZB预付款回归测试", "accessType": 1, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "operatorPrincipalName": null, "marketPrincipal": "wb.z<PERSON>meng<PERSON>@mesg.corp.netease.com", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 1631, "channelName": "有灵魂二", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 3, "verifyStatus": 0}, {"channelId": 4044360, "channelName": "极科泰克", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3933767, "channelName": "内购网", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 4044460, "channelName": "孩子王", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3919529, "channelName": "ZBtestEXCEL", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 2, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 3933870, "channelName": "网票网", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 4060818, "channelName": "拍店", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 4036224, "channelName": "楚楚推", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801113, "channelName": "test_20190325001", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801063, "channelName": "蜗牛读书纸书测试", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801065, "channelName": "谭聂测试专供", "accessType": 1, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 4038387, "channelName": "ZB同步招财渠道信息1", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 3, "verifyStatus": 0}, {"channelId": 6801076, "channelName": "唯品会", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 4048618, "channelName": "昆山名师", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801083, "channelName": "test_20190322001", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 1}, {"channelId": 4058846, "channelName": "瑞祥全球购", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801034, "channelName": "test_20190124_001", "accessType": 1, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801250, "channelName": "小赢卡贷", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801260, "channelName": "test_20190419_00003", "accessType": 1, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801258, "channelName": "test_20190419_00002", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 1}, {"channelId": 6801257, "channelName": "test_20190419_00001", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 1}, {"channelId": 4058930, "channelName": "网易成都棋牌", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3919660, "channelName": "国美", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6201118, "channelName": "测试1", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 1, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 3, "verifyStatus": 0}, {"channelId": 6201119, "channelName": "北京好生活", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6201116, "channelName": "微品", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3919734, "channelName": "承兴国际", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 4007785, "channelName": "心福利", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 4042593, "channelName": "南方航空", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 4018015, "channelName": "朵拉", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 4067160, "channelName": "瑞辅通", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3909459, "channelName": "ZBtest6", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801166, "channelName": "ZBtest库存回调优化测试渠道", "accessType": 0, "settleType": 3, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 4050775, "channelName": "广西天海", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 4001615, "channelName": "环球黑卡", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6201128, "channelName": "电能成套设备", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6201129, "channelName": "鲜特汇", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6201126, "channelName": "延荐", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6201127, "channelName": "银盛通信", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3940166, "channelName": "麦啦", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3936064, "channelName": "冠瑞商通", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6201122, "channelName": "瑞购", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6201120, "channelName": "test_20180730_1", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 1, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6201121, "channelName": "test_20180730_2", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 2, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801383, "channelName": "网易有福", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801382, "channelName": "新致", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801381, "channelName": "智汇互金", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801379, "channelName": "国寿i购", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 4001721, "channelName": "怡安翰威特", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801391, "channelName": "佣金按分级测试渠道", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801390, "channelName": "折扣按选品类测试渠道", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801389, "channelName": "我在家", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801386, "channelName": "淘宝C店", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801399, "channelName": "新渠道测试mq", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801398, "channelName": "丰宜科技", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801397, "channelName": "无佣金渠道", "accessType": 0, "settleType": 1, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801396, "channelName": "天猫智造", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801395, "channelName": "其他-佣金按选品类", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801394, "channelName": "佣金全品类测试渠道", "accessType": 0, "settleType": 1, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801393, "channelName": "折扣按分级测试渠道", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 6801392, "channelName": "折扣全品类测试渠道", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801407, "channelName": "渠道迁移mq回归测试1", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801406, "channelName": "回归mq迁移渠道", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801405, "channelName": "mq迁移招财同步", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801404, "channelName": "爱驰汽车", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801403, "channelName": "新建渠道mq迁移3", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801401, "channelName": "新建渠道mq迁移测试2", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801400, "channelName": "新建渠道mq消息测试", "accessType": 0, "settleType": 0, "reconciliationWay": 0, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3909528, "channelName": "ZBtest7", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 2, "verifyType": 3, "verifyStatus": 0}, {"channelId": 6801374, "channelName": "北京分贝", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801373, "channelName": "京东智造数码", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "wb.ruancheng<PERSON>@mesg.corp.netease.com", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801372, "channelName": "顾佳琴", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801371, "channelName": "网易游戏", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801370, "channelName": "天猫母婴", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801369, "channelName": "洽客科技", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801315, "channelName": "玩赚积分", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": -1, "verifyStatus": -1}, {"channelId": 3921907, "channelName": "hzchenzehe_test_2", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3919857, "channelName": "伙伴家", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 4065261, "channelName": "测试泰迪渠道", "accessType": 1, "settleType": 1, "reconciliationWay": 1, "accountType": 1, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 6801341, "channelName": "test_20190419_00004", "accessType": 1, "settleType": 1, "reconciliationWay": 1, "accountType": 1, "operatorPrincipal": "<EMAIL>", "operatorPrincipalName": null, "marketPrincipal": "<EMAIL>", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 2, "verifyStatus": 0}, {"channelId": 4032481, "channelName": "南城", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}, {"channelId": 3909597, "channelName": "ZBtest8", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 3, "verifyStatus": 1}, {"channelId": 4040641, "channelName": "尤马", "accessType": 0, "settleType": 0, "reconciliationWay": 1, "accountType": 0, "operatorPrincipal": "", "operatorPrincipalName": null, "marketPrincipal": "", "marketPrincipalName": null, "currentStatus": 1, "verifyType": 1, "verifyStatus": 0}]}