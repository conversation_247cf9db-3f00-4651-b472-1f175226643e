{"code": 200, "data": [{"align": "对齐方式 left-左对齐 center-居中 right-右对齐\n", "color": "字体颜色\n", "content": "内容\n", "ext": "扩展字段\n", "fontFamily": "字体\n", "fontSize": 71, "height": 43, "id": 91, "name": "段烨霖", "sort": 0, "type": 33, "width": 33, "x": 91, "y": 55, "zoomMode": "缩放 Origin-原始大小 Width-宽度指定 Height-高度指定 WidthHeight-宽高指定\n"}, {"align": "对齐方式 left-左对齐 center-居中 right-右对齐\n", "color": "字体颜色\n", "content": "内容\n", "ext": "扩展字段\n", "fontFamily": "字体\n", "fontSize": 27, "height": 26, "id": 38, "name": "吴绍辉", "sort": 0, "type": 16, "width": 36, "x": 33, "y": 78, "zoomMode": "缩放 Origin-原始大小 Width-宽度指定 Height-高度指定 WidthHeight-宽高指定\n"}, {"align": "对齐方式 left-左对齐 center-居中 right-右对齐\n", "color": "字体颜色\n", "content": "内容\n", "ext": "扩展字段\n", "fontFamily": "字体\n", "fontSize": 11, "height": 58, "id": 8, "name": "杜皓轩", "sort": 0, "type": 2, "width": 92, "x": 16, "y": 7, "zoomMode": "缩放 Origin-原始大小 Width-宽度指定 Height-高度指定 WidthHeight-宽高指定\n"}, {"align": "对齐方式 left-左对齐 center-居中 right-右对齐\n", "color": "字体颜色\n", "content": "内容\n", "ext": "扩展字段\n", "fontFamily": "字体\n", "fontSize": 83, "height": 31, "id": 35, "name": "崔展鹏", "sort": 0, "type": 55, "width": 88, "x": 89, "y": 72, "zoomMode": "缩放 Origin-原始大小 Width-宽度指定 Height-高度指定 WidthHeight-宽高指定\n"}, {"align": "对齐方式 left-左对齐 center-居中 right-右对齐\n", "color": "字体颜色\n", "content": "内容\n", "ext": "扩展字段\n", "fontFamily": "字体\n", "fontSize": 69, "height": 81, "id": 38, "name": "秦昊焱", "sort": 0, "type": 13, "width": 47, "x": 49, "y": 65, "zoomMode": "缩放 Origin-原始大小 Width-宽度指定 Height-高度指定 WidthHeight-宽高指定\n"}]}